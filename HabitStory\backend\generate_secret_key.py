#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to generate a secure secret key for JWT tokens.
Run this script to generate a new secret key for production use.
"""

import secrets
import string
import os
from pathlib import Path

def generate_secure_key(length: int = 64) -> str:
    """Generate a cryptographically secure random key."""
    alphabet = string.ascii_letters + string.digits + "!@#$%^&*"
    return ''.join(secrets.choice(alphabet) for _ in range(length))

def main():
    """Generate and display a secure secret key."""
    print("🔐 HabitStory Secret Key Generator")
    print("=" * 40)
    
    # Generate a secure key
    secret_key = generate_secure_key()
    
    print(f"Generated secure secret key:")
    print(f"SECRET_KEY={secret_key}")
    print()
    print("📋 Instructions:")
    print("1. Copy the SECRET_KEY value above")
    print("2. Add it to your production .env file")
    print("3. Never commit this key to version control")
    print("4. Store it securely in your production environment")
    print()
    print("⚠️  Security Notes:")
    print("- This key is used for JWT token signing")
    print("- Keep it secret and secure")
    print("- Use different keys for different environments")
    print("- Rotate keys periodically for enhanced security")

if __name__ == "__main__":
    main()
