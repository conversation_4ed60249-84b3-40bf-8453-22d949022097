{"version": 3, "file": "NotificationsHandlerModule.native.js", "sourceRoot": "", "sources": ["../src/NotificationsHandlerModule.native.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,mBAAmB,EAAE,MAAM,mBAAmB,CAAC;AAIxD,eAAe,mBAAmB,CAA6B,gCAAgC,CAAC,CAAC", "sourcesContent": ["import { requireNativeModule } from 'expo-modules-core';\n\nimport { NotificationsHandlerModule } from './NotificationsHandlerModule.types';\n\nexport default requireNativeModule<NotificationsHandlerModule>('ExpoNotificationsHandlerModule');\n"]}