"""
Module 9: Report Builder Service
Generates final HTML reports using Gemini + Jinja2 with inline SVGs.
"""

import json
import logging
from typing import Dict, List, Any, Optional
from jinja2 import Template, Environment, BaseLoader
import re

from .gemini_client import gemini_client

logger = logging.getLogger(__name__)


class ReportBuilderService:
    """Service for building comprehensive HTML reports."""
    
    def __init__(self):
        self.client = gemini_client
        self.jinja_env = Environment(loader=BaseLoader())
    
    async def build_weekly_report(
        self,
        user_id: str,
        entries: List[Dict[str, Any]],
        stats: Dict[str, Any],
        correlations: Dict[str, Any],
        story: str,
        recommendations: List[Dict[str, Any]],
        questions: List[Dict[str, Any]],
        visualizations: Dict[str, str],
        validation_results: Dict[str, Any],
        user_traits: Dict[str, Any],
        previous_feedback: Optional[str] = None
    ) -> str:
        """
        Build comprehensive weekly HTML report.
        
        Args:
            user_id: User identifier for token tracking
            entries: List of journal entries
            stats: Weekly statistics
            correlations: Correlation analysis
            story: Generated story narrative
            recommendations: Personalized recommendations
            questions: Follow-up questions
            visualizations: SVG visualizations
            validation_results: Data validation results
            user_traits: User personality traits
            previous_feedback: Previous feedback to incorporate
            
        Returns:
            Complete HTML report as string
        """
        try:
            # Create system prompt for report generation
            system_prompt = self._create_report_system_prompt(user_traits, previous_feedback)
            
            # Prepare comprehensive data for report
            report_data = self._prepare_report_data(
                entries, stats, correlations, story, recommendations, 
                questions, visualizations, validation_results
            )
            
            # Generate HTML structure with Gemini
            response = await self.client.call_gemini(
                system=system_prompt,
                user=f"Generate a complete HTML report with this data:\n\n{json.dumps(report_data, indent=2)}",
                temperature=0.7,
                max_tokens=3000,
                user_id=user_id
            )
            
            html_content = response["response"]
            
            # Post-process the HTML to embed SVG visualizations
            final_html = self._embed_visualizations(html_content, visualizations)
            
            # Validate and clean HTML
            final_html = self._validate_and_clean_html(final_html)
            
            return final_html
            
        except Exception as e:
            logger.error(f"Error building weekly report: {e}")
            return self._generate_fallback_report(user_traits, story, recommendations)
    
    def _create_report_system_prompt(
        self, 
        user_traits: Dict[str, Any], 
        previous_feedback: Optional[str] = None
    ) -> str:
        """Create system prompt for HTML report generation."""
        tone = user_traits.get('tone', 'informal')
        style = user_traits.get('style', 'conversational')
        traits = user_traits.get('traits', {})
        
        feedback_context = ""
        if previous_feedback:
            feedback_context = f"\nPREVIOUS FEEDBACK TO INCORPORATE:\n{previous_feedback}\n"
        
        return f"""You are an expert web designer and life coach who creates beautiful, personalized HTML reports for personal growth and habit tracking.

USER COMMUNICATION PROFILE:
- Tone: {tone}
- Style: {style}
- Personality Traits: {json.dumps(traits, indent=2)}

{feedback_context}

HTML REPORT REQUIREMENTS:

1. **Complete HTML5 Document**:
   - Proper DOCTYPE, html, head, and body structure
   - Responsive viewport meta tag
   - All CSS inline within <style> tags in <head>
   - No external dependencies or CDN links

2. **Design System**:
   - Color palette: Primary (#3B82F6), Secondary (#10B981), Accent (#F59E0B), Warning (#EF4444), Success (#059669), Neutral (#6B7280)
   - Typography: Use system fonts (Inter, system-ui, sans-serif)
   - Mobile-first responsive design
   - Clean, modern aesthetic with generous white space
   - Card-based layout with subtle shadows and rounded corners

3. **Content Structure**:
   - **Header Section**: Personalized title, date range, warm greeting
   - **Story Section**: The narrative story in an engaging format
   - **Insights Dashboard**: Key statistics and patterns with visual elements
   - **Visualizations Placeholders**: Use <!--chart0-->, <!--chart1-->, etc. for SVG insertion points
   - **Recommendations Section**: Actionable tips in an organized layout
   - **Reflection Questions**: Thoughtful questions for the upcoming week
   - **Footer**: Encouraging closing message

4. **Personalization**:
   - Match their {tone} tone exactly throughout all text
   - Use their {style} communication style consistently
   - Incorporate their personality traits into language and presentation
   - Reference specific data points and achievements
   - Make it feel like a close friend who knows them deeply is writing

5. **Interactive Elements**:
   - Hover effects on cards and buttons
   - Smooth CSS transitions
   - Progress bars for metrics
   - Collapsible sections if appropriate
   - Touch-friendly design for mobile

6. **Accessibility**:
   - Proper semantic HTML structure
   - WCAG AA compliant color contrasts
   - Alt text for visual elements
   - Screen reader friendly markup

7. **CSS Styling**:
   - Use CSS Grid and Flexbox for layouts
   - Implement smooth animations and transitions
   - Create beautiful gradients and shadows
   - Ensure cross-browser compatibility
   - Optimize for both light and dark themes

8. **Content Guidelines**:
   - Celebrate achievements and progress
   - Frame challenges as growth opportunities
   - Use encouraging, supportive language
   - Include specific data insights naturally
   - End with motivation and forward momentum

Generate a complete, beautiful HTML report that will inspire and motivate the user while providing valuable insights from their data. The report should feel personal, professional, and genuinely helpful for their growth journey.

Return ONLY the complete HTML document. No explanations or additional text."""
    
    def _prepare_report_data(
        self,
        entries: List[Dict[str, Any]],
        stats: Dict[str, Any],
        correlations: Dict[str, Any],
        story: str,
        recommendations: List[Dict[str, Any]],
        questions: List[Dict[str, Any]],
        visualizations: Dict[str, str],
        validation_results: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Prepare comprehensive data for report generation."""
        # Get period information
        period = stats.get('period', {})
        start_date = period.get('start_date', 'Unknown')
        end_date = period.get('end_date', 'Unknown')
        
        # Summarize key metrics
        key_metrics = {}
        quant_metrics = stats.get('quantitative_metrics', {})
        for metric, data in list(quant_metrics.items())[:5]:  # Top 5 metrics
            if isinstance(data, dict):
                key_metrics[metric] = {
                    'value': data.get('mean', 0),
                    'trend': data.get('trend', 'stable'),
                    'unit': data.get('unit', ''),
                    'consistency': data.get('consistency', 0)
                }
        
        # Summarize habits
        habit_summary = {}
        habit_metrics = stats.get('habit_metrics', {})
        for habit, data in habit_metrics.items():
            if isinstance(data, dict):
                habit_summary[habit] = {
                    'completion_rate': data.get('completion_rate', 0) * 100,
                    'consistency': data.get('consistency_score', 0),
                    'category': data.get('category', 'personal')
                }
        
        # Top insights
        top_insights = []
        for insight in correlations.get('insights', [])[:3]:
            top_insights.append({
                'text': insight.get('insight', ''),
                'strength': insight.get('strength', 'moderate'),
                'type': insight.get('type', 'general')
            })
        
        # High priority recommendations
        priority_recommendations = [
            rec for rec in recommendations 
            if rec.get('priority') == 'high'
        ][:3]
        
        # Key questions
        key_questions = [
            q for q in questions 
            if q.get('priority') in ['high', 'medium']
        ][:4]
        
        # Data quality summary
        quality_info = {
            'score': validation_results.get('data_quality_score', 0) * 100,
            'flags': validation_results.get('quality_flags', []),
            'issues_count': len(validation_results.get('outliers', [])) + 
                          len(validation_results.get('inconsistencies', []))
        }
        
        return {
            'period': {
                'start_date': start_date,
                'end_date': end_date,
                'total_entries': len(entries),
                'total_days': period.get('total_days', 0)
            },
            'story': story,
            'key_metrics': key_metrics,
            'habit_summary': habit_summary,
            'top_insights': top_insights,
            'priority_recommendations': priority_recommendations,
            'key_questions': key_questions,
            'quality_info': quality_info,
            'visualizations_available': list(visualizations.keys()),
            'overall_trend': stats.get('summary', {}).get('overall_trend', 'stable')
        }
    
    def _embed_visualizations(self, html_content: str, visualizations: Dict[str, str]) -> str:
        """Embed SVG visualizations into HTML placeholders."""
        try:
            # Replace chart placeholders with actual SVG content
            for i, (chart_name, svg_content) in enumerate(visualizations.items()):
                placeholder = f"<!--chart{i}-->"
                if placeholder in html_content:
                    # Clean SVG content
                    cleaned_svg = self._clean_svg_content(svg_content)
                    html_content = html_content.replace(placeholder, cleaned_svg)
                
                # Also try named placeholders
                named_placeholder = f"<!--{chart_name}-->"
                if named_placeholder in html_content:
                    cleaned_svg = self._clean_svg_content(svg_content)
                    html_content = html_content.replace(named_placeholder, cleaned_svg)
            
            return html_content
            
        except Exception as e:
            logger.error(f"Error embedding visualizations: {e}")
            return html_content
    
    def _clean_svg_content(self, svg_content: str) -> str:
        """Clean and optimize SVG content for embedding."""
        try:
            # Remove XML declaration if present
            svg_content = re.sub(r'<\?xml[^>]*\?>', '', svg_content)
            
            # Ensure SVG has proper attributes for responsive design
            if '<svg' in svg_content and 'viewBox' not in svg_content:
                # Try to extract width and height to create viewBox
                width_match = re.search(r'width="(\d+)"', svg_content)
                height_match = re.search(r'height="(\d+)"', svg_content)
                
                if width_match and height_match:
                    width = width_match.group(1)
                    height = height_match.group(1)
                    viewbox = f'viewBox="0 0 {width} {height}"'
                    
                    # Add viewBox and make responsive
                    svg_content = svg_content.replace(
                        '<svg',
                        f'<svg {viewbox} style="width: 100%; height: auto; max-width: 100%;"'
                    )
            
            return svg_content
            
        except Exception as e:
            logger.warning(f"Error cleaning SVG content: {e}")
            return svg_content
    
    def _validate_and_clean_html(self, html_content: str) -> str:
        """Validate and clean the generated HTML."""
        try:
            # Ensure proper HTML5 structure
            if not html_content.strip().startswith('<!DOCTYPE html>'):
                if '<html' in html_content:
                    # Extract from <html> tag
                    html_start = html_content.find('<html')
                    html_content = '<!DOCTYPE html>\n' + html_content[html_start:]
                else:
                    # Wrap in basic structure
                    html_content = f"""<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Weekly Report</title>
</head>
<body>
{html_content}
</body>
</html>"""
            
            # Remove any remaining chart placeholders that weren't replaced
            html_content = re.sub(r'<!--chart\d+-->', '', html_content)
            
            # Ensure proper encoding
            if '<meta charset="UTF-8">' not in html_content and '<head>' in html_content:
                html_content = html_content.replace(
                    '<head>',
                    '<head>\n    <meta charset="UTF-8">'
                )
            
            return html_content
            
        except Exception as e:
            logger.error(f"Error validating HTML: {e}")
            return html_content
    
    def _generate_fallback_report(
        self,
        user_traits: Dict[str, Any],
        story: str,
        recommendations: List[Dict[str, Any]]
    ) -> str:
        """Generate a fallback HTML report when AI generation fails."""
        tone = user_traits.get('tone', 'informal')
        
        greeting = "Hello!" if tone == 'informal' else "Greetings"
        
        # Create basic HTML structure
        html_template = """<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Weekly Summary Report</title>
    <style>
        body {
            font-family: Inter, system-ui, -apple-system, sans-serif;
            line-height: 1.6;
            color: #374151;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        }
        .container {
            background: white;
            border-radius: 16px;
            padding: 40px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e5e7eb;
        }
        .header h1 {
            color: #3B82F6;
            font-size: 2.5rem;
            margin-bottom: 10px;
        }
        .story-section {
            background: #f8fafc;
            padding: 30px;
            border-radius: 12px;
            margin: 30px 0;
            border-left: 4px solid #3B82F6;
        }
        .recommendations {
            margin: 30px 0;
        }
        .recommendation {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }
        .recommendation h3 {
            color: #10B981;
            margin-top: 0;
        }
        .footer {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #e5e7eb;
            color: #6B7280;
        }
        @media (max-width: 768px) {
            body { padding: 10px; }
            .container { padding: 20px; }
            .header h1 { font-size: 2rem; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Your Weekly Journey</h1>
            <p>{{ greeting }} Here's your personalized weekly summary.</p>
        </div>
        
        <div class="story-section">
            <h2>Your Story This Week</h2>
            <p>{{ story }}</p>
        </div>
        
        <div class="recommendations">
            <h2>Recommendations for Growth</h2>
            {% for rec in recommendations %}
            <div class="recommendation">
                <h3>{{ rec.title }}</h3>
                <p>{{ rec.description }}</p>
                {% if rec.actionable_steps %}
                <ul>
                    {% for step in rec.actionable_steps %}
                    <li>{{ step }}</li>
                    {% endfor %}
                </ul>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="footer">
            <p>Keep up the amazing work on your journey of growth and self-discovery! 🌟</p>
        </div>
    </div>
</body>
</html>"""
        
        try:
            template = self.jinja_env.from_string(html_template)
            return template.render(
                greeting=greeting,
                story=story or "This week has been part of your ongoing journey of growth and self-reflection.",
                recommendations=recommendations[:5]  # Limit to 5 recommendations
            )
        except Exception as e:
            logger.error(f"Error generating fallback report: {e}")
            return self._get_minimal_fallback_report()
    
    def _get_minimal_fallback_report(self) -> str:
        """Get minimal fallback report when everything else fails."""
        return """<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Weekly Report</title>
    <style>
        body { font-family: system-ui, sans-serif; max-width: 600px; margin: 50px auto; padding: 20px; }
        .container { background: #f8fafc; padding: 40px; border-radius: 12px; text-align: center; }
        h1 { color: #3B82F6; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Weekly Summary</h1>
        <p>Your weekly report is being generated. Thank you for your patience!</p>
        <p>Continue your amazing journey of growth and self-discovery.</p>
    </div>
</body>
</html>"""


# Global service instance
report_builder_service = ReportBuilderService()
