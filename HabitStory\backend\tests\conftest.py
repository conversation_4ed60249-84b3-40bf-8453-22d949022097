"""
Pytest configuration and fixtures for testing the HabitStory backend.
"""

import pytest
import asyncio
from typing import AsyncGenerator, Dict, Any
from unittest.mock import AsyncMock, MagicMock
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker
from httpx import AsyncClient

from app.main import app
from app.database import get_db, Base
from app.services.gemini_client import GeminiClient


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
async def test_db() -> AsyncGenerator[AsyncSession, None]:
    """Create a test database session."""
    # Use in-memory SQLite for testing
    engine = create_async_engine(
        "sqlite+aiosqlite:///:memory:",
        echo=False,
        future=True
    )
    
    # Create tables
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    
    # Create session
    async_session = sessionmaker(
        engine, class_=AsyncSession, expire_on_commit=False
    )
    
    async with async_session() as session:
        yield session
    
    await engine.dispose()


@pytest.fixture
async def client(test_db: AsyncSession) -> AsyncGenerator[AsyncClient, None]:
    """Create a test client with mocked database."""
    
    async def override_get_db():
        yield test_db
    
    app.dependency_overrides[get_db] = override_get_db
    
    async with AsyncClient(app=app, base_url="http://test") as ac:
        yield ac
    
    app.dependency_overrides.clear()


@pytest.fixture
def mock_gemini_client():
    """Mock Gemini client for testing."""
    mock_client = MagicMock(spec=GeminiClient)
    
    # Mock successful API response
    mock_response = {
        "response": "Test response from Gemini",
        "type": "text",
        "tokens": {
            "input": 10,
            "output": 5,
            "total": 15
        },
        "raw_response": {"test": "data"}
    }
    
    mock_client.call_gemini = AsyncMock(return_value=mock_response)
    mock_client.check_token_limit = AsyncMock(return_value={
        "limit": 10000,
        "used": 100,
        "remaining": 9900,
        "percentage_used": 1.0
    })
    
    return mock_client


@pytest.fixture
def sample_journal_entry():
    """Sample journal entry for testing."""
    return {
        "text": "Today I woke up feeling energized after 8 hours of sleep. I went for a 30-minute run and felt great. Had a healthy breakfast and meditated for 15 minutes. Work was productive and I felt focused. Mood was around 8/10 today.",
        "date": "2024-01-15T10:00:00Z",
        "user_id": "test_user_123"
    }


@pytest.fixture
def sample_parsed_entry():
    """Sample parsed journal entry for testing."""
    return {
        "text": "Today I woke up feeling energized after 8 hours of sleep...",
        "date": "2024-01-15T10:00:00Z",
        "user_id": "test_user_123",
        "habits": ["running", "meditation", "healthy eating"],
        "qualitative_habits": [
            {
                "name": "morning run",
                "category": "health",
                "completed": True,
                "confidence": 0.9
            },
            {
                "name": "meditation",
                "category": "wellness",
                "completed": True,
                "confidence": 0.8
            }
        ],
        "metrics": {
            "sleep_hours": 8,
            "exercise_minutes": 30,
            "meditation_minutes": 15,
            "mood_score": 8,
            "energy_level": 8
        },
        "reflection": "Felt energized and productive today with good habits.",
        "user_traits": {
            "tone": "informal",
            "style": "descriptive",
            "traits": {
                "health_conscious": True,
                "goal_oriented": True
            },
            "trait_evidence": {
                "health_conscious": ["mentions exercise", "healthy breakfast"],
                "goal_oriented": ["structured routine", "tracks metrics"]
            }
        }
    }


@pytest.fixture
def sample_user_traits():
    """Sample user traits for testing."""
    return {
        "tone": "informal",
        "style": "conversational",
        "traits": {
            "optimistic": True,
            "health_conscious": True,
            "reflective": True
        },
        "trait_evidence": {
            "optimistic": ["uses positive language", "focuses on achievements"],
            "health_conscious": ["tracks exercise", "mentions healthy habits"],
            "reflective": ["engages in journaling", "self-aware"]
        }
    }


@pytest.fixture
def sample_weekly_entries():
    """Sample week of journal entries for testing."""
    return [
        {
            "text": "Great start to the week! Ran 5k and felt amazing.",
            "date": "2024-01-15T10:00:00Z",
            "user_id": "test_user_123",
            "habits": ["running"],
            "metrics": {"exercise_minutes": 45, "mood_score": 9},
            "reflection": "Energetic start to the week"
        },
        {
            "text": "Busy day at work but managed to meditate for 20 minutes.",
            "date": "2024-01-16T10:00:00Z",
            "user_id": "test_user_123",
            "habits": ["meditation"],
            "metrics": {"meditation_minutes": 20, "stress_level": 4},
            "reflection": "Found calm despite busy schedule"
        },
        {
            "text": "Struggled with sleep last night, only got 5 hours. Feeling tired.",
            "date": "2024-01-17T10:00:00Z",
            "user_id": "test_user_123",
            "habits": [],
            "metrics": {"sleep_hours": 5, "mood_score": 4, "energy_level": 3},
            "reflection": "Need to prioritize sleep better"
        },
        {
            "text": "Better day today. Got 8 hours of sleep and had energy for yoga.",
            "date": "2024-01-18T10:00:00Z",
            "user_id": "test_user_123",
            "habits": ["yoga", "good sleep"],
            "metrics": {"sleep_hours": 8, "exercise_minutes": 60, "mood_score": 7},
            "reflection": "Sleep makes such a difference"
        },
        {
            "text": "Productive Friday! Completed all my goals and celebrated with friends.",
            "date": "2024-01-19T10:00:00Z",
            "user_id": "test_user_123",
            "habits": ["goal completion", "social time"],
            "metrics": {"productivity_score": 9, "social_interactions": 3, "mood_score": 8},
            "reflection": "Great way to end the work week"
        }
    ]


@pytest.fixture
def mock_pipeline_results():
    """Mock pipeline results for testing."""
    return {
        "user_id": "test_user_123",
        "generation_start": "2024-01-20T10:00:00Z",
        "modules_executed": [
            "parsing", "metrics", "correlations", "storytelling", 
            "recommendations", "validation", "questions", 
            "visualization", "report_builder"
        ],
        "errors": [],
        "warnings": [],
        "final_report": "<html><body><h1>Test Report</h1></body></html>",
        "stats": {
            "period": {"total_days": 5, "entries_count": 5},
            "quantitative_metrics": {
                "mood_score": {"mean": 7.2, "trend": "stable"},
                "sleep_hours": {"mean": 6.8, "trend": "improving"}
            },
            "habit_metrics": {
                "running": {"completion_rate": 0.6},
                "meditation": {"completion_rate": 0.4}
            }
        },
        "correlations": {
            "metric_correlations": [],
            "insights": [
                {"insight": "Sleep quality strongly affects mood", "strength": "strong"}
            ]
        },
        "story": "This week was a journey of ups and downs...",
        "recommendations": [
            {
                "title": "Prioritize Sleep",
                "description": "Focus on getting 7-8 hours consistently",
                "priority": "high"
            }
        ],
        "questions": [
            {
                "question": "What helps you sleep better?",
                "category": "wellness",
                "priority": "high"
            }
        ],
        "visualizations": {
            "weekly_summary": "<svg>test chart</svg>"
        },
        "validation": {
            "data_quality_score": 0.85,
            "outliers": [],
            "inconsistencies": []
        },
        "metadata": {
            "total_execution_time": 45.2,
            "entries_processed": 5,
            "modules_completed": 9,
            "generation_end": "2024-01-20T10:00:45Z"
        }
    }


@pytest.fixture
def mock_gemini_function_response():
    """Mock Gemini function calling response."""
    return {
        "response": '{"name": "extract_entry_data", "args": {"habits": ["running"], "metrics": {"mood_score": 8}}}',
        "type": "function_call",
        "tokens": {"input": 20, "output": 10, "total": 30}
    }


# Helper functions for tests
def assert_valid_json_structure(data: Dict[str, Any], required_keys: list):
    """Assert that data has required JSON structure."""
    assert isinstance(data, dict)
    for key in required_keys:
        assert key in data, f"Missing required key: {key}"


def assert_valid_html(html_content: str):
    """Assert that content is valid HTML."""
    assert html_content.strip().startswith("<!DOCTYPE html>")
    assert "<html" in html_content
    assert "</html>" in html_content
    assert "<body" in html_content
    assert "</body>" in html_content


def create_mock_db_session():
    """Create a mock database session for unit tests."""
    mock_session = AsyncMock(spec=AsyncSession)
    mock_session.add = MagicMock()
    mock_session.commit = AsyncMock()
    mock_session.refresh = AsyncMock()
    mock_session.execute = AsyncMock()
    mock_session.close = AsyncMock()
    return mock_session
