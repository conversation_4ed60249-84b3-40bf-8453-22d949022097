"""
Module 2: Metrics Service
Calculates statistics (averages, totals, streaks) using Python/pandas.
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from collections import defaultdict

logger = logging.getLogger(__name__)


class MetricsService:
    """Service for calculating metrics and statistics from journal data."""
    
    def __init__(self):
        pass
    
    def calculate_weekly_stats(self, entries: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Calculate comprehensive weekly statistics from journal entries.
        
        Args:
            entries: List of journal entries with parsed data
            
        Returns:
            Dict containing calculated statistics
        """
        try:
            if not entries:
                return self._get_empty_stats()
            
            # Convert entries to DataFrame for easier analysis
            df = self._entries_to_dataframe(entries)
            
            if df.empty:
                return self._get_empty_stats()
            
            stats = {
                "period": self._get_period_info(entries),
                "quantitative_metrics": self._calculate_quantitative_metrics(df),
                "habit_metrics": self._calculate_habit_metrics(entries),
                "qualitative_metrics": self._calculate_qualitative_metrics(entries),
                "trends": self._calculate_trends(df),
                "streaks": self._calculate_streaks(entries),
                "summary": {}
            }
            
            # Add summary statistics
            stats["summary"] = self._generate_summary_stats(stats)
            
            return stats
            
        except Exception as e:
            logger.error(f"Error calculating weekly stats: {e}")
            return self._get_empty_stats()
    
    def _entries_to_dataframe(self, entries: List[Dict[str, Any]]) -> pd.DataFrame:
        """Convert journal entries to pandas DataFrame."""
        data = []
        
        for entry in entries:
            try:
                # Parse metrics if they're stored as JSON string
                metrics = entry.get("metrics", {})
                if isinstance(metrics, str):
                    import json
                    metrics = json.loads(metrics)
                
                row = {
                    "date": pd.to_datetime(entry.get("date", entry.get("created_at"))),
                    "entry_length": len(entry.get("text", "")),
                    **metrics
                }
                data.append(row)
                
            except Exception as e:
                logger.warning(f"Error processing entry: {e}")
                continue
        
        if not data:
            return pd.DataFrame()
        
        df = pd.DataFrame(data)
        df.set_index("date", inplace=True)
        df.sort_index(inplace=True)
        
        return df
    
    def _calculate_quantitative_metrics(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Calculate statistics for quantitative metrics."""
        if df.empty:
            return {}
        
        metrics = {}
        
        # Define metric categories and their properties
        metric_configs = {
            "sleep_hours": {"min": 0, "max": 24, "unit": "hours", "ideal_range": (7, 9)},
            "water_liters": {"min": 0, "max": 10, "unit": "liters", "ideal_range": (2, 3)},
            "exercise_minutes": {"min": 0, "max": 300, "unit": "minutes", "ideal_range": (30, 60)},
            "mood_score": {"min": 1, "max": 10, "unit": "score", "ideal_range": (7, 10)},
            "stress_level": {"min": 1, "max": 10, "unit": "score", "ideal_range": (1, 4)},
            "productivity_score": {"min": 1, "max": 10, "unit": "score", "ideal_range": (7, 10)},
            "social_interactions": {"min": 0, "max": 20, "unit": "count", "ideal_range": (2, 5)},
            "screen_time_hours": {"min": 0, "max": 24, "unit": "hours", "ideal_range": (0, 6)},
            "meditation_minutes": {"min": 0, "max": 120, "unit": "minutes", "ideal_range": (10, 30)},
            "steps": {"min": 0, "max": 50000, "unit": "steps", "ideal_range": (8000, 12000)},
            "energy_level": {"min": 1, "max": 10, "unit": "score", "ideal_range": (7, 10)}
        }
        
        for metric, config in metric_configs.items():
            if metric in df.columns:
                series = df[metric].dropna()
                if len(series) > 0:
                    metrics[metric] = self._calculate_metric_stats(series, config)
        
        return metrics
    
    def _calculate_metric_stats(self, series: pd.Series, config: Dict) -> Dict[str, Any]:
        """Calculate statistics for a single metric."""
        stats = {
            "count": len(series),
            "mean": float(series.mean()),
            "median": float(series.median()),
            "std": float(series.std()) if len(series) > 1 else 0,
            "min": float(series.min()),
            "max": float(series.max()),
            "unit": config["unit"],
            "trend": self._calculate_simple_trend(series),
            "consistency": self._calculate_consistency(series),
        }
        
        # Calculate ideal range performance
        if "ideal_range" in config:
            ideal_min, ideal_max = config["ideal_range"]
            in_range = series.between(ideal_min, ideal_max).sum()
            stats["ideal_range"] = {
                "min": ideal_min,
                "max": ideal_max,
                "in_range_count": int(in_range),
                "in_range_percentage": float(in_range / len(series) * 100)
            }
        
        return stats
    
    def _calculate_habit_metrics(self, entries: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Calculate metrics for habits."""
        habit_data = defaultdict(list)
        
        for entry in entries:
            try:
                # Process regular habits
                habits = entry.get("habits", [])
                if isinstance(habits, str):
                    import json
                    habits = json.loads(habits)
                
                date = entry.get("date", entry.get("created_at"))
                
                for habit in habits:
                    habit_data[habit].append({
                        "date": date,
                        "completed": True,
                        "confidence": 1.0
                    })
                
                # Process qualitative habits
                qual_habits = entry.get("qualitative_habits", [])
                if isinstance(qual_habits, str):
                    import json
                    qual_habits = json.loads(qual_habits)
                
                for habit in qual_habits:
                    if isinstance(habit, dict):
                        name = habit.get("name")
                        if name:
                            habit_data[name].append({
                                "date": date,
                                "completed": habit.get("completed", False),
                                "confidence": habit.get("confidence", 1.0),
                                "category": habit.get("category", "personal")
                            })
                            
            except Exception as e:
                logger.warning(f"Error processing habits in entry: {e}")
                continue
        
        # Calculate statistics for each habit
        habit_stats = {}
        for habit_name, occurrences in habit_data.items():
            if occurrences:
                habit_stats[habit_name] = self._calculate_habit_stats(occurrences)
        
        return habit_stats
    
    def _calculate_habit_stats(self, occurrences: List[Dict]) -> Dict[str, Any]:
        """Calculate statistics for a single habit."""
        total_days = len(occurrences)
        completed_days = sum(1 for occ in occurrences if occ.get("completed", True))
        
        # Calculate average confidence
        confidences = [occ.get("confidence", 1.0) for occ in occurrences]
        avg_confidence = sum(confidences) / len(confidences) if confidences else 0
        
        # Get category (use most common)
        categories = [occ.get("category", "personal") for occ in occurrences if "category" in occ]
        category = max(set(categories), key=categories.count) if categories else "personal"
        
        return {
            "total_occurrences": total_days,
            "completed_count": completed_days,
            "completion_rate": completed_days / total_days if total_days > 0 else 0,
            "average_confidence": avg_confidence,
            "category": category,
            "consistency_score": self._calculate_habit_consistency(occurrences)
        }
    
    def _calculate_qualitative_metrics(self, entries: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Calculate metrics for qualitative aspects."""
        reflection_lengths = []
        entry_lengths = []
        sentiment_scores = []
        
        for entry in entries:
            # Entry length
            text = entry.get("text", "")
            entry_lengths.append(len(text))
            
            # Reflection length
            reflection = entry.get("reflection", "")
            reflection_lengths.append(len(reflection))
            
            # Simple sentiment analysis based on keywords
            sentiment_scores.append(self._estimate_sentiment(text))
        
        return {
            "entry_length": {
                "mean": np.mean(entry_lengths) if entry_lengths else 0,
                "std": np.std(entry_lengths) if len(entry_lengths) > 1 else 0,
                "trend": self._calculate_simple_trend(pd.Series(entry_lengths))
            },
            "reflection_quality": {
                "mean_length": np.mean(reflection_lengths) if reflection_lengths else 0,
                "consistency": np.std(reflection_lengths) if len(reflection_lengths) > 1 else 0
            },
            "sentiment": {
                "mean": np.mean(sentiment_scores) if sentiment_scores else 0,
                "trend": self._calculate_simple_trend(pd.Series(sentiment_scores))
            }
        }
    
    def _calculate_trends(self, df: pd.DataFrame) -> Dict[str, str]:
        """Calculate trends for metrics over time."""
        trends = {}
        
        for column in df.select_dtypes(include=[np.number]).columns:
            series = df[column].dropna()
            if len(series) >= 2:
                trends[column] = self._calculate_simple_trend(series)
        
        return trends
    
    def _calculate_streaks(self, entries: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Calculate habit streaks."""
        # This is a simplified version - would need more sophisticated logic for real streaks
        habit_streaks = {}
        
        # Group entries by date and check for habit consistency
        dates = sorted([entry.get("date", entry.get("created_at")) for entry in entries])
        
        if len(dates) >= 2:
            # Calculate journaling streak
            consecutive_days = 1
            for i in range(1, len(dates)):
                prev_date = pd.to_datetime(dates[i-1])
                curr_date = pd.to_datetime(dates[i])
                if (curr_date - prev_date).days == 1:
                    consecutive_days += 1
                else:
                    break
            
            habit_streaks["journaling"] = {
                "current_streak": consecutive_days,
                "longest_streak": consecutive_days  # Simplified
            }
        
        return habit_streaks
    
    def _calculate_simple_trend(self, series: pd.Series) -> str:
        """Calculate simple trend direction."""
        if len(series) < 2:
            return "stable"
        
        # Simple linear trend
        x = np.arange(len(series))
        slope = np.polyfit(x, series.values, 1)[0]
        
        if slope > 0.1:
            return "improving"
        elif slope < -0.1:
            return "declining"
        else:
            return "stable"
    
    def _calculate_consistency(self, series: pd.Series) -> float:
        """Calculate consistency score (0-1, higher is more consistent)."""
        if len(series) <= 1:
            return 1.0
        
        # Use coefficient of variation (inverted and normalized)
        cv = series.std() / series.mean() if series.mean() != 0 else 0
        consistency = max(0, 1 - min(cv, 1))
        return float(consistency)
    
    def _calculate_habit_consistency(self, occurrences: List[Dict]) -> float:
        """Calculate habit consistency score."""
        if not occurrences:
            return 0.0
        
        # Simple consistency based on completion rate and confidence
        completion_rate = sum(1 for occ in occurrences if occ.get("completed", True)) / len(occurrences)
        avg_confidence = sum(occ.get("confidence", 1.0) for occ in occurrences) / len(occurrences)
        
        return (completion_rate + avg_confidence) / 2
    
    def _estimate_sentiment(self, text: str) -> float:
        """Simple sentiment estimation (-1 to 1)."""
        positive_words = ["good", "great", "happy", "excellent", "amazing", "wonderful", "fantastic", "love", "enjoy", "excited"]
        negative_words = ["bad", "terrible", "awful", "hate", "sad", "angry", "frustrated", "stressed", "worried", "difficult"]
        
        text_lower = text.lower()
        positive_count = sum(1 for word in positive_words if word in text_lower)
        negative_count = sum(1 for word in negative_words if word in text_lower)
        
        total_words = len(text.split())
        if total_words == 0:
            return 0.0
        
        sentiment = (positive_count - negative_count) / max(total_words / 10, 1)
        return max(-1, min(1, sentiment))
    
    def _get_period_info(self, entries: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Get information about the time period."""
        if not entries:
            return {}
        
        dates = [pd.to_datetime(entry.get("date", entry.get("created_at"))) for entry in entries]
        dates = [d for d in dates if pd.notna(d)]
        
        if not dates:
            return {}
        
        return {
            "start_date": min(dates).isoformat(),
            "end_date": max(dates).isoformat(),
            "total_days": len(set(d.date() for d in dates)),
            "entries_count": len(entries)
        }
    
    def _generate_summary_stats(self, stats: Dict[str, Any]) -> Dict[str, Any]:
        """Generate high-level summary statistics."""
        summary = {
            "total_metrics_tracked": len(stats.get("quantitative_metrics", {})),
            "total_habits_tracked": len(stats.get("habit_metrics", {})),
            "data_quality_score": self._calculate_data_quality_score(stats),
            "overall_trend": self._determine_overall_trend(stats)
        }
        
        return summary
    
    def _calculate_data_quality_score(self, stats: Dict[str, Any]) -> float:
        """Calculate overall data quality score (0-1)."""
        # Simple scoring based on completeness and consistency
        metrics_count = len(stats.get("quantitative_metrics", {}))
        habits_count = len(stats.get("habit_metrics", {}))
        
        # Base score on data richness
        richness_score = min(1.0, (metrics_count + habits_count) / 10)
        
        return richness_score
    
    def _determine_overall_trend(self, stats: Dict[str, Any]) -> str:
        """Determine overall trend across all metrics."""
        trends = stats.get("trends", {})
        if not trends:
            return "stable"
        
        improving_count = sum(1 for trend in trends.values() if trend == "improving")
        declining_count = sum(1 for trend in trends.values() if trend == "declining")
        
        if improving_count > declining_count:
            return "improving"
        elif declining_count > improving_count:
            return "declining"
        else:
            return "stable"
    
    def _get_empty_stats(self) -> Dict[str, Any]:
        """Return empty stats structure."""
        return {
            "period": {},
            "quantitative_metrics": {},
            "habit_metrics": {},
            "qualitative_metrics": {},
            "trends": {},
            "streaks": {},
            "summary": {
                "total_metrics_tracked": 0,
                "total_habits_tracked": 0,
                "data_quality_score": 0.0,
                "overall_trend": "stable"
            }
        }


# Global service instance
metrics_service = MetricsService()
