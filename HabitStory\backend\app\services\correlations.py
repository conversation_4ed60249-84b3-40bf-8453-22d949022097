"""
Module 3: Correlations Service
Finds correlations between metrics using Python/scipy.stats.
"""

import pandas as pd
import numpy as np
from scipy import stats
from scipy.stats import pearsonr, spearmanr
import logging
from typing import Dict, List, Any, Tuple, Optional
from itertools import combinations

logger = logging.getLogger(__name__)


class CorrelationsService:
    """Service for finding correlations between metrics and habits."""
    
    def __init__(self):
        self.min_data_points = 3  # Minimum data points needed for correlation
        self.significance_threshold = 0.05  # P-value threshold for significance
        self.correlation_threshold = 0.3  # Minimum correlation strength to report
    
    def find_correlations(self, entries: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Find correlations between metrics and habits.
        
        Args:
            entries: List of journal entries with parsed data
            
        Returns:
            Dict containing correlation analysis results
        """
        try:
            if len(entries) < self.min_data_points:
                return self._get_empty_correlations("Insufficient data points")
            
            # Convert entries to DataFrame
            df = self._entries_to_dataframe(entries)
            
            if df.empty or len(df) < self.min_data_points:
                return self._get_empty_correlations("No valid numeric data")
            
            correlations = {
                "metric_correlations": self._calculate_metric_correlations(df),
                "habit_metric_correlations": self._calculate_habit_metric_correlations(entries, df),
                "temporal_correlations": self._calculate_temporal_correlations(df),
                "insights": [],
                "summary": {}
            }
            
            # Generate insights from correlations
            correlations["insights"] = self._generate_correlation_insights(correlations)
            correlations["summary"] = self._generate_correlation_summary(correlations)
            
            return correlations
            
        except Exception as e:
            logger.error(f"Error calculating correlations: {e}")
            return self._get_empty_correlations(f"Error: {str(e)}")
    
    def _entries_to_dataframe(self, entries: List[Dict[str, Any]]) -> pd.DataFrame:
        """Convert journal entries to pandas DataFrame with numeric metrics."""
        data = []
        
        for entry in entries:
            try:
                # Parse metrics if they're stored as JSON string
                metrics = entry.get("metrics", {})
                if isinstance(metrics, str):
                    import json
                    metrics = json.loads(metrics)
                
                # Only include numeric metrics
                numeric_metrics = {}
                for key, value in metrics.items():
                    if isinstance(value, (int, float)) and not np.isnan(value):
                        numeric_metrics[key] = value
                
                if numeric_metrics:  # Only add if we have numeric data
                    row = {
                        "date": pd.to_datetime(entry.get("date", entry.get("created_at"))),
                        "entry_length": len(entry.get("text", "")),
                        **numeric_metrics
                    }
                    data.append(row)
                    
            except Exception as e:
                logger.warning(f"Error processing entry for correlations: {e}")
                continue
        
        if not data:
            return pd.DataFrame()
        
        df = pd.DataFrame(data)
        df.set_index("date", inplace=True)
        df.sort_index(inplace=True)
        
        # Remove columns with too many missing values or no variance
        df = df.dropna(axis=1, thresh=self.min_data_points)
        df = df.loc[:, df.var() > 0]  # Remove columns with zero variance
        
        return df
    
    def _calculate_metric_correlations(self, df: pd.DataFrame) -> List[Dict[str, Any]]:
        """Calculate correlations between numeric metrics."""
        correlations = []
        
        if df.empty or len(df.columns) < 2:
            return correlations
        
        # Get all pairs of numeric columns
        numeric_columns = df.select_dtypes(include=[np.number]).columns
        
        for col1, col2 in combinations(numeric_columns, 2):
            try:
                # Get data for both columns, removing NaN pairs
                data1 = df[col1].dropna()
                data2 = df[col2].dropna()
                
                # Find common indices
                common_idx = data1.index.intersection(data2.index)
                if len(common_idx) < self.min_data_points:
                    continue
                
                x = data1.loc[common_idx]
                y = data2.loc[common_idx]
                
                # Calculate Pearson correlation
                pearson_r, pearson_p = pearsonr(x, y)
                
                # Calculate Spearman correlation (rank-based, more robust)
                spearman_r, spearman_p = spearmanr(x, y)
                
                # Only include significant correlations above threshold
                if (abs(pearson_r) >= self.correlation_threshold and 
                    pearson_p < self.significance_threshold):
                    
                    correlation = {
                        "metric1": col1,
                        "metric2": col2,
                        "pearson_correlation": float(pearson_r),
                        "pearson_p_value": float(pearson_p),
                        "spearman_correlation": float(spearman_r),
                        "spearman_p_value": float(spearman_p),
                        "data_points": len(common_idx),
                        "strength": self._classify_correlation_strength(abs(pearson_r)),
                        "direction": "positive" if pearson_r > 0 else "negative",
                        "significance": "significant" if pearson_p < self.significance_threshold else "not_significant"
                    }
                    
                    correlations.append(correlation)
                    
            except Exception as e:
                logger.warning(f"Error calculating correlation between {col1} and {col2}: {e}")
                continue
        
        # Sort by correlation strength
        correlations.sort(key=lambda x: abs(x["pearson_correlation"]), reverse=True)
        
        return correlations
    
    def _calculate_habit_metric_correlations(
        self, 
        entries: List[Dict[str, Any]], 
        df: pd.DataFrame
    ) -> List[Dict[str, Any]]:
        """Calculate correlations between habits and metrics."""
        correlations = []
        
        if df.empty:
            return correlations
        
        # Extract habit data
        habit_data = self._extract_habit_data(entries)
        
        if not habit_data:
            return correlations
        
        # For each habit, correlate with each metric
        numeric_columns = df.select_dtypes(include=[np.number]).columns
        
        for habit_name, habit_occurrences in habit_data.items():
            # Create habit series aligned with metrics DataFrame
            habit_series = self._create_habit_series(habit_occurrences, df.index)
            
            if len(habit_series.dropna()) < self.min_data_points:
                continue
            
            for metric_col in numeric_columns:
                try:
                    metric_series = df[metric_col].dropna()
                    
                    # Find common dates
                    common_idx = habit_series.index.intersection(metric_series.index)
                    if len(common_idx) < self.min_data_points:
                        continue
                    
                    habit_values = habit_series.loc[common_idx]
                    metric_values = metric_series.loc[common_idx]
                    
                    # Skip if habit has no variance (all same values)
                    if habit_values.var() == 0:
                        continue
                    
                    # Calculate correlation
                    correlation_r, correlation_p = pearsonr(habit_values, metric_values)
                    
                    if (abs(correlation_r) >= self.correlation_threshold and 
                        correlation_p < self.significance_threshold):
                        
                        correlation = {
                            "habit": habit_name,
                            "metric": metric_col,
                            "correlation": float(correlation_r),
                            "p_value": float(correlation_p),
                            "data_points": len(common_idx),
                            "strength": self._classify_correlation_strength(abs(correlation_r)),
                            "direction": "positive" if correlation_r > 0 else "negative",
                            "significance": "significant" if correlation_p < self.significance_threshold else "not_significant"
                        }
                        
                        correlations.append(correlation)
                        
                except Exception as e:
                    logger.warning(f"Error calculating habit-metric correlation: {e}")
                    continue
        
        # Sort by correlation strength
        correlations.sort(key=lambda x: abs(x["correlation"]), reverse=True)
        
        return correlations
    
    def _calculate_temporal_correlations(self, df: pd.DataFrame) -> List[Dict[str, Any]]:
        """Calculate correlations with time-based patterns."""
        correlations = []
        
        if df.empty or len(df) < 7:  # Need at least a week of data
            return correlations
        
        try:
            # Add temporal features
            df_temp = df.copy()
            df_temp['day_of_week'] = df_temp.index.dayofweek
            df_temp['day_of_month'] = df_temp.index.day
            df_temp['days_since_start'] = (df_temp.index - df_temp.index.min()).days
            
            temporal_features = ['day_of_week', 'days_since_start']
            numeric_columns = df.select_dtypes(include=[np.number]).columns
            
            for temp_feature in temporal_features:
                for metric_col in numeric_columns:
                    try:
                        temp_values = df_temp[temp_feature]
                        metric_values = df_temp[metric_col].dropna()
                        
                        # Find common indices
                        common_idx = temp_values.index.intersection(metric_values.index)
                        if len(common_idx) < self.min_data_points:
                            continue
                        
                        x = temp_values.loc[common_idx]
                        y = metric_values.loc[common_idx]
                        
                        correlation_r, correlation_p = pearsonr(x, y)
                        
                        if (abs(correlation_r) >= self.correlation_threshold and 
                            correlation_p < self.significance_threshold):
                            
                            correlation = {
                                "temporal_feature": temp_feature,
                                "metric": metric_col,
                                "correlation": float(correlation_r),
                                "p_value": float(correlation_p),
                                "data_points": len(common_idx),
                                "strength": self._classify_correlation_strength(abs(correlation_r)),
                                "direction": "positive" if correlation_r > 0 else "negative"
                            }
                            
                            correlations.append(correlation)
                            
                    except Exception as e:
                        logger.warning(f"Error calculating temporal correlation: {e}")
                        continue
            
        except Exception as e:
            logger.warning(f"Error in temporal correlation analysis: {e}")
        
        return correlations
    
    def _extract_habit_data(self, entries: List[Dict[str, Any]]) -> Dict[str, List[Dict]]:
        """Extract habit data from entries."""
        habit_data = {}
        
        for entry in entries:
            try:
                date = pd.to_datetime(entry.get("date", entry.get("created_at")))
                
                # Process qualitative habits (more detailed)
                qual_habits = entry.get("qualitative_habits", [])
                if isinstance(qual_habits, str):
                    import json
                    qual_habits = json.loads(qual_habits)
                
                for habit in qual_habits:
                    if isinstance(habit, dict) and "name" in habit:
                        name = habit["name"]
                        if name not in habit_data:
                            habit_data[name] = []
                        
                        habit_data[name].append({
                            "date": date,
                            "completed": habit.get("completed", False),
                            "confidence": habit.get("confidence", 1.0)
                        })
                
            except Exception as e:
                logger.warning(f"Error extracting habit data: {e}")
                continue
        
        return habit_data
    
    def _create_habit_series(self, habit_occurrences: List[Dict], date_index: pd.DatetimeIndex) -> pd.Series:
        """Create a pandas Series for habit data aligned with date index."""
        habit_dict = {}
        
        for occurrence in habit_occurrences:
            date = occurrence["date"]
            # Use completion status weighted by confidence
            value = occurrence["confidence"] if occurrence["completed"] else 0
            habit_dict[date] = value
        
        # Create series aligned with the date index
        habit_series = pd.Series(index=date_index, dtype=float)
        
        for date, value in habit_dict.items():
            if date in habit_series.index:
                habit_series[date] = value
        
        return habit_series
    
    def _classify_correlation_strength(self, abs_correlation: float) -> str:
        """Classify correlation strength."""
        if abs_correlation >= 0.7:
            return "strong"
        elif abs_correlation >= 0.5:
            return "moderate"
        elif abs_correlation >= 0.3:
            return "weak"
        else:
            return "very_weak"
    
    def _generate_correlation_insights(self, correlations: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate human-readable insights from correlations."""
        insights = []
        
        # Insights from metric correlations
        for corr in correlations.get("metric_correlations", [])[:5]:  # Top 5
            insight = {
                "type": "metric_correlation",
                "insight": self._format_metric_correlation_insight(corr),
                "strength": corr["strength"],
                "significance": corr["significance"]
            }
            insights.append(insight)
        
        # Insights from habit-metric correlations
        for corr in correlations.get("habit_metric_correlations", [])[:5]:  # Top 5
            insight = {
                "type": "habit_metric_correlation",
                "insight": self._format_habit_metric_correlation_insight(corr),
                "strength": corr["strength"],
                "significance": corr["significance"]
            }
            insights.append(insight)
        
        return insights
    
    def _format_metric_correlation_insight(self, corr: Dict[str, Any]) -> str:
        """Format metric correlation as human-readable insight."""
        metric1 = corr["metric1"].replace("_", " ").title()
        metric2 = corr["metric2"].replace("_", " ").title()
        direction = corr["direction"]
        strength = corr["strength"]
        
        if direction == "positive":
            return f"{metric1} and {metric2} show a {strength} positive relationship - when one increases, the other tends to increase too."
        else:
            return f"{metric1} and {metric2} show a {strength} negative relationship - when one increases, the other tends to decrease."
    
    def _format_habit_metric_correlation_insight(self, corr: Dict[str, Any]) -> str:
        """Format habit-metric correlation as human-readable insight."""
        habit = corr["habit"].replace("_", " ").title()
        metric = corr["metric"].replace("_", " ").title()
        direction = corr["direction"]
        strength = corr["strength"]
        
        if direction == "positive":
            return f"When you practice '{habit}', your {metric} tends to be higher - showing a {strength} positive connection."
        else:
            return f"When you practice '{habit}', your {metric} tends to be lower - showing a {strength} negative connection."
    
    def _generate_correlation_summary(self, correlations: Dict[str, Any]) -> Dict[str, Any]:
        """Generate summary statistics for correlations."""
        metric_corrs = correlations.get("metric_correlations", [])
        habit_corrs = correlations.get("habit_metric_correlations", [])
        temporal_corrs = correlations.get("temporal_correlations", [])
        
        return {
            "total_metric_correlations": len(metric_corrs),
            "total_habit_correlations": len(habit_corrs),
            "total_temporal_correlations": len(temporal_corrs),
            "strongest_metric_correlation": max(metric_corrs, key=lambda x: abs(x["pearson_correlation"]))["pearson_correlation"] if metric_corrs else 0,
            "strongest_habit_correlation": max(habit_corrs, key=lambda x: abs(x["correlation"]))["correlation"] if habit_corrs else 0,
            "significant_correlations": len([c for c in metric_corrs + habit_corrs if c.get("significance") == "significant"])
        }
    
    def _get_empty_correlations(self, reason: str = "No data") -> Dict[str, Any]:
        """Return empty correlations structure."""
        return {
            "metric_correlations": [],
            "habit_metric_correlations": [],
            "temporal_correlations": [],
            "insights": [],
            "summary": {
                "total_metric_correlations": 0,
                "total_habit_correlations": 0,
                "total_temporal_correlations": 0,
                "strongest_metric_correlation": 0,
                "strongest_habit_correlation": 0,
                "significant_correlations": 0,
                "reason": reason
            }
        }


# Global service instance
correlations_service = CorrelationsService()
