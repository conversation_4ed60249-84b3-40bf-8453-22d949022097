# JWT Token Rotation System

## Overview

HabitStory implements a comprehensive JWT token rotation system for enhanced security. This system automatically rotates tokens to minimize the risk of token compromise and provides secure authentication for all API endpoints.

## Features

### 🔐 **Dual Token System**
- **Access Tokens**: Short-lived (30 minutes) for API access
- **Refresh Tokens**: Long-lived (30 days) for token renewal

### 🔄 **Automatic Token Rotation**
- Tokens are automatically rotated when they reach a certain age
- Configurable rotation threshold (default: 24 hours)
- Seamless rotation without user intervention

### 🛡️ **Security Features**
- Secure token hashing for storage
- Token revocation on logout
- Expired token cleanup
- Login attempt tracking
- Device and IP tracking

## API Endpoints

### Authentication Endpoints

#### `POST /api/v1/auth/login`
Login and receive initial tokens.

**Request:**
```json
{
  "user_id": "user123",
  "name": "<PERSON>"
}
```

**Response:**
```json
{
  "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "refresh_token": "abc123def456...",
  "token_type": "bearer",
  "expires_in": 1800,
  "user": {
    "user_id": "user123",
    "name": "John Doe",
    "plan": "FREE",
    "is_active": true
  }
}
```

#### `POST /api/v1/auth/refresh`
Refresh tokens (rotates both access and refresh tokens).

**Request:**
```json
{
  "refresh_token": "abc123def456..."
}
```

**Response:**
```json
{
  "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "refresh_token": "xyz789uvw012...",
  "token_type": "bearer",
  "expires_in": 1800
}
```

#### `POST /api/v1/auth/logout`
Logout and revoke all user tokens.

**Headers:**
```
Authorization: Bearer <access_token>
```

**Response:**
```json
{
  "message": "Successfully logged out"
}
```

#### `GET /api/v1/auth/me`
Get current user information.

**Headers:**
```
Authorization: Bearer <access_token>
```

**Response:**
```json
{
  "user_id": "user123",
  "name": "John Doe",
  "plan": "FREE",
  "is_active": true,
  "created_at": "2024-01-01T00:00:00Z"
}
```

## Automatic Token Rotation

### How It Works

1. **Middleware Detection**: The `TokenRotationMiddleware` checks token age on each request
2. **Rotation Trigger**: If token is older than threshold (24 hours), rotation is triggered
3. **Response Headers**: New tokens are provided in response headers:
   - `X-Token-Rotated: true`
   - `X-New-Access-Token: <new_token>`

### Client Implementation

Clients should check for rotation headers and update stored tokens:

```javascript
// Example client-side handling
const response = await fetch('/api/v1/entries/parse', {
  headers: {
    'Authorization': `Bearer ${accessToken}`
  }
});

// Check for token rotation
if (response.headers.get('X-Token-Rotated') === 'true') {
  const newAccessToken = response.headers.get('X-New-Access-Token');
  // Update stored access token
  await updateStoredToken(newAccessToken);
}
```

## Configuration

### Environment Variables

```bash
# Security Configuration
SECRET_KEY=your_secure_secret_key_here
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=30

# Token Rotation Settings
AUTO_ROTATE_TOKENS=true
TOKEN_ROTATION_THRESHOLD_HOURS=24
```

### Settings in config.py

```python
class Settings(BaseSettings):
    # Security
    secret_key: str = os.getenv("SECRET_KEY", "dev-key")
    algorithm: str = "HS256"
    access_token_expire_minutes: int = 30
    refresh_token_expire_days: int = 30
    
    # Token rotation
    auto_rotate_tokens: bool = True
    token_rotation_threshold_hours: int = 24
```

## Database Schema

### Users Table
```sql
CREATE TABLE users (
    id INTEGER PRIMARY KEY,
    user_id VARCHAR(255) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) UNIQUE,
    is_active BOOLEAN DEFAULT TRUE,
    plan VARCHAR(20) DEFAULT 'FREE',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP
);
```

### Refresh Tokens Table
```sql
CREATE TABLE refresh_tokens (
    id INTEGER PRIMARY KEY,
    user_id VARCHAR(255) NOT NULL,
    token_hash VARCHAR(255) UNIQUE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NOT NULL,
    last_used_at TIMESTAMP,
    is_revoked BOOLEAN DEFAULT FALSE,
    revoked_at TIMESTAMP,
    device_info TEXT,
    ip_address VARCHAR(45)
);
```

### Login Attempts Table
```sql
CREATE TABLE login_attempts (
    id INTEGER PRIMARY KEY,
    user_id VARCHAR(255),
    ip_address VARCHAR(45) NOT NULL,
    user_agent TEXT,
    success BOOLEAN NOT NULL,
    attempted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    failure_reason VARCHAR(255)
);
```

## Security Best Practices

### ✅ **Implemented**
- Secure secret key generation
- Token hashing for storage
- Automatic token rotation
- Token expiration
- Revocation on logout
- Login attempt tracking
- Security headers middleware

### 🔧 **Recommended for Production**
- Use HTTPS only
- Implement rate limiting
- Monitor failed login attempts
- Regular security audits
- Token blacklisting for compromised tokens

## Background Tasks

The system includes automatic cleanup tasks:

- **Expired Token Cleanup**: Removes expired refresh tokens every hour
- **Login Attempt Cleanup**: Removes old login attempts (30+ days)
- **Production Only**: Background tasks only run in production environment

## Error Handling

### Common Error Responses

#### Invalid Token (401)
```json
{
  "detail": "Invalid access token"
}
```

#### Expired Token (401)
```json
{
  "detail": "Access token expired"
}
```

#### Invalid Refresh Token (401)
```json
{
  "detail": "Invalid or expired refresh token"
}
```

## Testing

Use the provided test endpoints to verify the system:

```bash
# Test login
curl -X POST "http://localhost:8000/api/v1/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"user_id": "test123", "name": "Test User"}'

# Test protected endpoint
curl -X GET "http://localhost:8000/api/v1/auth/me" \
  -H "Authorization: Bearer <access_token>"

# Test token refresh
curl -X POST "http://localhost:8000/api/v1/auth/refresh" \
  -H "Content-Type: application/json" \
  -d '{"refresh_token": "<refresh_token>"}'
```

## Monitoring

Monitor these metrics in production:

- Token rotation frequency
- Failed login attempts
- Token expiration rates
- API endpoint usage with authentication
- Background task execution
