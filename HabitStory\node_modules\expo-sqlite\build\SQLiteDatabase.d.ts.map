{"version": 3, "file": "SQLiteDatabase.d.ts", "sourceRoot": "", "sources": ["../src/SQLiteDatabase.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,KAAK,iBAAiB,EAAE,MAAM,mBAAmB,CAAC;AAI3D,OAAO,EAAsB,cAAc,EAAE,iBAAiB,EAAE,MAAM,kBAAkB,CAAC;AACzF,OAAO,EAAE,aAAa,EAAE,MAAM,iBAAiB,CAAC;AAChD,OAAO,EACL,gBAAgB,EAGhB,eAAe,EACf,eAAe,EACf,wBAAwB,EACzB,MAAM,mBAAmB,CAAC;AAG3B,OAAO,EAAE,iBAAiB,EAAE,CAAC;AAE7B;;GAEG;AACH,qBAAa,cAAc;aAEP,YAAY,EAAE,MAAM;aACpB,OAAO,EAAE,iBAAiB;aAC1B,cAAc,EAAE,cAAc;gBAF9B,YAAY,EAAE,MAAM,EACpB,OAAO,EAAE,iBAAiB,EAC1B,cAAc,EAAE,cAAc;IAGhD;;OAEG;IACI,oBAAoB,IAAI,OAAO,CAAC,OAAO,CAAC;IAI/C;;OAEG;IACI,UAAU,IAAI,OAAO,CAAC,IAAI,CAAC;IAIlC;;;;;OAKG;IACI,SAAS,CAAC,MAAM,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAI/C;;;;OAIG;IACI,cAAc,CAAC,YAAY,GAAE,MAAe,GAAG,OAAO,CAAC,UAAU,CAAC;IAIzE;;;;OAIG;IACU,YAAY,CAAC,MAAM,EAAE,MAAM,GAAG,OAAO,CAAC,eAAe,CAAC;IAMnE;;;;OAIG;IACU,kBAAkB,CAAC,MAAM,GAAE,MAAe,GAAG,OAAO,CAAC,aAAa,CAAC;IAMhF;;;;;;;;;;;;;;;;;;;;;;;OAuBG;IACU,oBAAoB,CAAC,IAAI,EAAE,MAAM,OAAO,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC;IAW3E;;;;;;;;;;;;;;;;;;;;;;OAsBG;IACU,6BAA6B,CACxC,IAAI,EAAE,CAAC,GAAG,EAAE,WAAW,KAAK,OAAO,CAAC,IAAI,CAAC,GACxC,OAAO,CAAC,IAAI,CAAC;IAqBhB;;OAEG;IACI,mBAAmB,IAAI,OAAO;IAIrC;;OAEG;IACI,SAAS,IAAI,IAAI;IAIxB;;;;;;;;OAQG;IACI,QAAQ,CAAC,MAAM,EAAE,MAAM,GAAG,IAAI;IAIrC;;;;;;OAMG;IACI,aAAa,CAAC,YAAY,GAAE,MAAe,GAAG,UAAU;IAI/D;;;;;;OAMG;IACI,WAAW,CAAC,MAAM,EAAE,MAAM,GAAG,eAAe;IAMnD;;;;;;;OAOG;IACI,iBAAiB,CAAC,MAAM,GAAE,MAAe,GAAG,aAAa;IAMhE;;;;;;OAMG;IACI,mBAAmB,CAAC,IAAI,EAAE,MAAM,IAAI,GAAG,IAAI;IAalD;;;;OAIG;IACI,QAAQ,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,gBAAgB,GAAG,OAAO,CAAC,eAAe,CAAC;IAEnF;;OAEG;IACI,QAAQ,CAAC,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,EAAE,wBAAwB,GAAG,OAAO,CAAC,eAAe,CAAC;IAY9F;;;;OAIG;IACI,aAAa,CAAC,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,gBAAgB,GAAG,OAAO,CAAC,CAAC,GAAG,IAAI,CAAC;IACpF;;OAEG;IACI,aAAa,CAAC,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,EAAE,wBAAwB,GAAG,OAAO,CAAC,CAAC,GAAG,IAAI,CAAC;IAa/F;;;;;OAKG;IACI,YAAY,CAAC,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,gBAAgB,GAAG,qBAAqB,CAAC,CAAC,CAAC;IAC1F;;OAEG;IACI,YAAY,CAAC,CAAC,EACnB,MAAM,EAAE,MAAM,EACd,GAAG,MAAM,EAAE,wBAAwB,GAClC,qBAAqB,CAAC,CAAC,CAAC;IAa3B;;;;;;;;;;;;;;;OAeG;IACI,WAAW,CAAC,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,gBAAgB,GAAG,OAAO,CAAC,CAAC,EAAE,CAAC;IAC7E;;OAEG;IACI,WAAW,CAAC,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,EAAE,wBAAwB,GAAG,OAAO,CAAC,CAAC,EAAE,CAAC;IAaxF;;;;;OAKG;IACI,OAAO,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,gBAAgB,GAAG,eAAe;IACzE;;OAEG;IACI,OAAO,CAAC,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,EAAE,wBAAwB,GAAG,eAAe;IAYpF;;;;;OAKG;IACI,YAAY,CAAC,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,gBAAgB,GAAG,CAAC,GAAG,IAAI;IAC1E;;OAEG;IACI,YAAY,CAAC,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,EAAE,wBAAwB,GAAG,CAAC,GAAG,IAAI;IAarF;;;;;;OAMG;IACI,WAAW,CAAC,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,gBAAgB,GAAG,gBAAgB,CAAC,CAAC,CAAC;IACpF;;OAEG;IACI,WAAW,CAAC,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,EAAE,wBAAwB,GAAG,gBAAgB,CAAC,CAAC,CAAC;IAa/F;;;;;OAKG;IACI,UAAU,CAAC,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,gBAAgB,GAAG,CAAC,EAAE;IACnE;;OAEG;IACI,UAAU,CAAC,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,EAAE,wBAAwB,GAAG,CAAC,EAAE;IAa9E;;;OAGG;IACI,UAAU,IAAI,OAAO,CAAC,IAAI,CAAC;CAQnC;AAED;;GAEG;AACH,eAAO,MAAM,wBAAwB,KAAsC,CAAC;AAE5E;;;;;;GAMG;AACH,wBAAsB,iBAAiB,CACrC,YAAY,EAAE,MAAM,EACpB,OAAO,CAAC,EAAE,iBAAiB,EAC3B,SAAS,CAAC,EAAE,MAAM,GACjB,OAAO,CAAC,cAAc,CAAC,CAUzB;AAED;;;;;;;;GAQG;AACH,wBAAgB,gBAAgB,CAC9B,YAAY,EAAE,MAAM,EACpB,OAAO,CAAC,EAAE,iBAAiB,EAC3B,SAAS,CAAC,EAAE,MAAM,GACjB,cAAc,CAUhB;AAED;;;;;GAKG;AACH,wBAAsB,wBAAwB,CAC5C,cAAc,EAAE,UAAU,EAC1B,OAAO,CAAC,EAAE,iBAAiB,GAC1B,OAAO,CAAC,cAAc,CAAC,CASzB;AAED;;;;;;;GAOG;AACH,wBAAgB,uBAAuB,CACrC,cAAc,EAAE,UAAU,EAC1B,OAAO,CAAC,EAAE,iBAAiB,GAC1B,cAAc,CAShB;AAED;;;;;GAKG;AACH,wBAAsB,mBAAmB,CAAC,YAAY,EAAE,MAAM,EAAE,SAAS,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,CAGjG;AAED;;;;;;;GAOG;AACH,wBAAgB,kBAAkB,CAAC,YAAY,EAAE,MAAM,EAAE,SAAS,CAAC,EAAE,MAAM,GAAG,IAAI,CAGjF;AAED;;;;;;;;;;GAUG;AACH,wBAAgB,mBAAmB,CAAC,EAClC,cAAc,EACd,kBAAkB,EAClB,YAAY,EACZ,gBAAgB,GACjB,EAAE;IACD,cAAc,EAAE,cAAc,CAAC;IAC/B,kBAAkB,CAAC,EAAE,MAAM,CAAC;IAC5B,YAAY,EAAE,cAAc,CAAC;IAC7B,gBAAgB,CAAC,EAAE,MAAM,CAAC;CAC3B,GAAG,OAAO,CAAC,IAAI,CAAC,CAOhB;AAED;;;;;;;;;;;;GAYG;AACH,wBAAgB,kBAAkB,CAAC,EACjC,cAAc,EACd,kBAAkB,EAClB,YAAY,EACZ,gBAAgB,GACjB,EAAE;IACD,cAAc,EAAE,cAAc,CAAC;IAC/B,kBAAkB,CAAC,EAAE,MAAM,CAAC;IAC5B,YAAY,EAAE,cAAc,CAAC;IAC7B,gBAAgB,CAAC,EAAE,MAAM,CAAC;CAC3B,GAAG,IAAI,CAOP;AAED;;GAEG;AACH,MAAM,MAAM,mBAAmB,GAAG;IAChC,+HAA+H;IAC/H,YAAY,EAAE,MAAM,CAAC;IAErB,8CAA8C;IAC9C,gBAAgB,EAAE,MAAM,CAAC;IAEzB,sBAAsB;IACtB,SAAS,EAAE,MAAM,CAAC;IAElB,0BAA0B;IAC1B,KAAK,EAAE,MAAM,CAAC;CACf,CAAC;AAEF;;;;;;GAMG;AACH,wBAAgB,yBAAyB,CACvC,QAAQ,EAAE,CAAC,KAAK,EAAE,mBAAmB,KAAK,IAAI,GAC7C,iBAAiB,CAEnB;AAED;;;GAGG;AACH,cAAM,WAAY,SAAQ,cAAc;WAClB,WAAW,CAAC,EAAE,EAAE,cAAc,GAAG,OAAO,CAAC,WAAW,CAAC;CAS1E"}