# HabitStory Backend Environment Variables
# Copy this file to .env and fill in your actual values

# Gemini API Configuration
GEMINI_API_KEY=your_gemini_api_key_here

# Security Configuration
SECRET_KEY=your_secure_secret_key_here_minimum_32_characters

# Database Configuration (optional, defaults to SQLite)
# DATABASE_URL=sqlite+aiosqlite:///./habitstory.db

# Environment Configuration
# ENVIRONMENT=production
# DEBUG=false
# LOG_LEVEL=INFO

# CORS Configuration (optional)
# ALLOWED_ORIGINS=["https://yourdomain.com"]

# Rate Limiting (optional)
# RATE_LIMIT_REQUESTS_PER_MINUTE=60
# RATE_LIMIT_REQUESTS_PER_HOUR=1000
