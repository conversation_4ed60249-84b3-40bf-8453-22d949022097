"""
FastAPI router for journal entries endpoints.
"""

from fastapi import APIRouter, HTTPException, Depends, status
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import BaseModel, Field
from typing import Dict, List, Any, Optional
from datetime import datetime
import logging

from ..database import get_db
from ..services.pipeline import weekly_report_pipeline
from ..services.ai_client import unified_ai_service

logger = logging.getLogger(__name__)

router = APIRouter()


class EntryRequest(BaseModel):
    """Request model for journal entry processing."""
    text: str = Field(..., description="Journal entry text")
    date: Optional[str] = Field(None, description="Entry date (ISO format)")
    user_id: str = Field(..., description="User identifier")
    user_traits: Optional[Dict[str, Any]] = Field(None, description="User personality traits")


class EntryResponse(BaseModel):
    """Response model for processed journal entry."""
    success: bool
    parsed_data: Dict[str, Any]
    daily_feedback: Dict[str, Any]
    tokens_used: Optional[int] = None
    processing_time: Optional[float] = None


class BatchEntryRequest(BaseModel):
    """Request model for batch entry processing."""
    entries: List[Dict[str, Any]] = Field(..., description="List of journal entries")
    user_id: str = Field(..., description="User identifier")
    user_traits: Optional[Dict[str, Any]] = Field(None, description="User personality traits")


@router.post("/entries/parse", response_model=EntryResponse)
async def parse_entry(
    request: EntryRequest,
    db: AsyncSession = Depends(get_db)
):
    """
    Parse a single journal entry and provide daily feedback.
    
    This endpoint:
    1. Parses the entry text to extract habits, metrics, and insights
    2. Updates user traits based on communication patterns
    3. Provides immediate daily feedback
    4. Tracks token usage
    """
    try:
        start_time = datetime.now()
        
        # Validate input
        if not request.text.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Entry text cannot be empty"
            )
        
        # Check token limits (placeholder - implement with database integration)
        # token_status = await check_token_limit(request.user_id)
        # For now, allow all requests
        
        # Create entry object
        entry = {
            "text": request.text,
            "date": request.date or datetime.now().isoformat(),
            "user_id": request.user_id
        }
        
        # Generate daily feedback (includes parsing)
        daily_feedback = await weekly_report_pipeline.generate_daily_feedback(
            request.user_id,
            entry,
            request.user_traits
        )
        
        # Calculate processing time
        processing_time = (datetime.now() - start_time).total_seconds()
        
        return EntryResponse(
            success=True,
            parsed_data=daily_feedback.get("parsed_data", {}),
            daily_feedback={
                "insights": daily_feedback.get("insights", []),
                "encouragement": daily_feedback.get("encouragement", ""),
                "reflection_prompt": daily_feedback.get("reflection_prompt", "")
            },
            processing_time=processing_time
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error parsing entry: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to process entry: {str(e)}"
        )


@router.post("/entries/batch-parse")
async def batch_parse_entries(
    request: BatchEntryRequest,
    db: AsyncSession = Depends(get_db)
):
    """
    Parse multiple journal entries in batch.
    
    This endpoint is useful for:
    1. Initial data import
    2. Bulk processing of historical entries
    3. Preparing data for weekly report generation
    """
    try:
        if not request.entries:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No entries provided"
            )
        
        if len(request.entries) > 50:  # Limit batch size
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Batch size limited to 50 entries"
            )
        
        # Check token limits (placeholder - implement with database integration)
        # estimated_tokens = len(request.entries) * 500  # Rough estimate
        # For now, allow all requests
        
        # Process entries
        processed_entries = []
        total_tokens = 0
        
        for i, entry in enumerate(request.entries):
            try:
                # Add user_id to entry
                entry["user_id"] = request.user_id
                
                # Generate feedback for each entry
                daily_feedback = await weekly_report_pipeline.generate_daily_feedback(
                    request.user_id,
                    entry,
                    request.user_traits
                )
                
                processed_entry = {
                    "index": i,
                    "original": entry,
                    "parsed_data": daily_feedback.get("parsed_data", {}),
                    "success": True
                }
                processed_entries.append(processed_entry)
                
            except Exception as e:
                logger.warning(f"Error processing entry {i}: {e}")
                processed_entries.append({
                    "index": i,
                    "original": entry,
                    "error": str(e),
                    "success": False
                })
        
        successful_count = sum(1 for entry in processed_entries if entry["success"])
        
        return {
            "success": True,
            "processed_count": len(processed_entries),
            "successful_count": successful_count,
            "failed_count": len(processed_entries) - successful_count,
            "entries": processed_entries,
            "tokens_used": total_tokens
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in batch processing: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Batch processing failed: {str(e)}"
        )


@router.get("/entries/{user_id}/token-usage")
async def get_token_usage(
    user_id: str,
    db: AsyncSession = Depends(get_db)
):
    """
    Get current token usage for a user.
    
    Returns:
    - Current month usage
    - Remaining tokens
    - Plan limits
    - Usage percentage
    """
    try:
        # Placeholder token usage - implement with database integration
        return {
            "user_id": user_id,
            "current_usage": 0,
            "monthly_limit": 10000,
            "remaining": 10000,
            "usage_percentage": 0.0,
            "plan": "FREE"
        }
        
    except Exception as e:
        logger.error(f"Error getting token usage: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get token usage: {str(e)}"
        )


@router.post("/entries/validate")
async def validate_entry_data(
    request: Dict[str, Any],
    db: AsyncSession = Depends(get_db)
):
    """
    Validate entry data for quality and consistency.

    This endpoint can be used to:
    1. Check data quality before processing
    2. Identify potential issues
    3. Suggest improvements
    """
    try:
        user_id = request.get("user_id")
        entries = request.get("entries", [])

        if not user_id or not entries:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="user_id and entries are required"
            )

        # Import validation service
        from ..services.validation import validation_service

        # Validate the data
        validation_results = await validation_service.validate_weekly_data(
            user_id,
            entries,
            {},  # Empty stats for validation-only
            request.get("user_traits", {})
        )

        return {
            "success": True,
            "validation_results": validation_results,
            "data_quality_score": validation_results.get("data_quality_score", 0),
            "issues_found": len(validation_results.get("outliers", [])) +
                           len(validation_results.get("inconsistencies", [])),
            "recommendations": validation_results.get("recommendations", [])
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error validating entry data: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Validation failed: {str(e)}"
        )


@router.get("/entries/health")
async def health_check():
    """Health check endpoint for the entries service."""
    try:
        # Test AI API connection
        health_results = await unified_ai_service.health_check_all()
        api_healthy = any(health_results.values())
        
        return {
            "status": "healthy" if api_healthy else "degraded",
            "ai_services": health_results,
            "timestamp": datetime.now().isoformat(),
            "version": "1.0.0"
        }
        
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return {
            "status": "unhealthy",
            "gemini_api": "disconnected",
            "error": str(e),
            "timestamp": datetime.now().isoformat(),
            "version": "1.0.0"
        }
