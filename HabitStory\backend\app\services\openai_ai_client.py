"""
OpenAI AI client implementation using the unified interface.
"""

import httpx
import asyncio
import json
import logging
from typing import Dict, List, Any, Optional

from .ai_client import BaseAIClient, AIProvider, AIMessage, AIResponse
from ..config import settings

logger = logging.getLogger(__name__)


class OpenAITokenCounter:
    """Token counting utility for OpenAI."""
    
    def __init__(self):
        # Use a simple approximation for token counting
        # 1 token ≈ 4 characters for most text (rough estimate)
        self.chars_per_token = 4
    
    def count_tokens(self, text: str) -> int:
        """Count approximate tokens in text."""
        return len(text) // self.chars_per_token


class OpenAIClient(BaseAIClient):
    """OpenAI AI client implementation."""
    
    def __init__(self, api_key: Optional[str] = None):
        super().__init__(AIProvider.OPENAI)
        self.api_key = api_key or settings.openai_api_key if hasattr(settings, 'openai_api_key') else None
        self.api_url = "https://api.openai.com/v1/chat/completions"
        self.timeout = 30
        self.max_retries = 3
        self.token_counter = OpenAITokenCounter()
        
        if not self.api_key:
            logger.warning("OpenAI API key not configured")
    
    def count_tokens(self, text: str) -> int:
        """Count tokens in the given text."""
        return self.token_counter.count_tokens(text)
    
    def _messages_to_openai_format(self, messages: List[AIMessage]) -> List[Dict[str, str]]:
        """Convert AIMessage objects to OpenAI format."""
        return [message.to_dict() for message in messages]
    
    async def _make_request(
        self,
        messages: List[Dict[str, str]],
        temperature: float = 0.7,
        max_tokens: int = 2048,
        functions: Optional[List[Dict]] = None,
        model: str = "gpt-3.5-turbo"
    ) -> Dict[str, Any]:
        """Make a single request to OpenAI API."""
        
        payload = {
            "model": model,
            "messages": messages,
            "temperature": temperature,
            "max_tokens": max_tokens,
            "top_p": 1,
            "frequency_penalty": 0,
            "presence_penalty": 0,
        }
        
        # Add function calling if provided
        if functions:
            payload["functions"] = functions
            payload["function_call"] = "auto"
        
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
        }
        
        async with httpx.AsyncClient(timeout=self.timeout) as client:
            response = await client.post(
                self.api_url,
                json=payload,
                headers=headers
            )
            response.raise_for_status()
            return response.json()
    
    async def generate_response(
        self,
        messages: List[AIMessage],
        temperature: float = 0.7,
        max_tokens: int = 2048,
        functions: Optional[List[Dict]] = None,
        user_id: Optional[str] = None
    ) -> AIResponse:
        """Generate a response from OpenAI."""
        if not self.api_key:
            raise ValueError("OpenAI API key not configured")
        
        # Convert messages to OpenAI format
        openai_messages = self._messages_to_openai_format(messages)
        
        # Count input tokens
        input_text = " ".join([msg["content"] for msg in openai_messages])
        input_tokens = self.count_tokens(input_text)
        
        # Track token usage if user_id provided
        if user_id:
            await self._track_token_usage(user_id, input_tokens, "input")
        
        last_exception = None
        
        for attempt in range(self.max_retries + 1):
            try:
                logger.info(f"OpenAI API call attempt {attempt + 1}/{self.max_retries + 1}")
                
                response_data = await self._make_request(
                    openai_messages,
                    temperature=temperature,
                    max_tokens=max_tokens,
                    functions=functions
                )
                
                # Extract response content
                if (response_data.get("choices") and 
                    len(response_data["choices"]) > 0 and
                    response_data["choices"][0].get("message") and
                    response_data["choices"][0]["message"].get("content")):
                    
                    content = response_data["choices"][0]["message"]["content"].strip()
                    
                    # Get token usage from response if available
                    usage = response_data.get("usage", {})
                    prompt_tokens = usage.get("prompt_tokens", input_tokens)
                    completion_tokens = usage.get("completion_tokens", self.count_tokens(content))
                    total_tokens = usage.get("total_tokens", prompt_tokens + completion_tokens)
                    
                    # Track output token usage
                    if user_id:
                        await self._track_token_usage(user_id, completion_tokens, "output")
                    
                    # Determine model used
                    model = response_data.get("model", "gpt-3.5-turbo")
                    
                    # Create response
                    return AIResponse(
                        content=content,
                        provider=self.provider,
                        model=model,
                        tokens_used=total_tokens,
                        input_tokens=prompt_tokens,
                        output_tokens=completion_tokens,
                        metadata={
                            "attempt": attempt + 1,
                            "raw_response": response_data,
                            "usage": usage
                        }
                    )
                else:
                    raise ValueError("Invalid response format from OpenAI API")
                    
            except httpx.HTTPStatusError as e:
                last_exception = e
                if e.response.status_code == 401:
                    logger.error("Invalid API key")
                    raise ValueError("Invalid OpenAI API key")
                elif e.response.status_code == 429:  # Rate limit
                    wait_time = 2 ** attempt  # Exponential backoff
                    logger.warning(f"Rate limited, waiting {wait_time} seconds")
                    await asyncio.sleep(wait_time)
                elif e.response.status_code == 500:
                    logger.error("OpenAI service error")
                    if attempt == self.max_retries:
                        raise ValueError("OpenAI service is temporarily unavailable")
                    await asyncio.sleep(2 ** attempt)
                else:
                    logger.error(f"HTTP error {e.response.status_code}: {e.response.text}")
                    if attempt == self.max_retries:
                        raise
                    await asyncio.sleep(2 ** attempt)
                    
            except (httpx.RequestError, asyncio.TimeoutError) as e:
                last_exception = e
                if attempt == self.max_retries:
                    logger.error(f"Request failed after {self.max_retries + 1} attempts: {e}")
                    raise ValueError(f"Failed to connect to OpenAI API: {e}")
                
                wait_time = 2 ** attempt
                logger.warning(f"Request failed, retrying in {wait_time} seconds: {e}")
                await asyncio.sleep(wait_time)
                
            except Exception as e:
                last_exception = e
                logger.error(f"Unexpected error: {e}")
                if attempt == self.max_retries:
                    raise
                await asyncio.sleep(2 ** attempt)
        
        # If we get here, all retries failed
        raise ValueError(f"OpenAI API call failed after {self.max_retries + 1} attempts: {last_exception}")
    
    async def health_check(self) -> bool:
        """Check if OpenAI API is healthy."""
        try:
            test_messages = [
                AIMessage("user", "Hello, please respond with just the word 'success' to test the connection.")
            ]
            
            response = await self.generate_response(
                messages=test_messages,
                temperature=0,
                max_tokens=10,
                user_id="health_check"
            )
            
            return "success" in response.content.lower()
            
        except Exception as e:
            logger.error(f"OpenAI health check failed: {e}")
            return False
    
    async def _track_token_usage(self, user_id: str, tokens: int, token_type: str):
        """Track token usage for a user (placeholder for database integration)."""
        # This will be implemented when we add the database models
        logger.info(f"Token usage - User: {user_id}, Tokens: {tokens}, Type: {token_type}")


# Create global OpenAI client instance (will be None if no API key)
openai_ai_client = None
try:
    openai_ai_client = OpenAIClient()
    if not openai_ai_client.api_key:
        openai_ai_client = None
except Exception as e:
    logger.warning(f"Failed to initialize OpenAI client: {e}")
    openai_ai_client = None
