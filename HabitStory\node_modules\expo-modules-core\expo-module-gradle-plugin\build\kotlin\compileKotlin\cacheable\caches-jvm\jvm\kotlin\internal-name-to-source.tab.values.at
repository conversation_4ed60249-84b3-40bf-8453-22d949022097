/ Header Record For PersistentHashMapValueStorage> =src/main/kotlin/expo/modules/plugin/AutolinkingIntegration.kt? >src/main/kotlin/expo/modules/plugin/ExpoModulesGradlePlugin.kt? >src/main/kotlin/expo/modules/plugin/ExpoModulesGradlePlugin.kt@ ?src/main/kotlin/expo/modules/plugin/ExtraPropertiesExtension.kt? >src/main/kotlin/expo/modules/plugin/ExpoModulesGradlePlugin.kt< ;src/main/kotlin/expo/modules/plugin/ProjectConfiguration.kt< ;src/main/kotlin/expo/modules/plugin/ProjectConfiguration.kt< ;src/main/kotlin/expo/modules/plugin/ProjectConfiguration.kt/ .src/main/kotlin/expo/modules/plugin/Version.kt/ .src/main/kotlin/expo/modules/plugin/Version.kt0 /src/main/kotlin/expo/modules/plugin/Warnings.ktG Fsrc/main/kotlin/expo/modules/plugin/android/AndroidLibraryExtension.ktG Fsrc/main/kotlin/expo/modules/plugin/android/AndroidLibraryExtension.ktG Fsrc/main/kotlin/expo/modules/plugin/android/AndroidLibraryExtension.ktI Hsrc/main/kotlin/expo/modules/plugin/android/MavenPublicationExtension.ktI Hsrc/main/kotlin/expo/modules/plugin/android/MavenPublicationExtension.ktI Hsrc/main/kotlin/expo/modules/plugin/android/MavenPublicationExtension.ktI Hsrc/main/kotlin/expo/modules/plugin/android/MavenPublicationExtension.ktI Hsrc/main/kotlin/expo/modules/plugin/android/MavenPublicationExtension.ktI Hsrc/main/kotlin/expo/modules/plugin/android/MavenPublicationExtension.ktI Hsrc/main/kotlin/expo/modules/plugin/android/MavenPublicationExtension.ktI Hsrc/main/kotlin/expo/modules/plugin/android/MavenPublicationExtension.ktI Hsrc/main/kotlin/expo/modules/plugin/android/MavenPublicationExtension.ktI Hsrc/main/kotlin/expo/modules/plugin/android/MavenPublicationExtension.ktI Hsrc/main/kotlin/expo/modules/plugin/android/MavenPublicationExtension.ktI Hsrc/main/kotlin/expo/modules/plugin/android/MavenPublicationExtension.ktI Hsrc/main/kotlin/expo/modules/plugin/android/MavenPublicationExtension.ktI Hsrc/main/kotlin/expo/modules/plugin/android/MavenPublicationExtension.ktI Hsrc/main/kotlin/expo/modules/plugin/android/MavenPublicationExtension.ktI Hsrc/main/kotlin/expo/modules/plugin/android/MavenPublicationExtension.ktI Hsrc/main/kotlin/expo/modules/plugin/android/MavenPublicationExtension.ktI Hsrc/main/kotlin/expo/modules/plugin/android/MavenPublicationExtension.ktI Hsrc/main/kotlin/expo/modules/plugin/android/MavenPublicationExtension.ktI Hsrc/main/kotlin/expo/modules/plugin/android/MavenPublicationExtension.ktI Hsrc/main/kotlin/expo/modules/plugin/android/MavenPublicationExtension.ktH Gsrc/main/kotlin/expo/modules/plugin/gradle/ExpoGradleHelperExtension.ktH Gsrc/main/kotlin/expo/modules/plugin/gradle/ExpoGradleHelperExtension.ktB Asrc/main/kotlin/expo/modules/plugin/gradle/ExpoModuleExtension.ktB Asrc/main/kotlin/expo/modules/plugin/gradle/ExpoModuleExtension.ktB Asrc/main/kotlin/expo/modules/plugin/gradle/ExpoModuleExtension.ktS Rsrc/withAutolinkingPlugin/kotlin/expo/modules/plugin/AutolinkingIntegrationImpl.kt