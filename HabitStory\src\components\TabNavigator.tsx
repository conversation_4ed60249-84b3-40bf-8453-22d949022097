
import React from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { Text, View, StyleSheet, Animated } from 'react-native';
import DashboardScreen from '../screens/Dashboard';
import JournalScreen from '../screens/JournalScreen';
import HistoryScreen from '../screens/HistoryScreen';
import ReportScreen from '../screens/ReportScreen';
import SettingsScreenSimple from '../screens/SettingsScreenSimple';


const Tab = createBottomTabNavigator();

// Enhanced icon component with animations and better styling
const TabIcon: React.FC<{ name: string; focused: boolean }> = ({ name, focused }) => {
  const scaleValue = React.useRef(new Animated.Value(focused ? 1.1 : 1)).current;

  React.useEffect(() => {
    Animated.spring(scaleValue, {
      toValue: focused ? 1.1 : 1,
      useNativeDriver: true,
      tension: 100,
      friction: 8,
    }).start();
  }, [focused, scaleValue]);

  const getIcon = () => {
    switch (name) {
      case 'Dashboard':
        return '🏠';
      case 'Journal':
        return '📝';
      case 'History':
        return '📚';
      case 'Reports':
        return '📊';
      case 'Settings':
        return '⚙️';
      default:
        return '📱';
    }
  };

  const getLabel = () => {
    switch (name) {
      case 'Dashboard':
        return 'Home';
      case 'Journal':
        return 'Journal';
      case 'History':
        return 'History';
      case 'Reports':
        return 'Reports';
      case 'Settings':
        return 'Settings';
      default:
        return name;
    }
  };

  return (
    <View style={styles.tabContainer}>
      <Animated.View
        style={[
          styles.iconContainer,
          { transform: [{ scale: scaleValue }] },
          focused ? styles.iconContainerFocused : null
        ]}
      >
        <Text style={{ fontSize: 24 }}>
          {getIcon()}
        </Text>
      </Animated.View>
      <Text style={[styles.label, focused ? styles.labelFocused : styles.labelDefault]}>
        {getLabel()}
      </Text>
      {focused && <View style={styles.indicator} />}
    </View>
  );
};

const TabNavigator: React.FC = () => {
  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused }) => (
          <TabIcon name={route.name} focused={focused} />
        ),
        tabBarShowLabel: false,
        tabBarStyle: {
          height: 80,
          paddingBottom: 10,
          paddingTop: 10,
          backgroundColor: 'white',
          borderTopWidth: 1,
          borderTopColor: '#e5e7eb',
        },
        headerShown: false,
      })}
    >
      <Tab.Screen
        name="Dashboard"
        component={DashboardScreen}
        options={{
          tabBarLabel: 'Dashboard',
        }}
      />
      <Tab.Screen
        name="Journal"
        component={JournalScreen}
        options={{
          tabBarLabel: 'Journal',
        }}
      />
      <Tab.Screen
        name="History"
        component={HistoryScreen}
        options={{
          tabBarLabel: 'History',
        }}
      />
      <Tab.Screen
        name="Reports"
        component={ReportScreen}
        options={{
          tabBarLabel: 'Reports',
        }}
      />
      <Tab.Screen
        name="Settings"
        component={SettingsScreenSimple}
        options={{
          tabBarLabel: 'Settings',
        }}
      />
    </Tab.Navigator>
  );
};

const styles = StyleSheet.create({
  tabContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
  },
  iconContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    width: 48,
    height: 48,
    borderRadius: 24,
  },
  iconContainerFocused: {
    backgroundColor: '#dbeafe',
  },
  label: {
    fontSize: 12,
    marginTop: 4,
    fontWeight: '500',
  },
  labelFocused: {
    color: '#2563eb',
  },
  labelDefault: {
    color: '#6b7280',
  },
  indicator: {
    position: 'absolute',
    bottom: -4,
    width: 4,
    height: 4,
    backgroundColor: '#2563eb',
    borderRadius: 2,
  },
});

export default TabNavigator;
