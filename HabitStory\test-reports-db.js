// Test script to check reports in database
const { getAllReports, insertReport } = require('./src/lib/db');

async function testReportsDatabase() {
  console.log('🔍 Testing Reports Database...\n');

  try {
    // Note: This test won't work in Node.js environment since it requires React Native
    // This is just to show the structure of how reports would be tested
    
    console.log('📊 Checking Reports in Database:');
    console.log('- getAllReports() should return array of reports');
    console.log('- Each report should have: id, week_start, week_end, html_content, created_at');
    
    console.log('\n📝 Report Structure Expected:');
    console.log(`{
  id: number,
  week_start: string,
  week_end: string, 
  html_content: string,
  created_at: string,
  feedback_rating: number | null,
  feedback_text: string | null
}`);
    
    console.log('\n🔧 Debugging Steps:');
    console.log('1. Check if reports table exists');
    console.log('2. Check if any reports have been generated');
    console.log('3. Verify html_content is not null/empty');
    console.log('4. Verify reportId parameter is passed correctly');
    
    console.log('\n🎯 Common Issues:');
    console.log('❌ No reports generated yet');
    console.log('❌ Report exists but html_content is null/empty');
    console.log('❌ reportId parameter not passed correctly');
    console.log('❌ Database query failing silently');
    
    console.log('\n💡 Solutions:');
    console.log('✅ Generate a test report first');
    console.log('✅ Check console logs for debugging info');
    console.log('✅ Verify navigation parameters');
    console.log('✅ Check if Gemini API is working');
    
    console.log('\n📱 To test in the app:');
    console.log('1. Go to Reports tab');
    console.log('2. Click "Generate This Week\'s Report"');
    console.log('3. Wait for generation to complete');
    console.log('4. Click "View Report" in the alert');
    console.log('5. Check console logs for debugging info');

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Run the test
testReportsDatabase();
