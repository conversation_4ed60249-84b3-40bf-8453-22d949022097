"""
FastAPI router for weekly reports endpoints.
"""

from fastapi import APIRouter, HTTPException, Depends, status, Response
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import BaseModel, Field
from typing import Dict, List, Any, Optional
from datetime import datetime, date
import logging

from ..database import get_db
from ..services.pipeline import weekly_report_pipeline
from ..services.gemini_client import gemini_client
from ..models.reports import WeeklyReport
from ..models.traits import UserTraits

logger = logging.getLogger(__name__)

router = APIRouter()


class WeeklyReportRequest(BaseModel):
    """Request model for weekly report generation."""
    user_id: str = Field(..., description="User identifier")
    entries: List[Dict[str, Any]] = Field(..., description="Journal entries for the week")
    week_start: str = Field(..., description="Week start date (ISO format)")
    week_end: str = Field(..., description="Week end date (ISO format)")
    user_traits: Optional[Dict[str, Any]] = Field(None, description="User personality traits")
    previous_feedback: Optional[str] = Field(None, description="Previous feedback to incorporate")


class WeeklyReportResponse(BaseModel):
    """Response model for weekly report generation."""
    success: bool
    report_id: Optional[int] = None
    html_content: Optional[str] = None
    generation_time: Optional[float] = None
    tokens_used: Optional[int] = None
    metadata: Optional[Dict[str, Any]] = None
    error: Optional[str] = None


@router.post("/reports/weekly", response_model=WeeklyReportResponse)
async def generate_weekly_report(
    request: WeeklyReportRequest,
    db: AsyncSession = Depends(get_db)
):
    """
    Generate a comprehensive weekly report using the 9-module pipeline.
    
    This endpoint:
    1. Validates user token limits
    2. Runs the complete 9-module pipeline
    3. Saves the report to the database
    4. Returns the generated HTML report
    """
    try:
        start_time = datetime.now()
        
        # Validate input
        if not request.entries:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No journal entries provided"
            )
        
        if len(request.entries) > 30:  # Reasonable limit for weekly entries
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Too many entries for weekly report (max 30)"
            )
        
        # Check token limits
        token_status = await gemini_client.check_token_limit(request.user_id)
        estimated_tokens = len(request.entries) * 200 + 2000  # Rough estimate for full pipeline
        
        if token_status["remaining"] < estimated_tokens:
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail="Insufficient tokens for weekly report generation. Please upgrade your plan."
            )
        
        # Get or create user traits
        user_traits = request.user_traits
        if not user_traits:
            # Try to get from database
            # For now, use default traits
            user_traits = {
                "tone": "informal",
                "style": "conversational",
                "traits": {"reflective": True},
                "trait_evidence": {"reflective": ["engages in journaling"]}
            }
        
        # Run the complete pipeline
        pipeline_results = await weekly_report_pipeline.generate_weekly_report(
            request.user_id,
            request.entries,
            user_traits,
            request.previous_feedback
        )
        
        # Check if pipeline was successful
        if not pipeline_results.get("final_report"):
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to generate report: " + str(pipeline_results.get("errors", []))
            )
        
        # Save report to database
        try:
            week_start_date = date.fromisoformat(request.week_start)
            week_end_date = date.fromisoformat(request.week_end)
            
            new_report = WeeklyReport(
                user_id=request.user_id,
                week_start=week_start_date,
                week_end=week_end_date,
                html_content=pipeline_results["final_report"],
                generation_time_seconds=int(pipeline_results.get("metadata", {}).get("total_execution_time", 0)),
                tokens_used=pipeline_results.get("metadata", {}).get("tokens_used")
            )
            
            db.add(new_report)
            await db.commit()
            await db.refresh(new_report)
            
            report_id = new_report.id
            
        except Exception as db_error:
            logger.warning(f"Failed to save report to database: {db_error}")
            report_id = None
        
        # Calculate total time
        total_time = (datetime.now() - start_time).total_seconds()
        
        return WeeklyReportResponse(
            success=True,
            report_id=report_id,
            html_content=pipeline_results["final_report"],
            generation_time=total_time,
            tokens_used=pipeline_results.get("metadata", {}).get("tokens_used"),
            metadata=pipeline_results.get("metadata", {})
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error generating weekly report: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate weekly report: {str(e)}"
        )


@router.get("/reports/{report_id}")
async def get_report(
    report_id: int,
    db: AsyncSession = Depends(get_db)
):
    """
    Retrieve a previously generated report by ID.
    """
    try:
        # Query report from database
        from sqlalchemy import select
        
        stmt = select(WeeklyReport).where(WeeklyReport.id == report_id)
        result = await db.execute(stmt)
        report = result.scalar_one_or_none()
        
        if not report:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Report not found"
            )
        
        return {
            "id": report.id,
            "user_id": report.user_id,
            "week_start": report.week_start.isoformat(),
            "week_end": report.week_end.isoformat(),
            "html_content": report.html_content,
            "generation_time_seconds": report.generation_time_seconds,
            "tokens_used": report.tokens_used,
            "created_at": report.created_at.isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving report: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve report: {str(e)}"
        )


@router.get("/reports/{report_id}/html")
async def get_report_html(
    report_id: int,
    db: AsyncSession = Depends(get_db)
):
    """
    Retrieve report HTML content directly for viewing.
    """
    try:
        from sqlalchemy import select
        
        stmt = select(WeeklyReport).where(WeeklyReport.id == report_id)
        result = await db.execute(stmt)
        report = result.scalar_one_or_none()
        
        if not report:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Report not found"
            )
        
        return Response(
            content=report.html_content,
            media_type="text/html"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving report HTML: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve report HTML: {str(e)}"
        )


@router.get("/reports/user/{user_id}")
async def get_user_reports(
    user_id: str,
    limit: int = 10,
    offset: int = 0,
    db: AsyncSession = Depends(get_db)
):
    """
    Get all reports for a specific user.
    """
    try:
        from sqlalchemy import select, desc
        
        stmt = (
            select(WeeklyReport)
            .where(WeeklyReport.user_id == user_id)
            .order_by(desc(WeeklyReport.created_at))
            .limit(limit)
            .offset(offset)
        )
        
        result = await db.execute(stmt)
        reports = result.scalars().all()
        
        return {
            "user_id": user_id,
            "reports": [
                {
                    "id": report.id,
                    "week_start": report.week_start.isoformat(),
                    "week_end": report.week_end.isoformat(),
                    "generation_time_seconds": report.generation_time_seconds,
                    "tokens_used": report.tokens_used,
                    "created_at": report.created_at.isoformat()
                }
                for report in reports
            ],
            "count": len(reports),
            "limit": limit,
            "offset": offset
        }
        
    except Exception as e:
        logger.error(f"Error retrieving user reports: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve user reports: {str(e)}"
        )


@router.delete("/reports/{report_id}")
async def delete_report(
    report_id: int,
    db: AsyncSession = Depends(get_db)
):
    """
    Delete a report by ID.
    """
    try:
        from sqlalchemy import select
        
        stmt = select(WeeklyReport).where(WeeklyReport.id == report_id)
        result = await db.execute(stmt)
        report = result.scalar_one_or_none()
        
        if not report:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Report not found"
            )
        
        await db.delete(report)
        await db.commit()
        
        return {"success": True, "message": "Report deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting report: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete report: {str(e)}"
        )


@router.post("/reports/preview")
async def preview_report_components(
    request: Dict[str, Any],
    db: AsyncSession = Depends(get_db)
):
    """
    Preview individual components of a report without generating the full report.
    Useful for testing and debugging.
    """
    try:
        user_id = request.get("user_id")
        entries = request.get("entries", [])
        component = request.get("component", "all")  # all, stats, correlations, story, etc.
        
        if not user_id or not entries:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="user_id and entries are required"
            )
        
        # Import services as needed
        from ..services.metrics import metrics_service
        from ..services.correlations import correlations_service
        from ..services.storytelling import storytelling_service
        
        user_traits = request.get("user_traits", {
            "tone": "informal",
            "style": "conversational",
            "traits": {"reflective": True}
        })
        
        preview_data = {}
        
        if component in ["all", "stats"]:
            preview_data["stats"] = metrics_service.calculate_weekly_stats(entries)
        
        if component in ["all", "correlations"]:
            preview_data["correlations"] = correlations_service.find_correlations(entries)
        
        if component in ["all", "story"]:
            stats = preview_data.get("stats") or metrics_service.calculate_weekly_stats(entries)
            correlations = preview_data.get("correlations") or correlations_service.find_correlations(entries)
            
            story = await storytelling_service.generate_weekly_story(
                user_id, entries, stats, correlations, user_traits
            )
            preview_data["story"] = story
        
        return {
            "success": True,
            "component": component,
            "preview_data": preview_data,
            "entries_count": len(entries)
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error generating preview: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate preview: {str(e)}"
        )
