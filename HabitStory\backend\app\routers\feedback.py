"""
FastAPI router for feedback endpoints.
"""

from fastapi import APIRouter, HTTPException, Depends, status
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import BaseModel, Field
from typing import Dict, List, Any, Optional
from datetime import datetime
import logging

from ..database import get_db
from ..models.feedback import ReportFeedback
from ..models.reports import WeeklyReport

logger = logging.getLogger(__name__)

router = APIRouter()


class FeedbackRequest(BaseModel):
    """Request model for report feedback."""
    report_id: int = Field(..., description="Report ID")
    sentiment: str = Field(..., description="Overall sentiment: positive, negative, neutral")
    rating: Optional[int] = Field(None, description="Overall rating (1-5)")
    comment: Optional[str] = Field(None, description="Detailed feedback comment")
    content_helpful: Optional[int] = Field(None, description="Content helpfulness (1-5)")
    tone_appropriate: Optional[int] = Field(None, description="Tone appropriateness (1-5)")
    insights_valuable: Optional[int] = Field(None, description="Insights value (1-5)")
    recommendations_useful: Optional[int] = Field(None, description="Recommendations usefulness (1-5)")


class FeedbackResponse(BaseModel):
    """Response model for feedback submission."""
    success: bool
    feedback_id: Optional[int] = None
    message: str


@router.post("/feedback", response_model=FeedbackResponse)
async def submit_feedback(
    request: FeedbackRequest,
    db: AsyncSession = Depends(get_db)
):
    """
    Submit feedback for a weekly report.
    
    This feedback will be used to improve future reports for the user.
    """
    try:
        # Validate sentiment
        valid_sentiments = ["positive", "negative", "neutral"]
        if request.sentiment not in valid_sentiments:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid sentiment. Must be one of: {valid_sentiments}"
            )
        
        # Validate ratings (1-5 scale)
        rating_fields = [
            request.rating,
            request.content_helpful,
            request.tone_appropriate,
            request.insights_valuable,
            request.recommendations_useful
        ]
        
        for rating in rating_fields:
            if rating is not None and (rating < 1 or rating > 5):
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Ratings must be between 1 and 5"
                )
        
        # Check if report exists
        from sqlalchemy import select
        
        stmt = select(WeeklyReport).where(WeeklyReport.id == request.report_id)
        result = await db.execute(stmt)
        report = result.scalar_one_or_none()
        
        if not report:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Report not found"
            )
        
        # Create feedback record
        feedback = ReportFeedback(
            report_id=request.report_id,
            sentiment=request.sentiment,
            rating=request.rating,
            comment=request.comment,
            content_helpful=request.content_helpful,
            tone_appropriate=request.tone_appropriate,
            insights_valuable=request.insights_valuable,
            recommendations_useful=request.recommendations_useful
        )
        
        db.add(feedback)
        await db.commit()
        await db.refresh(feedback)
        
        return FeedbackResponse(
            success=True,
            feedback_id=feedback.id,
            message="Feedback submitted successfully. Thank you for helping us improve!"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error submitting feedback: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to submit feedback: {str(e)}"
        )


@router.get("/feedback/report/{report_id}")
async def get_report_feedback(
    report_id: int,
    db: AsyncSession = Depends(get_db)
):
    """
    Get all feedback for a specific report.
    """
    try:
        from sqlalchemy import select
        
        stmt = select(ReportFeedback).where(ReportFeedback.report_id == report_id)
        result = await db.execute(stmt)
        feedback_list = result.scalars().all()
        
        return {
            "report_id": report_id,
            "feedback_count": len(feedback_list),
            "feedback": [
                {
                    "id": feedback.id,
                    "sentiment": feedback.sentiment,
                    "rating": feedback.rating,
                    "comment": feedback.comment,
                    "content_helpful": feedback.content_helpful,
                    "tone_appropriate": feedback.tone_appropriate,
                    "insights_valuable": feedback.insights_valuable,
                    "recommendations_useful": feedback.recommendations_useful,
                    "created_at": feedback.created_at.isoformat()
                }
                for feedback in feedback_list
            ]
        }
        
    except Exception as e:
        logger.error(f"Error retrieving feedback: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve feedback: {str(e)}"
        )


@router.get("/feedback/user/{user_id}/summary")
async def get_user_feedback_summary(
    user_id: str,
    db: AsyncSession = Depends(get_db)
):
    """
    Get feedback summary for a user across all their reports.
    """
    try:
        from sqlalchemy import select, func, join
        
        # Join feedback with reports to filter by user
        stmt = (
            select(
                func.count(ReportFeedback.id).label("total_feedback"),
                func.avg(ReportFeedback.rating).label("avg_rating"),
                func.avg(ReportFeedback.content_helpful).label("avg_content_helpful"),
                func.avg(ReportFeedback.tone_appropriate).label("avg_tone_appropriate"),
                func.avg(ReportFeedback.insights_valuable).label("avg_insights_valuable"),
                func.avg(ReportFeedback.recommendations_useful).label("avg_recommendations_useful"),
                func.count(
                    func.case([(ReportFeedback.sentiment == "positive", 1)])
                ).label("positive_count"),
                func.count(
                    func.case([(ReportFeedback.sentiment == "negative", 1)])
                ).label("negative_count"),
                func.count(
                    func.case([(ReportFeedback.sentiment == "neutral", 1)])
                ).label("neutral_count")
            )
            .select_from(
                join(ReportFeedback, WeeklyReport, ReportFeedback.report_id == WeeklyReport.id)
            )
            .where(WeeklyReport.user_id == user_id)
        )
        
        result = await db.execute(stmt)
        summary = result.first()
        
        if not summary or summary.total_feedback == 0:
            return {
                "user_id": user_id,
                "total_feedback": 0,
                "message": "No feedback available for this user"
            }
        
        return {
            "user_id": user_id,
            "total_feedback": summary.total_feedback,
            "average_ratings": {
                "overall": round(summary.avg_rating, 2) if summary.avg_rating else None,
                "content_helpful": round(summary.avg_content_helpful, 2) if summary.avg_content_helpful else None,
                "tone_appropriate": round(summary.avg_tone_appropriate, 2) if summary.avg_tone_appropriate else None,
                "insights_valuable": round(summary.avg_insights_valuable, 2) if summary.avg_insights_valuable else None,
                "recommendations_useful": round(summary.avg_recommendations_useful, 2) if summary.avg_recommendations_useful else None
            },
            "sentiment_distribution": {
                "positive": summary.positive_count,
                "negative": summary.negative_count,
                "neutral": summary.neutral_count
            }
        }
        
    except Exception as e:
        logger.error(f"Error retrieving feedback summary: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve feedback summary: {str(e)}"
        )


@router.get("/feedback/recent")
async def get_recent_feedback(
    limit: int = 10,
    db: AsyncSession = Depends(get_db)
):
    """
    Get recent feedback across all users (for admin/analytics purposes).
    """
    try:
        from sqlalchemy import select, desc, join
        
        stmt = (
            select(
                ReportFeedback.id,
                ReportFeedback.sentiment,
                ReportFeedback.rating,
                ReportFeedback.comment,
                ReportFeedback.created_at,
                WeeklyReport.user_id,
                WeeklyReport.week_start,
                WeeklyReport.week_end
            )
            .select_from(
                join(ReportFeedback, WeeklyReport, ReportFeedback.report_id == WeeklyReport.id)
            )
            .order_by(desc(ReportFeedback.created_at))
            .limit(limit)
        )
        
        result = await db.execute(stmt)
        feedback_list = result.all()
        
        return {
            "recent_feedback": [
                {
                    "feedback_id": feedback.id,
                    "user_id": feedback.user_id,
                    "sentiment": feedback.sentiment,
                    "rating": feedback.rating,
                    "comment": feedback.comment[:100] + "..." if feedback.comment and len(feedback.comment) > 100 else feedback.comment,
                    "week_start": feedback.week_start.isoformat(),
                    "week_end": feedback.week_end.isoformat(),
                    "created_at": feedback.created_at.isoformat()
                }
                for feedback in feedback_list
            ],
            "count": len(feedback_list),
            "limit": limit
        }
        
    except Exception as e:
        logger.error(f"Error retrieving recent feedback: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve recent feedback: {str(e)}"
        )


@router.delete("/feedback/{feedback_id}")
async def delete_feedback(
    feedback_id: int,
    db: AsyncSession = Depends(get_db)
):
    """
    Delete feedback by ID.
    """
    try:
        from sqlalchemy import select
        
        stmt = select(ReportFeedback).where(ReportFeedback.id == feedback_id)
        result = await db.execute(stmt)
        feedback = result.scalar_one_or_none()
        
        if not feedback:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Feedback not found"
            )
        
        await db.delete(feedback)
        await db.commit()
        
        return {"success": True, "message": "Feedback deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting feedback: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete feedback: {str(e)}"
        )
