{"version": 3, "file": "ServerRegistrationModule.js", "sourceRoot": "", "sources": ["../src/ServerRegistrationModule.ts"], "names": [], "mappings": "AAEA,eAAe;IACb,WAAW,EAAE,GAAG,EAAE,GAAE,CAAC;IACrB,eAAe,EAAE,GAAG,EAAE,GAAE,CAAC;CACE,CAAC", "sourcesContent": ["import { ServerRegistrationModule } from './ServerRegistrationModule.types';\n\nexport default {\n  addListener: () => {},\n  removeListeners: () => {},\n} as ServerRegistrationModule;\n"]}