{"formatVersion": "1.1", "component": {"group": "host.exp.exponent", "module": "expo.modules.notifications", "version": "0.31.4", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "8.13"}}, "variants": [{"name": "releaseVariantReleaseApiPublication", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.libraryelements": "aar", "org.gradle.usage": "java-api"}, "files": [{"name": "expo.modules.notifications-0.31.4.aar", "url": "expo.modules.notifications-0.31.4.aar", "size": 385766, "sha512": "3d4d78ea43b434e2dc1f98753fe0df9c2231bb9c36ce7049dfad0b96588e754cfbf68dd256de9bb93a4e8724284c14108d1ddbdc5ad334f494cf7e7aa06867cd", "sha256": "c8e1f39d0b9ee62d8d1ad90195d6ac343b2fc5b5c31680ef91d005b91db0152e", "sha1": "e885d81269fd205458059aabf26175be02dab3c6", "md5": "0ce7216475af7c010849e878ed7def3d"}]}, {"name": "releaseVariantReleaseRuntimePublication", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.libraryelements": "aar", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "org.jetbrains.kotlin", "module": "kotlin-parcelize-runtime", "version": {"requires": "2.0.21"}}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7", "version": {"requires": "2.0.21"}}, {"group": "androidx.core", "module": "core", "version": {"requires": "1.6.0"}}, {"group": "androidx.lifecycle", "module": "lifecycle-runtime", "version": {"requires": "2.2.0"}}, {"group": "androidx.lifecycle", "module": "lifecycle-process", "version": {"requires": "2.2.0"}}, {"group": "androidx.lifecycle", "module": "lifecycle-common-java8", "version": {"requires": "2.2.0"}}, {"group": "org.jetbrains.kotlinx", "module": "kotlinx-coroutines-android", "version": {"requires": "1.7.3"}}, {"group": "com.google.firebase", "module": "firebase-messaging", "version": {"requires": "24.0.1"}}, {"group": "me.leolin", "module": "ShortcutBadger", "version": {"requires": "1.1.22"}, "excludes": [{"group": "*", "module": "*"}], "thirdPartyCompatibility": {"artifactSelector": {"name": "ShortcutBadger", "type": "aar", "extension": "aar"}}}], "files": [{"name": "expo.modules.notifications-0.31.4.aar", "url": "expo.modules.notifications-0.31.4.aar", "size": 385766, "sha512": "3d4d78ea43b434e2dc1f98753fe0df9c2231bb9c36ce7049dfad0b96588e754cfbf68dd256de9bb93a4e8724284c14108d1ddbdc5ad334f494cf7e7aa06867cd", "sha256": "c8e1f39d0b9ee62d8d1ad90195d6ac343b2fc5b5c31680ef91d005b91db0152e", "sha1": "e885d81269fd205458059aabf26175be02dab3c6", "md5": "0ce7216475af7c010849e878ed7def3d"}]}, {"name": "releaseVariantReleaseSourcePublication", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "java-runtime"}, "files": [{"name": "expo.modules.notifications-0.31.4-sources.jar", "url": "expo.modules.notifications-0.31.4-sources.jar", "size": 99407, "sha512": "ad96d4d7ea7b0cecbdea1ce64b28e4dbf5267074af9d7d352bd9f866d67cc32310347a9e68712e8502b55a815890002007a154ca508c0451a61acee82865eb9e", "sha256": "3d445a8ee9d150d620fafe8a452c8c74427a5d835ab1263cec7d4f0743ac8f3d", "sha1": "0e7782673df063a4fb10df6a346945f0367e43ba", "md5": "aa0446afdc9c4a1fc05d65f48e6cc53b"}]}]}