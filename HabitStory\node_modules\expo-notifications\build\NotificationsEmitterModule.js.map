{"version": 3, "file": "NotificationsEmitterModule.js", "sourceRoot": "", "sources": ["../src/NotificationsEmitterModule.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,QAAQ,EAAE,MAAM,mBAAmB,CAAC;AAI7C,IAAI,mBAAmB,GAAG,KAAK,CAAC;AAEhC,eAAe;IACb,WAAW,EAAE,GAAG,EAAE;QAChB,IAAI,CAAC,mBAAmB,EAAE,CAAC;YACzB,OAAO,CAAC,IAAI,CACV,6EAA6E,QAAQ,CAAC,EAAE,0CAA0C,CACnI,CAAC;YACF,mBAAmB,GAAG,IAAI,CAAC;QAC7B,CAAC;IACH,CAAC;IACD,eAAe,EAAE,GAAG,EAAE,GAAE,CAAC;CACI,CAAC", "sourcesContent": ["import { Platform } from 'expo-modules-core';\n\nimport { NotificationsEmitterModule } from './NotificationsEmitterModule.types';\n\nlet warningHasBeenShown = false;\n\nexport default {\n  addListener: () => {\n    if (!warningHasBeenShown) {\n      console.warn(\n        `[expo-notifications] Emitting notifications is not yet fully supported on ${Platform.OS}. Adding a listener will have no effect.`\n      );\n      warningHasBeenShown = true;\n    }\n  },\n  removeListeners: () => {},\n} as NotificationsEmitterModule;\n"]}