"""
Module 1: Parsing Service
Extracts habits, metrics, reflection, and trait evidence from journal entries using Gemini.
"""

import json
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime

from .ai_client import unified_ai_service

logger = logging.getLogger(__name__)


class ParsingService:
    """Service for parsing journal entries with AI."""

    def __init__(self):
        self.ai_service = unified_ai_service
    
    async def parse_entry(
        self,
        entry_text: str,
        user_id: str,
        previous_traits: Optional[Dict] = None
    ) -> Dict[str, Any]:
        """
        Parse a journal entry to extract structured data.
        
        Args:
            entry_text: The journal entry text
            user_id: User identifier for token tracking
            previous_traits: Previous user traits for context
            
        Returns:
            Dict containing habits, metrics, reflection, and trait evidence
        """
        try:
            # Define function schema for structured extraction
            functions = [{
                "name": "extract_entry_data",
                "description": "Extract structured data from a journal entry",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "habits": {
                            "type": "array",
                            "items": {"type": "string"},
                            "description": "List of habit-related activities mentioned"
                        },
                        "qualitative_habits": {
                            "type": "array",
                            "items": {
                                "type": "object",
                                "properties": {
                                    "name": {"type": "string"},
                                    "category": {
                                        "type": "string",
                                        "enum": ["health", "productivity", "wellness", "social", "personal"]
                                    },
                                    "completed": {"type": "boolean"},
                                    "confidence": {"type": "number", "minimum": 0, "maximum": 1}
                                },
                                "required": ["name", "category", "completed", "confidence"]
                            },
                            "description": "Detailed qualitative habit tracking"
                        },
                        "metrics": {
                            "type": "object",
                            "properties": {
                                "sleep_hours": {"type": "number", "minimum": 0, "maximum": 24},
                                "water_liters": {"type": "number", "minimum": 0, "maximum": 10},
                                "exercise_minutes": {"type": "number", "minimum": 0, "maximum": 300},
                                "mood_score": {"type": "number", "minimum": 1, "maximum": 10},
                                "stress_level": {"type": "number", "minimum": 1, "maximum": 10},
                                "productivity_score": {"type": "number", "minimum": 1, "maximum": 10},
                                "social_interactions": {"type": "number", "minimum": 0, "maximum": 20},
                                "screen_time_hours": {"type": "number", "minimum": 0, "maximum": 24},
                                "meditation_minutes": {"type": "number", "minimum": 0, "maximum": 120},
                                "steps": {"type": "number", "minimum": 0, "maximum": 50000},
                                "energy_level": {"type": "number", "minimum": 1, "maximum": 10}
                            },
                            "description": "Quantifiable metrics extracted from the entry"
                        },
                        "reflection": {
                            "type": "string",
                            "description": "Brief 1-2 sentence summary of main insight or feeling"
                        },
                        "user_traits": {
                            "type": "object",
                            "properties": {
                                "tone": {
                                    "type": "string",
                                    "enum": ["formal", "informal", "casual", "professional", "playful", "serious"]
                                },
                                "style": {
                                    "type": "string", 
                                    "enum": ["descriptive", "concise", "emotional", "analytical", "conversational", "poetic"]
                                },
                                "traits": {
                                    "type": "object",
                                    "description": "Personality traits with boolean/string values"
                                },
                                "trait_evidence": {
                                    "type": "object",
                                    "description": "Evidence for each trait as arrays of strings"
                                }
                            },
                            "required": ["tone", "style", "traits", "trait_evidence"]
                        }
                    },
                    "required": ["habits", "qualitative_habits", "metrics", "reflection", "user_traits"]
                }
            }]
            
            # Create system prompt
            system_prompt = self._create_parsing_system_prompt(previous_traits)
            
            # Make AI API call
            response = await self.ai_service.generate_response(
                system_prompt=system_prompt,
                user_prompt=f"Please analyze this journal entry:\n\n{entry_text}",
                functions=functions,
                temperature=0.3,
                user_id=user_id
            )
            
            # Parse response - try JSON first, then fallback to text parsing
            try:
                # Try to parse as JSON (structured response)
                parsed_data = json.loads(response.content)
                if isinstance(parsed_data, dict) and any(key in parsed_data for key in ["habits", "metrics", "reflection"]):
                    return self._validate_parsed_data(parsed_data)
            except (json.JSONDecodeError, KeyError):
                pass

            # Fallback to text parsing if structured parsing fails
            logger.warning("Structured parsing failed, attempting text parsing")
            return await self._parse_from_text_response(entry_text, user_id, previous_traits)
            
        except Exception as e:
            logger.error(f"Error parsing entry: {e}")
            return self._get_fallback_response()
    
    def _create_parsing_system_prompt(self, previous_traits: Optional[Dict] = None) -> str:
        """Create the system prompt for parsing."""
        return f"""You are an expert AI assistant specialized in analyzing personal journal entries to extract meaningful, structured data. Your goal is to help users understand their daily patterns and growth.

ANALYSIS TASK:
Extract structured data from journal entries with high accuracy and insight.

EXTRACTION REQUIREMENTS:

1. **habits**: Array of specific habit-related activities mentioned or implied
   - Include both explicit habits ("I meditated for 20 minutes") and implicit ones ("felt calm after my morning routine")
   - Focus on recurring, intentional behaviors
   - Examples: ["morning meditation", "evening walk", "journaling", "reading before bed"]

2. **qualitative_habits**: Detailed habit tracking with:
   - name: Clear, consistent habit name (e.g., "ate healthy", "slept well", "exercised")
   - category: One of "health", "productivity", "wellness", "social", "personal"
   - completed: true if the habit was done/achieved, false if mentioned as not done
   - confidence: 0-1 score of how confident you are about this detection

3. **metrics**: Quantifiable data when mentioned or reasonably inferred
   - Only include metrics that are explicitly mentioned or clearly implied
   - Use appropriate numeric values within realistic ranges

4. **reflection**: Concise, insightful summary (1-2 sentences)
   - Capture the main emotional tone and key insight
   - Use the user's voice and style
   - Focus on growth, learning, or significant moments

5. **user_traits**: Analyze communication and personality patterns
   - tone: Communication formality level
   - style: Writing and expression style
   - traits: Personality characteristics with boolean/string values
   - trait_evidence: Specific evidence from the text supporting each trait

CONTEXT:
Previous user traits: {json.dumps(previous_traits, indent=2) if previous_traits else 'None available'}

Use the extract_entry_data function to return structured data."""
    
    async def _parse_from_text_response(
        self,
        entry_text: str,
        user_id: str,
        previous_traits: Optional[Dict] = None
    ) -> Dict[str, Any]:
        """Fallback text-based parsing when function calling fails."""
        system_prompt = f"""You are an expert AI assistant that analyzes journal entries. Extract structured data and return it as valid JSON.

Previous user traits: {json.dumps(previous_traits) if previous_traits else 'None'}

Return ONLY a valid JSON object with this exact structure:
{{
  "habits": ["habit1", "habit2"],
  "qualitative_habits": [
    {{
      "name": "ate healthy",
      "category": "health",
      "completed": true,
      "confidence": 0.9
    }}
  ],
  "metrics": {{"sleep_hours": 8, "mood_score": 7}},
  "reflection": "Brief insight from the entry",
  "user_traits": {{
    "tone": "informal",
    "style": "conversational",
    "traits": {{"optimistic": true}},
    "trait_evidence": {{"optimistic": ["used positive language"]}}
  }}
}}"""
        
        response = await self.ai_service.generate_response(
            system_prompt=system_prompt,
            user_prompt=f"Analyze this journal entry:\n\n{entry_text}",
            temperature=0.3,
            user_id=user_id
        )
        
        try:
            # Clean and parse JSON response
            json_text = response.content.strip()
            json_text = json_text.replace("```json", "").replace("```", "")
            
            # Find JSON object in response
            start_idx = json_text.find("{")
            end_idx = json_text.rfind("}") + 1
            if start_idx >= 0 and end_idx > start_idx:
                json_text = json_text[start_idx:end_idx]
            
            parsed_data = json.loads(json_text)
            return self._validate_parsed_data(parsed_data)
            
        except Exception as e:
            logger.error(f"Error parsing text response: {e}")
            return self._get_fallback_response()
    
    def _validate_parsed_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate and clean parsed data."""
        validated = {
            "habits": data.get("habits", []),
            "qualitative_habits": [],
            "metrics": data.get("metrics", {}),
            "reflection": data.get("reflection", "No specific reflection extracted."),
            "user_traits": {
                "tone": "informal",
                "style": "conversational", 
                "traits": {},
                "trait_evidence": {}
            }
        }
        
        # Validate qualitative habits
        for habit in data.get("qualitative_habits", []):
            if (isinstance(habit, dict) and
                "name" in habit and
                "category" in habit and
                "completed" in habit and
                "confidence" in habit):
                validated["qualitative_habits"].append(habit)
        
        # Validate user traits
        if "user_traits" in data and isinstance(data["user_traits"], dict):
            traits = data["user_traits"]
            validated["user_traits"].update({
                "tone": traits.get("tone", "informal"),
                "style": traits.get("style", "conversational"),
                "traits": traits.get("traits", {}),
                "trait_evidence": traits.get("trait_evidence", {})
            })
        
        return validated
    
    def _get_fallback_response(self) -> Dict[str, Any]:
        """Return fallback response when parsing fails."""
        return {
            "habits": [],
            "qualitative_habits": [],
            "metrics": {},
            "reflection": "Unable to analyze entry automatically. Please try again.",
            "user_traits": {
                "tone": "informal",
                "style": "conversational",
                "traits": {},
                "trait_evidence": {}
            }
        }


# Global service instance
parsing_service = ParsingService()
