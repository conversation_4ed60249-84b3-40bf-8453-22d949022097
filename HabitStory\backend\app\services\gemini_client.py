"""
Generic Gemini API client with retry logic, timeout handling, and token counting.
"""

import asyncio
import json
import logging
from typing import Dict, List, Optional, Any, Union
import httpx
from datetime import datetime, timedelta
import tiktoken

from ..config import settings

logger = logging.getLogger(__name__)


class TokenCounter:
    """Token counting utility for usage tracking."""
    
    def __init__(self):
        # Use a simple approximation for token counting
        # 1 token ≈ 4 characters for most text
        self.chars_per_token = 4
    
    def count_tokens(self, text: str) -> int:
        """Count approximate tokens in text."""
        return len(text) // self.chars_per_token
    
    def count_tokens_messages(self, messages: List[Dict[str, str]]) -> int:
        """Count tokens in message list."""
        total = 0
        for message in messages:
            if isinstance(message, dict):
                for value in message.values():
                    if isinstance(value, str):
                        total += self.count_tokens(value)
        return total


class GeminiClient:
    """Generic Gemini API client with advanced features."""
    
    def __init__(self):
        self.api_key = settings.gemini_api_key
        self.api_url = settings.gemini_api_url
        self.timeout = settings.gemini_timeout
        self.max_retries = settings.gemini_max_retries
        self.token_counter = TokenCounter()
        
        # Rate limiting
        self._request_times: List[datetime] = []
        self._max_requests_per_minute = settings.rate_limit_requests_per_minute
        
    async def _check_rate_limit(self):
        """Check and enforce rate limiting."""
        now = datetime.now()
        # Remove requests older than 1 minute
        self._request_times = [
            req_time for req_time in self._request_times
            if now - req_time < timedelta(minutes=1)
        ]
        
        if len(self._request_times) >= self._max_requests_per_minute:
            sleep_time = 60 - (now - self._request_times[0]).seconds
            logger.warning(f"Rate limit reached, sleeping for {sleep_time} seconds")
            await asyncio.sleep(sleep_time)
        
        self._request_times.append(now)
    
    async def _make_request(
        self,
        prompt: str,
        temperature: float = 0.7,
        max_tokens: int = 2048,
        functions: Optional[List[Dict]] = None
    ) -> Dict[str, Any]:
        """Make a single request to Gemini API."""
        await self._check_rate_limit()
        
        # Prepare request payload
        payload = {
            "contents": [{
                "parts": [{
                    "text": prompt
                }]
            }],
            "generationConfig": {
                "temperature": temperature,
                "topK": 40,
                "topP": 0.95,
                "maxOutputTokens": max_tokens,
            }
        }
        
        # Add function calling if provided
        if functions:
            payload["tools"] = [{
                "functionDeclarations": functions
            }]
        
        headers = {
            "Content-Type": "application/json",
        }
        
        async with httpx.AsyncClient(timeout=self.timeout) as client:
            response = await client.post(
                f"{self.api_url}?key={self.api_key}",
                json=payload,
                headers=headers
            )
            response.raise_for_status()
            return response.json()
    
    async def call_gemini(
        self,
        system: str,
        user: str,
        functions: Optional[List[Dict]] = None,
        temperature: float = 0.7,
        max_tokens: int = 2048,
        user_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Generic Gemini API call with retry logic and token counting.
        
        Args:
            system: System prompt/instructions
            user: User message/query
            functions: Optional function definitions for function calling
            temperature: Generation temperature (0.0-1.0)
            max_tokens: Maximum tokens to generate
            user_id: User ID for token tracking
            
        Returns:
            Dict containing response and metadata
        """
        # Combine system and user prompts
        full_prompt = f"{system}\n\nUser Query: {user}"
        
        # Count input tokens
        input_tokens = self.token_counter.count_tokens(full_prompt)
        
        # Track token usage if user_id provided
        if user_id:
            await self._track_token_usage(user_id, input_tokens, "input")
        
        last_exception = None
        
        for attempt in range(self.max_retries + 1):
            try:
                logger.info(f"Gemini API call attempt {attempt + 1}/{self.max_retries + 1}")
                
                response_data = await self._make_request(
                    full_prompt,
                    temperature=temperature,
                    max_tokens=max_tokens,
                    functions=functions
                )
                
                # Extract response text
                if response_data.get("candidates") and len(response_data["candidates"]) > 0:
                    candidate = response_data["candidates"][0]
                    
                    # Handle function calling response
                    if "functionCall" in candidate.get("content", {}).get("parts", [{}])[0]:
                        function_call = candidate["content"]["parts"][0]["functionCall"]
                        response_text = json.dumps(function_call)
                        response_type = "function_call"
                    else:
                        # Regular text response
                        response_text = candidate.get("content", {}).get("parts", [{}])[0].get("text", "")
                        response_type = "text"
                    
                    # Count output tokens
                    output_tokens = self.token_counter.count_tokens(response_text)
                    total_tokens = input_tokens + output_tokens
                    
                    # Track output token usage
                    if user_id:
                        await self._track_token_usage(user_id, output_tokens, "output")
                    
                    logger.info(f"Gemini API call successful. Tokens: {total_tokens}")
                    
                    return {
                        "response": response_text,
                        "type": response_type,
                        "tokens": {
                            "input": input_tokens,
                            "output": output_tokens,
                            "total": total_tokens
                        },
                        "raw_response": response_data
                    }
                else:
                    raise ValueError("No valid response from Gemini API")
                    
            except httpx.HTTPStatusError as e:
                last_exception = e
                if e.response.status_code == 429:  # Rate limit
                    wait_time = 2 ** attempt  # Exponential backoff
                    logger.warning(f"Rate limited, waiting {wait_time} seconds")
                    await asyncio.sleep(wait_time)
                elif e.response.status_code == 400:
                    logger.error(f"Bad request: {e.response.text}")
                    raise ValueError(f"Invalid request: {e.response.text}")
                elif e.response.status_code == 403:
                    logger.error("Invalid API key")
                    raise ValueError("Invalid Gemini API key")
                else:
                    logger.error(f"HTTP error {e.response.status_code}: {e.response.text}")
                    if attempt == self.max_retries:
                        raise
                    await asyncio.sleep(2 ** attempt)
                    
            except (httpx.RequestError, asyncio.TimeoutError) as e:
                last_exception = e
                if attempt == self.max_retries:
                    logger.error(f"Request failed after {self.max_retries + 1} attempts: {e}")
                    raise ValueError(f"Failed to connect to Gemini API: {e}")
                
                wait_time = 2 ** attempt
                logger.warning(f"Request failed, retrying in {wait_time} seconds: {e}")
                await asyncio.sleep(wait_time)
                
            except Exception as e:
                last_exception = e
                logger.error(f"Unexpected error: {e}")
                if attempt == self.max_retries:
                    raise
                await asyncio.sleep(2 ** attempt)
        
        # If we get here, all retries failed
        raise ValueError(f"Gemini API call failed after {self.max_retries + 1} attempts: {last_exception}")
    
    async def _track_token_usage(self, user_id: str, tokens: int, token_type: str):
        """Track token usage for a user (placeholder for database integration)."""
        # This will be implemented when we add the database models
        logger.info(f"Token usage - User: {user_id}, Tokens: {tokens}, Type: {token_type}")
    
    async def check_token_limit(self, user_id: str, plan: str = "FREE") -> Dict[str, Any]:
        """Check if user is within token limits for their plan."""
        # This will be implemented with database integration
        limit = settings.token_limits.get(plan, settings.token_limits["FREE"])
        # Placeholder - would query actual usage from database
        used = 0
        
        return {
            "limit": limit,
            "used": used,
            "remaining": limit - used,
            "percentage_used": (used / limit) * 100 if limit > 0 else 0
        }


# Global client instance
gemini_client = GeminiClient()
