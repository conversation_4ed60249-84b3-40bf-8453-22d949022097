"""
Module 4: Storytelling Service
Generates personalized stories adapted to user traits using Gemini.
"""

import json
import logging
from typing import Dict, List, Any, Optional

from .ai_client import unified_ai_service

logger = logging.getLogger(__name__)


class StorytellingService:
    """Service for generating personalized narratives from user data."""
    
    def __init__(self):
        self.ai_service = unified_ai_service
    
    async def generate_weekly_story(
        self,
        user_id: str,
        entries: List[Dict[str, Any]],
        stats: Dict[str, Any],
        correlations: Dict[str, Any],
        user_traits: Dict[str, Any],
        previous_feedback: Optional[str] = None
    ) -> str:
        """
        Generate a personalized weekly story narrative.
        
        Args:
            user_id: User identifier for token tracking
            entries: List of journal entries
            stats: Weekly statistics from metrics service
            correlations: Correlation analysis results
            user_traits: User personality traits and communication style
            previous_feedback: Previous feedback to incorporate
            
        Returns:
            Personalized story narrative as string
        """
        try:
            # Create system prompt for storytelling
            system_prompt = self._create_storytelling_system_prompt(user_traits, previous_feedback)
            
            # Prepare user data for story generation
            user_prompt = self._create_user_data_prompt(entries, stats, correlations)
            
            # Generate story
            response = await self.ai_service.generate_response(
                system_prompt=system_prompt,
                user_prompt=user_prompt,
                temperature=0.8,  # Higher temperature for more creative storytelling
                max_tokens=1500,
                user_id=user_id
            )

            return response.content
            
        except Exception as e:
            logger.error(f"Error generating weekly story: {e}")
            return self._get_fallback_story(user_traits)
    
    async def generate_habit_story(
        self,
        user_id: str,
        habit_name: str,
        habit_data: Dict[str, Any],
        user_traits: Dict[str, Any]
    ) -> str:
        """
        Generate a story focused on a specific habit.
        
        Args:
            user_id: User identifier
            habit_name: Name of the habit
            habit_data: Statistics and data about the habit
            user_traits: User personality traits
            
        Returns:
            Habit-focused story narrative
        """
        try:
            system_prompt = f"""You are a skilled storyteller who creates personalized, encouraging narratives about habit development. 

USER COMMUNICATION STYLE:
- Tone: {user_traits.get('tone', 'informal')}
- Style: {user_traits.get('style', 'conversational')}
- Personality traits: {json.dumps(user_traits.get('traits', {}), indent=2)}

Create a short, inspiring story (2-3 paragraphs) about the user's journey with the habit '{habit_name}'. 
Focus on progress, challenges overcome, and future potential. Use their communication style and personality traits.
Make it personal and motivating."""
            
            user_prompt = f"""Habit: {habit_name}
Habit Data: {json.dumps(habit_data, indent=2)}

Create an inspiring story about this habit journey."""
            
            response = await self.client.call_gemini(
                system=system_prompt,
                user=user_prompt,
                temperature=0.8,
                max_tokens=800,
                user_id=user_id
            )
            
            return response["response"]
            
        except Exception as e:
            logger.error(f"Error generating habit story: {e}")
            return f"Your journey with {habit_name} is a story of growth and dedication. Every step forward matters."
    
    async def generate_insight_story(
        self,
        user_id: str,
        insight: Dict[str, Any],
        user_traits: Dict[str, Any]
    ) -> str:
        """
        Generate a story around a specific insight or correlation.
        
        Args:
            user_id: User identifier
            insight: Specific insight or correlation to focus on
            user_traits: User personality traits
            
        Returns:
            Insight-focused narrative
        """
        try:
            system_prompt = f"""You are a thoughtful storyteller who helps people understand their personal insights through narrative.

USER COMMUNICATION STYLE:
- Tone: {user_traits.get('tone', 'informal')}
- Style: {user_traits.get('style', 'conversational')}
- Personality: {json.dumps(user_traits.get('traits', {}), indent=2)}

Create a brief, meaningful story (1-2 paragraphs) that explains this insight in a personal, relatable way.
Help the user understand what this means for their life and growth. Use their communication style."""
            
            user_prompt = f"""Insight to explore: {json.dumps(insight, indent=2)}

Turn this insight into a personal story that helps the user understand its significance."""
            
            response = await self.client.call_gemini(
                system=system_prompt,
                user=user_prompt,
                temperature=0.7,
                max_tokens=600,
                user_id=user_id
            )
            
            return response["response"]
            
        except Exception as e:
            logger.error(f"Error generating insight story: {e}")
            return "This insight reveals an important pattern in your journey of self-discovery and growth."
    
    def _create_storytelling_system_prompt(
        self, 
        user_traits: Dict[str, Any], 
        previous_feedback: Optional[str] = None
    ) -> str:
        """Create the system prompt for weekly storytelling."""
        tone = user_traits.get('tone', 'informal')
        style = user_traits.get('style', 'conversational')
        traits = user_traits.get('traits', {})
        trait_evidence = user_traits.get('trait_evidence', {})
        
        feedback_context = ""
        if previous_feedback:
            feedback_context = f"\nPREVIOUS FEEDBACK TO INCORPORATE:\n{previous_feedback}\n"
        
        return f"""You are a masterful storyteller and life coach who creates deeply personalized, engaging narratives from personal data. Your goal is to help users see their journey as a meaningful story of growth and self-discovery.

USER COMMUNICATION PROFILE:
- Tone: {tone}
- Style: {style}
- Personality Traits: {json.dumps(traits, indent=2)}
- Trait Evidence: {json.dumps(trait_evidence, indent=2)}

{feedback_context}

STORYTELLING GUIDELINES:

1. **Narrative Structure**: Create a compelling story arc with:
   - Opening that sets the scene of their week
   - Development showing challenges, growth, and discoveries
   - Resolution that celebrates progress and looks forward

2. **Personalization**: 
   - Mirror their exact {tone} tone throughout
   - Use their {style} communication style consistently
   - Incorporate their personality traits naturally into the narrative
   - Reference specific details from their data to make it personal

3. **Emotional Resonance**:
   - Acknowledge both struggles and victories
   - Celebrate small wins and progress
   - Frame challenges as part of the growth journey
   - Use encouraging, supportive language that matches their style

4. **Data Integration**:
   - Weave statistics and correlations into the narrative naturally
   - Don't just list numbers - tell the story behind them
   - Highlight meaningful patterns and insights
   - Connect data points to emotional and personal significance

5. **Length and Flow**:
   - Create 3-4 paragraphs of engaging narrative
   - Use smooth transitions between ideas
   - Maintain reader engagement throughout
   - End with inspiration and forward momentum

6. **Voice Matching**:
   - If they're analytical, include thoughtful observations
   - If they're emotional, acknowledge feelings and experiences
   - If they're goal-oriented, focus on progress and achievements
   - If they're reflective, include deeper insights and meaning

Create a story that makes them feel seen, understood, and motivated to continue their journey."""
    
    def _create_user_data_prompt(
        self,
        entries: List[Dict[str, Any]],
        stats: Dict[str, Any],
        correlations: Dict[str, Any]
    ) -> str:
        """Create the user data prompt for story generation."""
        # Summarize entries
        entry_summaries = []
        for i, entry in enumerate(entries[-7:]):  # Last 7 entries
            date = entry.get('date', f'Day {i+1}')
            text_preview = entry.get('text', '')[:200] + '...' if len(entry.get('text', '')) > 200 else entry.get('text', '')
            reflection = entry.get('reflection', '')
            
            entry_summaries.append(f"**{date}**: {text_preview}\nReflection: {reflection}")
        
        entries_text = "\n\n".join(entry_summaries)
        
        # Summarize key statistics
        stats_summary = self._summarize_stats(stats)
        
        # Summarize key correlations
        correlations_summary = self._summarize_correlations(correlations)
        
        return f"""WEEKLY JOURNAL ENTRIES:
{entries_text}

WEEKLY STATISTICS SUMMARY:
{stats_summary}

KEY CORRELATIONS AND INSIGHTS:
{correlations_summary}

Please create a personalized weekly story that weaves together these elements into a compelling narrative about the user's journey this week."""
    
    def _summarize_stats(self, stats: Dict[str, Any]) -> str:
        """Summarize key statistics for storytelling."""
        summary_parts = []
        
        # Period info
        period = stats.get('period', {})
        if period:
            summary_parts.append(f"Period: {period.get('total_days', 0)} days with {period.get('entries_count', 0)} journal entries")
        
        # Key metrics
        quant_metrics = stats.get('quantitative_metrics', {})
        if quant_metrics:
            key_metrics = []
            for metric, data in list(quant_metrics.items())[:5]:  # Top 5 metrics
                if isinstance(data, dict) and 'mean' in data:
                    key_metrics.append(f"{metric.replace('_', ' ')}: avg {data['mean']:.1f} ({data.get('trend', 'stable')})")
            if key_metrics:
                summary_parts.append("Key Metrics: " + ", ".join(key_metrics))
        
        # Habits
        habit_metrics = stats.get('habit_metrics', {})
        if habit_metrics:
            habit_summary = []
            for habit, data in list(habit_metrics.items())[:3]:  # Top 3 habits
                if isinstance(data, dict) and 'completion_rate' in data:
                    rate = data['completion_rate'] * 100
                    habit_summary.append(f"{habit}: {rate:.0f}% completion")
            if habit_summary:
                summary_parts.append("Habits: " + ", ".join(habit_summary))
        
        # Overall trend
        summary_info = stats.get('summary', {})
        if summary_info:
            trend = summary_info.get('overall_trend', 'stable')
            summary_parts.append(f"Overall trend: {trend}")
        
        return "\n".join(summary_parts) if summary_parts else "Limited statistical data available"
    
    def _summarize_correlations(self, correlations: Dict[str, Any]) -> str:
        """Summarize key correlations for storytelling."""
        insights = correlations.get('insights', [])
        
        if not insights:
            return "No significant correlations found this week"
        
        # Take top 3 insights
        top_insights = insights[:3]
        insight_texts = []
        
        for insight in top_insights:
            insight_text = insight.get('insight', '')
            strength = insight.get('strength', '')
            insight_texts.append(f"- {insight_text} (Strength: {strength})")
        
        return "\n".join(insight_texts)
    
    def _get_fallback_story(self, user_traits: Dict[str, Any]) -> str:
        """Generate a fallback story when AI generation fails."""
        tone = user_traits.get('tone', 'informal')
        
        if tone == 'formal':
            return """This week represents another chapter in your ongoing journey of self-discovery and personal development. While the specific details of your experiences may vary, the consistent practice of reflection and self-awareness demonstrates your commitment to growth. Each entry in your journal contributes to a larger narrative of intentional living and continuous improvement."""
        else:
            return """What a week it's been! Every day you showed up and took the time to reflect on your experiences - and that's something worth celebrating. Your journey is uniquely yours, filled with moments of growth, discovery, and progress. Keep going, because every step forward is building the story of who you're becoming."""


# Global service instance
storytelling_service = StorytellingService()
