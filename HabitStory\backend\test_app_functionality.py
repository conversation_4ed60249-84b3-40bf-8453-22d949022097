#!/usr/bin/env python3
"""
Script de verificación completa para HabitStory
Prueba todos los componentes del sistema para asegurar que funcionen correctamente.
"""

import sys
import os
import asyncio
import json
from datetime import datetime
from unittest.mock import patch, AsyncMock

# Agregar el directorio de la app al path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '.'))

def print_header(title):
    """Imprime un encabezado formateado."""
    print(f"\n{'='*60}")
    print(f"🔍 {title}")
    print(f"{'='*60}")

def print_success(message):
    """Imprime un mensaje de éxito."""
    print(f"✅ {message}")

def print_error(message):
    """Imprime un mensaje de error."""
    print(f"❌ {message}")

def print_info(message):
    """Imprime un mensaje informativo."""
    print(f"ℹ️  {message}")

async def test_imports():
    """Prueba que todas las importaciones funcionen correctamente."""
    print_header("VERIFICACIÓN DE IMPORTACIONES")
    
    try:
        # Importaciones básicas
        print_info("Probando importaciones básicas...")
        from app.config import settings
        from app.database import engine, Base
        from app.main import app
        print_success("Importaciones básicas: OK")
        
        # Importaciones de modelos
        print_info("Probando importaciones de modelos...")
        from app.models.traits import UserTraits
        from app.models.reports import WeeklyReport
        from app.models.feedback import ReportFeedback
        from app.models.usage import TokenUsage
        print_success("Modelos de base de datos: OK")
        
        # Importaciones de servicios
        print_info("Probando importaciones de servicios...")
        from app.services.gemini_client import gemini_client
        from app.services.parsing import parsing_service
        from app.services.metrics import metrics_service
        from app.services.correlations import correlations_service
        from app.services.storytelling import storytelling_service
        from app.services.recommendations import recommendations_service
        from app.services.validation import validation_service
        from app.services.questions import questions_service
        from app.services.visualization import visualization_service
        from app.services.report_builder import report_builder_service
        from app.services.pipeline import weekly_report_pipeline
        print_success("Servicios del pipeline: OK")
        
        # Importaciones de routers
        print_info("Probando importaciones de routers...")
        from app.routers import entries, reports, feedback
        print_success("Routers de API: OK")
        
        return True
        
    except Exception as e:
        print_error(f"Error en importaciones: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_configuration():
    """Prueba la configuración del sistema."""
    print_header("VERIFICACIÓN DE CONFIGURACIÓN")
    
    try:
        from app.config import settings
        
        print_info("Verificando configuración...")
        print(f"  • Database URL: {settings.database_url}")
        print(f"  • Gemini API URL: {settings.gemini_api_url}")
        print(f"  • Gemini Timeout: {settings.gemini_timeout}s")
        print(f"  • Token Limits: {settings.token_limits}")
        
        # Verificar que la API key esté configurada
        if settings.gemini_api_key and len(settings.gemini_api_key) > 10:
            print_success("API Key de Gemini: Configurada")
        else:
            print_error("API Key de Gemini: No configurada correctamente")
            
        print_success("Configuración: OK")
        return True
        
    except Exception as e:
        print_error(f"Error en configuración: {e}")
        return False

async def test_services():
    """Prueba los servicios individuales."""
    print_header("VERIFICACIÓN DE SERVICIOS")
    
    try:
        # Datos de prueba
        sample_entry = {
            "text": "Hoy fue un gran día! Corrí 5km por la mañana y me sentí increíble. Medité 15 minutos y mi estado de ánimo está excelente.",
            "date": "2024-01-15T08:00:00Z",
            "user_id": "test_user"
        }
        
        sample_user_traits = {
            "tone": "informal",
            "style": "optimistic",
            "traits": {
                "health_conscious": True,
                "goal_oriented": True
            }
        }
        
        # Mock de respuesta de Gemini
        mock_gemini_response = {
            "response": json.dumps({
                "name": "extract_entry_data",
                "args": {
                    "habits": ["running", "meditation"],
                    "qualitative_habits": [
                        {"name": "morning_run", "category": "health", "completed": True, "confidence": 0.9}
                    ],
                    "metrics": {"exercise_minutes": 45, "mood_score": 9, "energy_level": 8},
                    "reflection": "Gran día con mucha energía positiva",
                    "user_traits": sample_user_traits
                }
            }),
            "type": "function_call",
            "tokens": {"input": 100, "output": 50, "total": 150}
        }
        
        # Probar servicio de parsing
        print_info("Probando servicio de parsing...")
        from app.services.parsing import parsing_service
        
        with patch.object(parsing_service.client, 'call_gemini', return_value=mock_gemini_response):
            parsed_data = await parsing_service.parse_entry(
                "test_user", sample_entry["text"], sample_user_traits
            )
            
            assert "habits" in parsed_data
            assert "metrics" in parsed_data
            assert "reflection" in parsed_data
            print_success("Servicio de parsing: OK")
        
        # Probar servicio de métricas
        print_info("Probando servicio de métricas...")
        from app.services.metrics import metrics_service
        
        sample_entries = [
            {
                "habits": ["running", "meditation"],
                "metrics": {"mood_score": 8, "exercise_minutes": 30},
                "date": "2024-01-15"
            },
            {
                "habits": ["running"],
                "metrics": {"mood_score": 7, "exercise_minutes": 45},
                "date": "2024-01-16"
            }
        ]
        
        stats = metrics_service.calculate_weekly_stats(sample_entries)
        assert "quantitative_metrics" in stats
        assert "habit_metrics" in stats
        print_success("Servicio de métricas: OK")
        
        # Probar servicio de correlaciones
        print_info("Probando servicio de correlaciones...")
        from app.services.correlations import correlations_service
        
        correlations = correlations_service.find_correlations(sample_entries)
        assert "insights" in correlations
        print_success("Servicio de correlaciones: OK")
        
        # Probar servicio de visualización
        print_info("Probando servicio de visualización...")
        from app.services.visualization import visualization_service
        
        visualizations = visualization_service.generate_weekly_visualizations(
            sample_entries, stats, correlations
        )
        assert isinstance(visualizations, dict)
        print_success("Servicio de visualización: OK")
        
        return True
        
    except Exception as e:
        print_error(f"Error en servicios: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_pipeline():
    """Prueba el pipeline completo."""
    print_header("VERIFICACIÓN DEL PIPELINE COMPLETO")
    
    try:
        from app.services.pipeline import weekly_report_pipeline
        
        # Datos de prueba para una semana completa
        sample_entries = [
            {
                "text": "Gran inicio de semana! Corrí 5km y medité 15 minutos. Me siento muy energizado.",
                "date": "2024-01-15T08:00:00Z",
                "user_id": "test_user"
            },
            {
                "text": "Día productivo en el trabajo. Hice yoga 30 minutos y cociné una cena saludable.",
                "date": "2024-01-16T19:00:00Z",
                "user_id": "test_user"
            },
            {
                "text": "Dormí mal anoche, solo 5 horas. Me siento cansado pero hice ejercicio ligero.",
                "date": "2024-01-17T10:00:00Z",
                "user_id": "test_user"
            }
        ]
        
        user_traits = {
            "tone": "informal",
            "style": "optimistic",
            "traits": {
                "health_conscious": True,
                "goal_oriented": True
            }
        }
        
        # Mock de múltiples respuestas de Gemini para diferentes módulos
        mock_responses = [
            # Parsing
            {
                "response": json.dumps({
                    "name": "extract_entry_data",
                    "args": {
                        "habits": ["running", "meditation", "yoga"],
                        "metrics": {"exercise_minutes": 45, "mood_score": 8, "sleep_hours": 6},
                        "reflection": "Semana con altibajos pero manteniendo hábitos",
                        "user_traits": user_traits
                    }
                }),
                "type": "function_call"
            },
            # Storytelling
            {
                "response": "¡Qué semana tan interesante has tenido! Empezaste con mucha energía corriendo y meditando, lo cual es fantástico. Aunque tuviste una noche difícil de sueño, es admirable cómo mantuviste tu compromiso con el ejercicio. Tu dedicación a mantener hábitos saludables incluso en días difíciles muestra una gran fortaleza mental.",
                "type": "text"
            },
            # Recommendations
            {
                "response": json.dumps({
                    "name": "generate_recommendations",
                    "args": {
                        "recommendations": [
                            {
                                "category": "wellness",
                                "title": "Optimiza tu sueño",
                                "description": "Prioriza conseguir 7-8 horas de sueño consistente",
                                "priority": "high",
                                "actionable_steps": ["Establece rutina de sueño", "Evita pantallas antes de dormir"],
                                "reasoning": "El sueño afecta directamente tu energía y estado de ánimo"
                            }
                        ]
                    }
                }),
                "type": "function_call"
            },
            # Questions
            {
                "response": json.dumps({
                    "name": "generate_questions",
                    "args": {
                        "questions": [
                            {
                                "category": "reflection",
                                "question": "¿Qué factores específicos te ayudan a dormir mejor?",
                                "context": "Basado en tu conexión sueño-energía"
                            }
                        ]
                    }
                }),
                "type": "function_call"
            },
            # Report builder
            {
                "response": """<!DOCTYPE html>
<html><head><title>Tu Semana</title></head>
<body><h1>Reporte Semanal</h1><p>Gran progreso esta semana!</p></body></html>""",
                "type": "text"
            }
        ]
        
        print_info("Ejecutando pipeline completo...")
        
        with patch('app.services.gemini_client.gemini_client.call_gemini') as mock_gemini:
            # Configurar respuestas cíclicas
            mock_gemini.side_effect = mock_responses * 10
            
            # Ejecutar pipeline
            result = await weekly_report_pipeline.generate_weekly_report(
                user_id="test_user",
                entries=sample_entries,
                user_traits=user_traits
            )
            
            # Verificar resultado
            assert "user_id" in result
            assert "final_report" in result
            assert "modules_executed" in result
            assert len(result["modules_executed"]) > 0
            
            print_success(f"Pipeline ejecutado exitosamente")
            print_info(f"Módulos ejecutados: {', '.join(result['modules_executed'])}")
            
            if result["final_report"]:
                print_success("Reporte HTML generado correctamente")
            else:
                print_error("No se generó reporte HTML")
        
        return True
        
    except Exception as e:
        print_error(f"Error en pipeline: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_api_structure():
    """Prueba la estructura de la API."""
    print_header("VERIFICACIÓN DE ESTRUCTURA DE API")
    
    try:
        from app.main import app
        from fastapi.testclient import TestClient
        
        print_info("Verificando aplicación FastAPI...")
        
        # Verificar que la app se puede crear
        assert app is not None
        print_success("Aplicación FastAPI: OK")
        
        # Verificar rutas principales
        routes = [route.path for route in app.routes]
        expected_routes = [
            "/",
            "/health",
            "/api/v1/entries/parse",
            "/api/v1/reports/weekly",
            "/api/v1/feedback"
        ]
        
        print_info("Verificando rutas de API...")
        for route in expected_routes:
            if any(route in r for r in routes):
                print_success(f"Ruta {route}: OK")
            else:
                print_error(f"Ruta {route}: No encontrada")
        
        return True
        
    except Exception as e:
        print_error(f"Error en estructura de API: {e}")
        return False

async def test_database_models():
    """Prueba los modelos de base de datos."""
    print_header("VERIFICACIÓN DE MODELOS DE BASE DE DATOS")
    
    try:
        from app.models.traits import UserTraits
        from app.models.reports import WeeklyReport
        from app.models.feedback import ReportFeedback
        from app.models.usage import TokenUsage
        
        print_info("Verificando modelos de base de datos...")
        
        # Verificar que los modelos se pueden instanciar
        user_traits = UserTraits(
            user_id="test_user",
            tone="informal",
            style="optimistic",
            traits={"health_conscious": True}
        )
        print_success("Modelo UserTraits: OK")
        
        weekly_report = WeeklyReport(
            user_id="test_user",
            week_start=datetime.now().date(),
            week_end=datetime.now().date(),
            html_content="<html>Test</html>",
            metadata={"test": True}
        )
        print_success("Modelo WeeklyReport: OK")
        
        feedback = ReportFeedback(
            report_id=1,
            sentiment="positive",
            rating=5,
            comment="Excelente reporte"
        )
        print_success("Modelo ReportFeedback: OK")
        
        usage = TokenUsage(
            user_id="test_user",
            tokens_used=100,
            plan="FREE"
        )
        print_success("Modelo TokenUsage: OK")
        
        return True
        
    except Exception as e:
        print_error(f"Error en modelos: {e}")
        return False

async def main():
    """Función principal que ejecuta todas las pruebas."""
    print_header("VERIFICACIÓN COMPLETA DE HABITSTORY")
    print_info("Iniciando verificación de funcionalidad...")
    
    results = []
    
    # Ejecutar todas las pruebas
    results.append(await test_imports())
    results.append(await test_configuration())
    results.append(await test_database_models())
    results.append(await test_services())
    results.append(await test_pipeline())
    results.append(await test_api_structure())
    
    # Resumen final
    print_header("RESUMEN DE VERIFICACIÓN")
    
    passed = sum(results)
    total = len(results)
    
    print(f"📊 Pruebas ejecutadas: {total}")
    print(f"✅ Pruebas exitosas: {passed}")
    print(f"❌ Pruebas fallidas: {total - passed}")
    print(f"📈 Porcentaje de éxito: {(passed/total)*100:.1f}%")
    
    if passed == total:
        print_success("🎉 ¡TODAS LAS PRUEBAS PASARON! La aplicación funciona correctamente.")
        print_info("✨ HabitStory está listo para producción")
    else:
        print_error(f"⚠️  {total - passed} pruebas fallaron. Revisar errores arriba.")
    
    return passed == total

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
