"""
User traits model for storing personality and communication patterns.
"""

from sqlalchemy import <PERSON>umn, Integer, String, Text, DateTime, JSON
from sqlalchemy.sql import func
from ..database import Base


class UserTraits(Base):
    """User personality traits and communication patterns."""
    
    __tablename__ = "traits"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(String(255), nullable=False, index=True)
    
    # Communication style
    tone = Column(String(50), nullable=False, default="informal")
    style = Column(String(50), nullable=False, default="conversational")
    
    # Personality traits as JSON
    traits = Column(JSON, nullable=False, default=dict)
    
    # Evidence for each trait as JSON
    trait_evidence = Column(JSON, nullable=False, default=dict)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    def __repr__(self):
        return f"<UserTraits(user_id={self.user_id}, tone={self.tone}, style={self.style})>"
