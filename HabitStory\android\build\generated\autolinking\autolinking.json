{"root": "C:\\_CARPETEr\\__Projects\\__Personal\\AppTracker\\MVP2\\HabitStory", "reactNativePath": "C:\\_CARPETEr\\__Projects\\__Personal\\AppTracker\\MVP2\\HabitStory\\node_modules\\react-native", "dependencies": {"expo": {"root": "C:\\_CARPETEr\\__Projects\\__Personal\\AppTracker\\MVP2\\HabitStory\\node_modules\\expo", "name": "expo", "platforms": {"android": {"sourceDir": "C:\\_CARPETEr\\__Projects\\__Personal\\AppTracker\\MVP2\\HabitStory\\node_modules\\expo\\android", "packageImportPath": "import expo.modules.ExpoModulesPackage;", "packageInstance": "new ExpoModulesPackage()", "buildTypes": [], "componentDescriptors": [], "cmakeListsPath": "C:/_CARPETEr/__Projects/__Personal/AppTracker/MVP2/HabitStory/node_modules/expo/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-safe-area-context": {"root": "C:\\_CARPETEr\\__Projects\\__Personal\\AppTracker\\MVP2\\HabitStory\\node_modules\\react-native-safe-area-context", "name": "react-native-safe-area-context", "platforms": {"android": {"sourceDir": "C:\\_CARPETEr\\__Projects\\__Personal\\AppTracker\\MVP2\\HabitStory\\node_modules\\react-native-safe-area-context\\android", "packageImportPath": "import com.th3rdwave.safeareacontext.SafeAreaContextPackage;", "packageInstance": "new SafeAreaContextPackage()", "buildTypes": [], "libraryName": "safeareacontext", "componentDescriptors": ["RNCSafeAreaProviderComponentDescriptor", "RNCSafeAreaViewComponentDescriptor"], "cmakeListsPath": "C:/_CARPETEr/__Projects/__Personal/AppTracker/MVP2/HabitStory/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-screens": {"root": "C:\\_CARPETEr\\__Projects\\__Personal\\AppTracker\\MVP2\\HabitStory\\node_modules\\react-native-screens", "name": "react-native-screens", "platforms": {"android": {"sourceDir": "C:\\_CARPETEr\\__Projects\\__Personal\\AppTracker\\MVP2\\HabitStory\\node_modules\\react-native-screens\\android", "packageImportPath": "import com.swmansion.rnscreens.RNScreensPackage;", "packageInstance": "new RNScreensPackage()", "buildTypes": [], "libraryName": "rnscreens", "componentDescriptors": ["RNSFullWindowOverlayComponentDescriptor", "RNSScreenContainerComponentDescriptor", "RNSScreenNavigationContainerComponentDescriptor", "RNSScreenStackHeaderConfigComponentDescriptor", "RNSScreenStackHeaderSubviewComponentDescriptor", "RNSScreenStackComponentDescriptor", "RNSSearchBarComponentDescriptor", "RNSScreenComponentDescriptor", "RNSScreenFooterComponentDescriptor", "RNSScreenContentWrapperComponentDescriptor", "RNSModalScreenComponentDescriptor"], "cmakeListsPath": "C:/_CARPETEr/__Projects/__Personal/AppTracker/MVP2/HabitStory/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-webview": {"root": "C:\\_CARPETEr\\__Projects\\__Personal\\AppTracker\\MVP2\\HabitStory\\node_modules\\react-native-webview", "name": "react-native-webview", "platforms": {"android": {"sourceDir": "C:\\_CARPETEr\\__Projects\\__Personal\\AppTracker\\MVP2\\HabitStory\\node_modules\\react-native-webview\\android", "packageImportPath": "import com.reactnativecommunity.webview.RNCWebViewPackage;", "packageInstance": "new RNCWebViewPackage()", "buildTypes": [], "libraryName": "RNCWebViewSpec", "componentDescriptors": ["RNCWebViewComponentDescriptor"], "cmakeListsPath": "C:/_CARPETEr/__Projects/__Personal/AppTracker/MVP2/HabitStory/node_modules/react-native-webview/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-edge-to-edge": {"root": "C:\\_CARPETEr\\__Projects\\__Personal\\AppTracker\\MVP2\\HabitStory\\node_modules\\react-native-edge-to-edge", "name": "react-native-edge-to-edge", "platforms": {"android": {"sourceDir": "C:\\_CARPETEr\\__Projects\\__Personal\\AppTracker\\MVP2\\HabitStory\\node_modules\\react-native-edge-to-edge\\android", "packageImportPath": "import com.zoontek.rnedgetoedge.EdgeToEdgePackage;", "packageInstance": "new EdgeToEdgePackage()", "buildTypes": [], "libraryName": "RNEdgeToEdge", "componentDescriptors": [], "cmakeListsPath": "C:/_CARPETEr/__Projects/__Personal/AppTracker/MVP2/HabitStory/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}}, "project": {"android": {"packageName": "com.habitstory.app", "sourceDir": "C:\\_CARPETEr\\__Projects\\__Personal\\AppTracker\\MVP2\\HabitStory\\android"}}}