{"version": 3, "file": "NotificationsEmitterModule.types.js", "sourceRoot": "", "sources": ["../src/NotificationsEmitterModule.types.ts"], "names": [], "mappings": "", "sourcesContent": ["import { ProxyNativeModule } from 'expo-modules-core';\n\nimport { NotificationResponse } from './Notifications.types';\n\nexport interface NotificationsEmitterModule extends ProxyNativeModule {\n  getLastNotificationResponse?: () => NotificationResponse | null;\n  clearLastNotificationResponse?: () => void;\n}\n"]}