{"version": 3, "file": "NotificationChannelManager.js", "sourceRoot": "", "sources": ["../src/NotificationChannelManager.ts"], "names": [], "mappings": "AAEA,eAAe;IACb,WAAW,EAAE,GAAG,EAAE,GAAE,CAAC;IACrB,eAAe,EAAE,GAAG,EAAE,GAAE,CAAC;CACI,CAAC", "sourcesContent": ["import { NotificationChannelManager } from './NotificationChannelManager.types';\n\nexport default {\n  addListener: () => {},\n  removeListeners: () => {},\n} as NotificationChannelManager;\n"]}