"""
Authentication router for HabitStory backend.
"""

from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, HTTPException, status, Request
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from pydantic import BaseModel
from typing import Optional
import logging

from ..database import get_db
from ..models.auth import User, LoginAttempt
from ..services.auth import auth_service
from ..config import settings

logger = logging.getLogger(__name__)
router = APIRouter()
security = HTTPBearer()


class LoginRequest(BaseModel):
    """Login request model."""
    user_id: str
    name: str


class LoginResponse(BaseModel):
    """Login response model."""
    access_token: str
    refresh_token: str
    token_type: str = "bearer"
    expires_in: int
    user: dict


class RefreshRequest(BaseModel):
    """Token refresh request model."""
    refresh_token: str


class RefreshResponse(BaseModel):
    """Token refresh response model."""
    access_token: str
    refresh_token: str
    token_type: str = "bearer"
    expires_in: int


async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: AsyncSession = Depends(get_db)
) -> User:
    """Get current authenticated user."""
    token = credentials.credentials
    
    # Verify access token
    payload = await auth_service.verify_access_token(token)
    user_id = payload.get("user_id")
    
    if not user_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token payload"
        )
    
    # Get user from database
    result = await db.execute(select(User).where(User.user_id == user_id))
    user = result.scalar_one_or_none()
    
    if not user or not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="User not found or inactive"
        )
    
    return user


@router.post("/auth/login", response_model=LoginResponse)
async def login(
    request: LoginRequest,
    http_request: Request,
    db: AsyncSession = Depends(get_db)
):
    """
    Login endpoint - creates or updates user and returns JWT tokens.
    """
    try:
        # Get client info
        ip_address = http_request.client.host if http_request.client else None
        user_agent = http_request.headers.get("user-agent")
        
        # Check if user exists, create if not
        result = await db.execute(select(User).where(User.user_id == request.user_id))
        user = result.scalar_one_or_none()
        
        if not user:
            # Create new user
            user = User(
                user_id=request.user_id,
                name=request.name
            )
            db.add(user)
            await db.commit()
            await db.refresh(user)
            logger.info(f"Created new user: {request.user_id}")
        else:
            # Update existing user name if changed
            if user.name != request.name:
                user.name = request.name
                await db.commit()
        
        # Create tokens
        access_token = auth_service.create_access_token({
            "sub": user.user_id,
            "user_id": user.user_id,
            "name": user.name
        })
        
        refresh_token = await auth_service.create_refresh_token(
            user.user_id,
            db,
            device_info=user_agent,
            ip_address=ip_address
        )
        
        # Log successful login
        login_attempt = LoginAttempt(
            user_id=user.user_id,
            ip_address=ip_address,
            user_agent=user_agent,
            success=True
        )
        db.add(login_attempt)
        await db.commit()
        
        return LoginResponse(
            access_token=access_token,
            refresh_token=refresh_token,
            expires_in=settings.access_token_expire_minutes * 60,
            user={
                "user_id": user.user_id,
                "name": user.name,
                "plan": user.plan,
                "is_active": user.is_active
            }
        )
        
    except Exception as e:
        # Log failed login attempt
        login_attempt = LoginAttempt(
            user_id=request.user_id,
            ip_address=ip_address,
            user_agent=user_agent,
            success=False,
            failure_reason=str(e)
        )
        db.add(login_attempt)
        await db.commit()
        
        logger.error(f"Login failed for {request.user_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Login failed"
        )


@router.post("/auth/refresh", response_model=RefreshResponse)
async def refresh_tokens(
    request: RefreshRequest,
    http_request: Request,
    db: AsyncSession = Depends(get_db)
):
    """
    Refresh tokens endpoint - rotates access and refresh tokens.
    """
    try:
        # Get client info
        ip_address = http_request.client.host if http_request.client else None
        user_agent = http_request.headers.get("user-agent")
        
        # Rotate tokens
        new_access_token, new_refresh_token = await auth_service.rotate_tokens(
            request.refresh_token,
            db,
            device_info=user_agent,
            ip_address=ip_address
        )
        
        return RefreshResponse(
            access_token=new_access_token,
            refresh_token=new_refresh_token,
            expires_in=settings.access_token_expire_minutes * 60
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Token refresh failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Token refresh failed"
        )


@router.post("/auth/logout")
async def logout(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Logout endpoint - revokes all user tokens.
    """
    try:
        await auth_service.revoke_all_user_tokens(current_user.user_id, db)
        
        return {"message": "Successfully logged out"}
        
    except Exception as e:
        logger.error(f"Logout failed for {current_user.user_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Logout failed"
        )


@router.get("/auth/me")
async def get_current_user_info(
    current_user: User = Depends(get_current_user)
):
    """
    Get current user information.
    """
    return {
        "user_id": current_user.user_id,
        "name": current_user.name,
        "plan": current_user.plan,
        "is_active": current_user.is_active,
        "created_at": current_user.created_at
    }
