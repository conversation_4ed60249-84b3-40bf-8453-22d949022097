#!/usr/bin/env python3
"""
Script simple para iniciar HabitStory
Ejecuta: python start_app.py
"""

import subprocess
import sys
import os
import time
import webbrowser
from pathlib import Path

def print_banner():
    """Imprime el banner de HabitStory."""
    print("""
    ╔══════════════════════════════════════════════════════════════╗
    ║                        🌱 HabitStory 🌱                        ║
    ║              AI-Powered Personal Growth Platform              ║
    ║                                                              ║
    ║                    Starting Application...                   ║
    ╚══════════════════════════════════════════════════════════════╝
    """)

def check_requirements():
    """Verifica que los requisitos estén instalados."""
    print("🔍 Verificando requisitos...")
    
    # Verificar Python
    python_version = sys.version_info
    if python_version.major < 3 or (python_version.major == 3 and python_version.minor < 8):
        print("❌ Error: Se requiere Python 3.8 o superior")
        return False
    print(f"✅ Python {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    # Verificar que estemos en el directorio correcto
    if not Path("backend").exists() or not Path("package.json").exists():
        print("❌ Error: Ejecuta este script desde el directorio HabitStory")
        return False
    print("✅ Directorio correcto")
    
    return True

def install_backend_deps():
    """Instala las dependencias del backend."""
    print("\n📦 Instalando dependencias del backend...")
    
    try:
        # Cambiar al directorio backend
        os.chdir("backend")
        
        # Instalar dependencias
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Dependencias del backend instaladas")
            return True
        else:
            print(f"❌ Error instalando dependencias: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False
    finally:
        os.chdir("..")

def start_backend():
    """Inicia el servidor backend."""
    print("\n🚀 Iniciando servidor backend...")
    
    try:
        os.chdir("backend")
        
        # Crear la base de datos si no existe
        print("📊 Configurando base de datos...")
        
        # Iniciar el servidor
        print("🌐 Iniciando FastAPI en http://localhost:8000")
        
        # Ejecutar uvicorn
        process = subprocess.Popen([
            sys.executable, "-m", "uvicorn", "app.main:app", 
            "--host", "0.0.0.0", "--port", "8000", "--reload"
        ])
        
        return process
        
    except Exception as e:
        print(f"❌ Error iniciando backend: {e}")
        return None
    finally:
        os.chdir("..")

def start_frontend():
    """Inicia el frontend móvil."""
    print("\n📱 Iniciando aplicación móvil...")
    
    try:
        # Verificar si npm está disponible
        result = subprocess.run(["npm", "--version"], capture_output=True)
        if result.returncode != 0:
            print("❌ npm no está instalado. Instala Node.js primero.")
            return None
        
        print("📦 Instalando dependencias del frontend...")
        result = subprocess.run(["npm", "install"], capture_output=True)
        
        if result.returncode != 0:
            print("⚠️  Advertencia: Error instalando dependencias del frontend")
        
        print("🚀 Iniciando Expo...")
        process = subprocess.Popen(["npx", "expo", "start"])
        
        return process
        
    except Exception as e:
        print(f"❌ Error iniciando frontend: {e}")
        return None

def main():
    """Función principal."""
    print_banner()
    
    # Verificar requisitos
    if not check_requirements():
        print("\n❌ No se pueden cumplir los requisitos. Revisa la instalación.")
        return
    
    # Instalar dependencias del backend
    if not install_backend_deps():
        print("\n❌ Error instalando dependencias del backend.")
        return
    
    # Iniciar backend
    backend_process = start_backend()
    if not backend_process:
        print("\n❌ Error iniciando el backend.")
        return
    
    # Esperar un poco para que el backend se inicie
    print("⏳ Esperando que el backend se inicie...")
    time.sleep(5)
    
    # Verificar que el backend esté funcionando
    try:
        import requests
        response = requests.get("http://localhost:8000/health", timeout=5)
        if response.status_code == 200:
            print("✅ Backend funcionando correctamente")
        else:
            print("⚠️  Backend iniciado pero no responde correctamente")
    except:
        print("⚠️  No se puede verificar el estado del backend")
    
    # Iniciar frontend
    frontend_process = start_frontend()
    
    # Mostrar información
    print(f"""
    ╔══════════════════════════════════════════════════════════════╗
    ║                    🎉 HabitStory Iniciado 🎉                  ║
    ╠══════════════════════════════════════════════════════════════╣
    ║                                                              ║
    ║  🌐 Backend API: http://localhost:8000                       ║
    ║  📚 Documentación: http://localhost:8000/docs                ║
    ║  ❤️  Health Check: http://localhost:8000/health              ║
    ║                                                              ║
    ║  📱 Para la app móvil:                                       ║
    ║     1. Instala "Expo Go" en tu móvil                        ║
    ║     2. Escanea el código QR que aparece                     ║
    ║     3. ¡Disfruta probando HabitStory!                       ║
    ║                                                              ║
    ║  🛑 Para detener: Presiona Ctrl+C                           ║
    ╚══════════════════════════════════════════════════════════════╝
    """)
    
    # Abrir documentación en el navegador
    try:
        time.sleep(2)
        webbrowser.open("http://localhost:8000/docs")
        print("🌐 Abriendo documentación de la API en el navegador...")
    except:
        pass
    
    # Esperar hasta que el usuario presione Ctrl+C
    try:
        print("\n⏳ Aplicación ejecutándose... Presiona Ctrl+C para detener.")
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n\n🛑 Deteniendo HabitStory...")
        
        # Terminar procesos
        if backend_process:
            backend_process.terminate()
            print("✅ Backend detenido")
        
        if frontend_process:
            frontend_process.terminate()
            print("✅ Frontend detenido")
        
        print("👋 ¡Gracias por usar HabitStory!")

if __name__ == "__main__":
    main()
