"""
Reports model for storing generated weekly summaries.
"""

from sqlalchemy import Column, Integer, String, Text, DateTime, Date
from sqlalchemy.sql import func
from ..database import Base


class WeeklyReport(Base):
    """Weekly summary reports."""
    
    __tablename__ = "reports"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(String(255), nullable=False, index=True)
    
    # Week period
    week_start = Column(Date, nullable=False)
    week_end = Column(Date, nullable=False)
    
    # Generated HTML content
    html_content = Column(Text, nullable=False)
    
    # Metadata
    generation_time_seconds = Column(Integer, nullable=True)
    tokens_used = Column(Integer, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    def __repr__(self):
        return f"<WeeklyReport(user_id={self.user_id}, week_start={self.week_start})>"
