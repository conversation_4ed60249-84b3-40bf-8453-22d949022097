"""
JWT Authentication service with token rotation.
"""

import jwt
import hashlib
import secrets
from datetime import datetime, timedelta
from typing import Op<PERSON>, Dict, Any, Tuple
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_
from fastapi import HTT<PERSON>Exception, status
import logging

from ..config import settings
from ..models.auth import User, RefreshToken, LoginAttempt
from ..database import get_db

logger = logging.getLogger(__name__)


class AuthService:
    """JWT Authentication service with token rotation."""
    
    def __init__(self):
        self.secret_key = settings.secret_key
        self.algorithm = settings.algorithm
        self.access_token_expire_minutes = settings.access_token_expire_minutes
        self.refresh_token_expire_days = 30  # Refresh tokens expire in 30 days
    
    def _hash_token(self, token: str) -> str:
        """Hash a token for secure storage."""
        return hashlib.sha256(token.encode()).hexdigest()
    
    def _generate_refresh_token(self) -> str:
        """Generate a cryptographically secure refresh token."""
        return secrets.token_urlsafe(32)
    
    def create_access_token(self, data: Dict[str, Any]) -> str:
        """Create a JWT access token."""
        to_encode = data.copy()
        expire = datetime.utcnow() + timedelta(minutes=self.access_token_expire_minutes)
        to_encode.update({"exp": expire, "type": "access"})
        
        encoded_jwt = jwt.encode(to_encode, self.secret_key, algorithm=self.algorithm)
        return encoded_jwt
    
    async def create_refresh_token(
        self, 
        user_id: str, 
        db: AsyncSession,
        device_info: Optional[str] = None,
        ip_address: Optional[str] = None
    ) -> str:
        """Create and store a refresh token."""
        # Generate refresh token
        refresh_token = self._generate_refresh_token()
        token_hash = self._hash_token(refresh_token)
        
        # Create refresh token record
        db_refresh_token = RefreshToken(
            user_id=user_id,
            token_hash=token_hash,
            expires_at=datetime.utcnow() + timedelta(days=self.refresh_token_expire_days),
            device_info=device_info,
            ip_address=ip_address
        )
        
        db.add(db_refresh_token)
        await db.commit()
        
        logger.info(f"Created refresh token for user {user_id}")
        return refresh_token
    
    async def verify_access_token(self, token: str) -> Dict[str, Any]:
        """Verify and decode an access token."""
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm])
            
            # Check token type
            if payload.get("type") != "access":
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid token type"
                )
            
            return payload
            
        except jwt.ExpiredSignatureError:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Access token expired"
            )
        except jwt.JWTError:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid access token"
            )
    
    async def verify_refresh_token(self, token: str, db: AsyncSession) -> Optional[RefreshToken]:
        """Verify a refresh token and return the token record."""
        token_hash = self._hash_token(token)
        
        # Query refresh token
        result = await db.execute(
            select(RefreshToken).where(
                and_(
                    RefreshToken.token_hash == token_hash,
                    RefreshToken.is_revoked == False
                )
            )
        )
        db_token = result.scalar_one_or_none()
        
        if not db_token:
            return None
        
        # Check if token is expired
        if db_token.is_expired:
            # Clean up expired token
            await self.revoke_refresh_token(db_token, db)
            return None
        
        # Update last used timestamp
        db_token.last_used_at = datetime.utcnow()
        await db.commit()
        
        return db_token
    
    async def rotate_tokens(
        self, 
        refresh_token: str, 
        db: AsyncSession,
        device_info: Optional[str] = None,
        ip_address: Optional[str] = None
    ) -> Tuple[str, str]:
        """Rotate access and refresh tokens."""
        # Verify current refresh token
        db_token = await self.verify_refresh_token(refresh_token, db)
        if not db_token:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid or expired refresh token"
            )
        
        user_id = db_token.user_id
        
        # Revoke current refresh token
        await self.revoke_refresh_token(db_token, db)
        
        # Create new tokens
        new_access_token = self.create_access_token({"sub": user_id, "user_id": user_id})
        new_refresh_token = await self.create_refresh_token(
            user_id, db, device_info, ip_address
        )
        
        logger.info(f"Rotated tokens for user {user_id}")
        return new_access_token, new_refresh_token
    
    async def revoke_refresh_token(self, db_token: RefreshToken, db: AsyncSession):
        """Revoke a refresh token."""
        db_token.is_revoked = True
        db_token.revoked_at = datetime.utcnow()
        await db.commit()
        
        logger.info(f"Revoked refresh token for user {db_token.user_id}")
    
    async def revoke_all_user_tokens(self, user_id: str, db: AsyncSession):
        """Revoke all refresh tokens for a user."""
        result = await db.execute(
            select(RefreshToken).where(
                and_(
                    RefreshToken.user_id == user_id,
                    RefreshToken.is_revoked == False
                )
            )
        )
        tokens = result.scalars().all()
        
        for token in tokens:
            await self.revoke_refresh_token(token, db)
        
        logger.info(f"Revoked all tokens for user {user_id}")
    
    async def cleanup_expired_tokens(self, db: AsyncSession):
        """Clean up expired refresh tokens."""
        result = await db.execute(
            select(RefreshToken).where(
                RefreshToken.expires_at < datetime.utcnow()
            )
        )
        expired_tokens = result.scalars().all()
        
        for token in expired_tokens:
            await db.delete(token)
        
        await db.commit()
        logger.info(f"Cleaned up {len(expired_tokens)} expired tokens")


# Global auth service instance
auth_service = AuthService()
