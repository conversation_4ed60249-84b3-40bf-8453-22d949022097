# HabitStory Backend Production Environment Variables
# Copy this file to .env.production and fill in your actual production values

# Gemini API Configuration
GEMINI_API_KEY=your_production_gemini_api_key_here

# Security Configuration - GENERATE A NEW KEY FOR PRODUCTION!
# Run: python generate_secret_key.py
SECRET_KEY=your_production_secret_key_minimum_64_characters_here

# Database Configuration
DATABASE_URL=postgresql://user:password@localhost:5432/habitstory_prod

# Environment Configuration
ENVIRONMENT=production
DEBUG=false
LOG_LEVEL=WARNING

# CORS Configuration - Restrict to your domain
ALLOWED_ORIGINS=["https://yourdomain.com","https://app.yourdomain.com"]

# Rate Limiting
RATE_LIMIT_REQUESTS_PER_MINUTE=30
RATE_LIMIT_REQUESTS_PER_HOUR=500
