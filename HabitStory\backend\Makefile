# HabitStory Backend Makefile

.PHONY: help install dev test lint format clean docker-build docker-run migrate upgrade

# Default target
help:
	@echo "Available commands:"
	@echo "  install     - Install dependencies"
	@echo "  dev         - Run development server"
	@echo "  test        - Run tests"
	@echo "  test-cov    - Run tests with coverage"
	@echo "  lint        - Run linting"
	@echo "  format      - Format code"
	@echo "  clean       - Clean cache and temp files"
	@echo "  docker-build - Build Docker image"
	@echo "  docker-run  - Run with Docker"
	@echo "  migrate     - Create new migration"
	@echo "  upgrade     - Run database migrations"

# Install dependencies
install:
	pip install -r requirements.txt

# Run development server
dev:
	uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

# Run tests
test:
	pytest -v

# Run tests with coverage
test-cov:
	pytest --cov=app --cov-report=html --cov-report=term-missing

# Run linting
lint:
	flake8 app tests
	mypy app

# Format code
format:
	black app tests
	isort app tests

# Clean cache and temp files
clean:
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete
	find . -type d -name "*.egg-info" -exec rm -rf {} +
	rm -rf .coverage htmlcov/ .pytest_cache/

# Build Docker image
docker-build:
	docker build -t habitstory-backend .

# Run with Docker
docker-run:
	docker run -p 8000:8000 -e GEMINI_API_KEY=${GEMINI_API_KEY} habitstory-backend

# Create new migration
migrate:
	alembic revision --autogenerate -m "$(MSG)"

# Run database migrations
upgrade:
	alembic upgrade head

# Initialize database
init-db:
	alembic upgrade head

# Reset database (development only)
reset-db:
	rm -f habitstory.db
	alembic upgrade head

# Run development with auto-reload
dev-watch:
	uvicorn app.main:app --reload --host 0.0.0.0 --port 8000 --log-level debug

# Check API health
health:
	curl -f http://localhost:8000/health || echo "API not responding"

# Generate API documentation
docs:
	@echo "API documentation available at:"
	@echo "  Swagger UI: http://localhost:8000/docs"
	@echo "  ReDoc: http://localhost:8000/redoc"

# Run specific test file
test-file:
	pytest -v tests/$(FILE)

# Run tests for specific module
test-module:
	pytest -v -k $(MODULE)

# Install development dependencies
install-dev:
	pip install -r requirements.txt
	pip install pytest pytest-asyncio pytest-cov black isort flake8 mypy

# Check code quality
quality: lint test

# Full development setup
setup: install-dev init-db
	@echo "Development environment ready!"
	@echo "Run 'make dev' to start the server"

# Production build
build-prod:
	docker build -t habitstory-backend:latest .
	docker tag habitstory-backend:latest habitstory-backend:$(shell date +%Y%m%d-%H%M%S)

# Deploy (placeholder)
deploy:
	@echo "Deployment configuration needed"
	@echo "Set up your deployment pipeline here"
