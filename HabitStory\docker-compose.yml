version: '3.8'

services:
  # FastAPI Backend
  backend:
    build: 
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=sqlite+aiosqlite:///./habitstory.db
      - GEMINI_API_KEY=${GEMINI_API_KEY:-AIzaSyBPGdbmlVQzY3XsVEXo_UPElTtVBiNSR_4}
      - ENVIRONMENT=development
      - DEBUG=true
    volumes:
      - ./backend:/app
      - backend_data:/app/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Expo Development Server (optional for development)
  mobile:
    build:
      context: ./HabitStory
      dockerfile: Dockerfile.dev
    ports:
      - "8081:8081"  # Expo dev server
      - "19000:19000"  # Expo dev tools
      - "19001:19001"  # Expo dev tools
      - "19002:19002"  # Expo dev tools
    environment:
      - EXPO_DEVTOOLS_LISTEN_ADDRESS=0.0.0.0
      - REACT_NATIVE_PACKAGER_HOSTNAME=0.0.0.0
    volumes:
      - ./HabitStory:/app
      - /app/node_modules
    stdin_open: true
    tty: true
    depends_on:
      - backend
    profiles:
      - development

  # Database (SQLite is file-based, but we could add PostgreSQL for production)
  # postgres:
  #   image: postgres:15-alpine
  #   environment:
  #     POSTGRES_DB: habitstory
  #     POSTGRES_USER: habitstory
  #     POSTGRES_PASSWORD: habitstory_password
  #   volumes:
  #     - postgres_data:/var/lib/postgresql/data
  #   ports:
  #     - "5432:5432"
  #   profiles:
  #     - production

  # Redis for caching (optional)
  # redis:
  #   image: redis:7-alpine
  #   ports:
  #     - "6379:6379"
  #   volumes:
  #     - redis_data:/data
  #   profiles:
  #     - production

  # Nginx reverse proxy (for production)
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - backend
    profiles:
      - production

volumes:
  backend_data:
  # postgres_data:
  # redis_data:

networks:
  default:
    name: habitstory_network
