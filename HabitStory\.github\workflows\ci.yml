name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

env:
  PYTHON_VERSION: '3.11'
  NODE_VERSION: '18'

jobs:
  # Backend Tests
  backend-test:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
    
    - name: Cache Python dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-
    
    - name: Install dependencies
      working-directory: ./HabitStory/backend
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install pytest pytest-asyncio pytest-cov black isort flake8 mypy
    
    - name: Lint with flake8
      working-directory: ./HabitStory/backend
      run: |
        flake8 app tests --count --select=E9,F63,F7,F82 --show-source --statistics
        flake8 app tests --count --exit-zero --max-complexity=10 --max-line-length=127 --statistics
    
    - name: Format check with black
      working-directory: ./HabitStory/backend
      run: black --check app tests
    
    - name: Import sort check
      working-directory: ./HabitStory/backend
      run: isort --check-only app tests
    
    - name: Type check with mypy
      working-directory: ./HabitStory/backend
      run: mypy app --ignore-missing-imports
      continue-on-error: true
    
    - name: Test with pytest
      working-directory: ./HabitStory/backend
      env:
        GEMINI_API_KEY: ${{ secrets.GEMINI_API_KEY || 'test-key' }}
      run: |
        pytest --cov=app --cov-report=xml --cov-report=term-missing
    
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./HabitStory/backend/coverage.xml
        flags: backend
        name: backend-coverage

  # Frontend Tests
  frontend-test:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: './HabitStory/package-lock.json'
    
    - name: Install dependencies
      working-directory: ./HabitStory
      run: npm ci
    
    - name: Run ESLint
      working-directory: ./HabitStory
      run: npm run lint
      continue-on-error: true
    
    - name: Run TypeScript check
      working-directory: ./HabitStory
      run: npx tsc --noEmit
    
    - name: Run tests
      working-directory: ./HabitStory
      run: npm test
      continue-on-error: true

  # Integration Tests
  integration-test:
    runs-on: ubuntu-latest
    needs: [backend-test, frontend-test]
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
    
    - name: Set up Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
    
    - name: Start backend service
      working-directory: ./HabitStory/backend
      env:
        GEMINI_API_KEY: ${{ secrets.GEMINI_API_KEY || 'test-key' }}
      run: |
        pip install -r requirements.txt
        uvicorn app.main:app --host 0.0.0.0 --port 8000 &
        sleep 10
    
    - name: Test API health
      run: |
        curl -f http://localhost:8000/health || exit 1
        curl -f http://localhost:8000/docs || exit 1
    
    - name: Run integration tests
      working-directory: ./HabitStory/backend
      env:
        GEMINI_API_KEY: ${{ secrets.GEMINI_API_KEY || 'test-key' }}
        API_BASE_URL: http://localhost:8000
      run: |
        pytest tests/test_integration.py -v
      continue-on-error: true

  # Docker Build Test
  docker-test:
    runs-on: ubuntu-latest
    needs: [backend-test]
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
    
    - name: Build Docker image
      working-directory: ./HabitStory/backend
      run: |
        docker build -t habitstory-backend:test .
    
    - name: Test Docker image
      run: |
        docker run -d -p 8000:8000 --name test-container \
          -e GEMINI_API_KEY=${{ secrets.GEMINI_API_KEY || 'test-key' }} \
          habitstory-backend:test
        sleep 15
        curl -f http://localhost:8000/health || exit 1
        docker stop test-container
        docker rm test-container

  # Security Scan
  security-scan:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: './HabitStory/backend'
        format: 'sarif'
        output: 'trivy-results.sarif'
    
    - name: Upload Trivy scan results to GitHub Security tab
      uses: github/codeql-action/upload-sarif@v2
      with:
        sarif_file: 'trivy-results.sarif'

  # Deploy to staging (on develop branch)
  deploy-staging:
    runs-on: ubuntu-latest
    needs: [backend-test, frontend-test, integration-test, docker-test]
    if: github.ref == 'refs/heads/develop' && github.event_name == 'push'
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Deploy to staging
      run: |
        echo "Deploying to staging environment..."
        # Add your staging deployment commands here
        # Example: deploy to Heroku, AWS, etc.
    
    - name: Notify deployment
      run: |
        echo "Staging deployment completed"
        # Add notification logic (Slack, email, etc.)

  # Deploy to production (on main branch)
  deploy-production:
    runs-on: ubuntu-latest
    needs: [backend-test, frontend-test, integration-test, docker-test]
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Deploy to production
      run: |
        echo "Deploying to production environment..."
        # Add your production deployment commands here
        # Example: deploy to production servers
    
    - name: Notify deployment
      run: |
        echo "Production deployment completed"
        # Add notification logic (Slack, email, etc.)

  # Performance Tests (optional)
  performance-test:
    runs-on: ubuntu-latest
    needs: [integration-test]
    if: github.ref == 'refs/heads/main'
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Run performance tests
      run: |
        echo "Running performance tests..."
        # Add performance testing tools like k6, Artillery, etc.
        # Example: k6 run performance-tests.js
