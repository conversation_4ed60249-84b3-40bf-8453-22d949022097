"""
Authentication middleware for automatic token rotation.
"""

from fastapi import Request, Response
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware
from datetime import datetime, timedelta
import logging
import json

from ..services.auth import auth_service
from ..database import get_db_session
from ..config import settings

logger = logging.getLogger(__name__)


class TokenRotationMiddleware(BaseHTTPMiddleware):
    """Middleware to handle automatic token rotation."""
    
    def __init__(self, app):
        super().__init__(app)
        self.rotation_threshold = timedelta(hours=settings.token_rotation_threshold_hours)
    
    async def dispatch(self, request: Request, call_next):
        """Process request and handle token rotation if needed."""
        
        # Skip token rotation for auth endpoints
        if request.url.path.startswith("/api/v1/auth/"):
            return await call_next(request)
        
        # Check if request has authorization header
        auth_header = request.headers.get("authorization")
        if not auth_header or not auth_header.startswith("Bearer "):
            return await call_next(request)
        
        try:
            # Extract token
            token = auth_header.split(" ")[1]
            
            # Verify token and check if rotation is needed
            payload = await auth_service.verify_access_token(token)
            
            # Check token age for rotation
            if settings.auto_rotate_tokens:
                token_issued_at = datetime.fromtimestamp(payload.get("iat", 0))
                if datetime.utcnow() - token_issued_at > self.rotation_threshold:
                    # Token is old enough for rotation
                    logger.info(f"Token rotation triggered for user {payload.get('user_id')}")
                    
                    # Add rotation flag to request state
                    request.state.needs_token_rotation = True
                    request.state.user_id = payload.get("user_id")
            
            # Process the request
            response = await call_next(request)
            
            # Handle token rotation in response if needed
            if hasattr(request.state, "needs_token_rotation") and request.state.needs_token_rotation:
                await self._add_rotation_headers(response, request.state.user_id)
            
            return response
            
        except Exception as e:
            logger.error(f"Token rotation middleware error: {e}")
            # Continue with normal request processing
            return await call_next(request)
    
    async def _add_rotation_headers(self, response: Response, user_id: str):
        """Add token rotation headers to response."""
        try:
            # Create new access token
            new_access_token = auth_service.create_access_token({
                "sub": user_id,
                "user_id": user_id
            })
            
            # Add rotation headers
            response.headers["X-Token-Rotated"] = "true"
            response.headers["X-New-Access-Token"] = new_access_token
            
            logger.info(f"Added token rotation headers for user {user_id}")
            
        except Exception as e:
            logger.error(f"Failed to add rotation headers: {e}")


class SecurityHeadersMiddleware(BaseHTTPMiddleware):
    """Middleware to add security headers."""
    
    async def dispatch(self, request: Request, call_next):
        """Add security headers to response."""
        response = await call_next(request)
        
        # Add security headers
        response.headers["X-Content-Type-Options"] = "nosniff"
        response.headers["X-Frame-Options"] = "DENY"
        response.headers["X-XSS-Protection"] = "1; mode=block"
        response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
        
        # Add HSTS header for HTTPS
        if request.url.scheme == "https":
            response.headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains"
        
        return response
