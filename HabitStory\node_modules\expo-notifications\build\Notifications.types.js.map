{"version": 3, "file": "Notifications.types.js", "sourceRoot": "", "sources": ["../src/Notifications.types.ts"], "names": [], "mappings": "AAiQA;;;GAGG;AACH,MAAM,CAAN,IAAY,4BAQX;AARD,WAAY,4BAA4B;IACtC,qDAAqB,CAAA;IACrB,+CAAe,CAAA;IACf,iDAAiB,CAAA;IACjB,mDAAmB,CAAA;IACnB,iDAAiB,CAAA;IACjB,6CAAa,CAAA;IACb,8DAA8B,CAAA;AAChC,CAAC,EARW,4BAA4B,KAA5B,4BAA4B,QAQvC;AAmID;;;GAGG;AACH,MAAM,CAAN,IAAY,2BAMX;AAND,WAAY,2BAA2B;IACrC,0CAAW,CAAA;IACX,0CAAW,CAAA;IACX,kDAAmB,CAAA;IACnB,4CAAa,CAAA;IACb,0CAAW,CAAA;AACb,CAAC,EANW,2BAA2B,KAA3B,2BAA2B,QAMtC;AAwXD,OAAO,EAIL,gBAAgB,GACjB,MAAM,mBAAmB,CAAC", "sourcesContent": ["/**\n * An object which represents a notification delivered by a push notification system.\n *\n * On Android under `remoteMessage` field a JS version of the Firebase `RemoteMessage` may be accessed.\n * On iOS under `payload` you may find full contents of [`UNNotificationContent`'s](https://developer.apple.com/documentation/usernotifications/unnotificationcontent?language=objc) [`userInfo`](https://developer.apple.com/documentation/usernotifications/unnotificationcontent/1649869-userinfo?language=objc), for example [remote notification payload](https://developer.apple.com/library/archive/documentation/NetworkingInternet/Conceptual/RemoteNotificationsPG/CreatingtheNotificationPayload.html).\n */\nimport type { EventSubscription } from 'expo-modules-core';\n\nexport type PushNotificationTrigger = {\n  type: 'push';\n  /**\n   * @platform ios\n   */\n  payload?: Record<string, unknown>;\n  /**\n   * @platform android\n   */\n  remoteMessage?: FirebaseRemoteMessage;\n};\n\n/**\n * A trigger related to a [`UNCalendarNotificationTrigger`](https://developer.apple.com/documentation/usernotifications/uncalendarnotificationtrigger?language=objc).\n * @platform ios\n */\nexport interface CalendarNotificationTrigger {\n  type: 'calendar';\n  repeats: boolean;\n  dateComponents: {\n    era?: number;\n    year?: number;\n    month?: number;\n    day?: number;\n    hour?: number;\n    minute?: number;\n    second?: number;\n    weekday?: number;\n    weekdayOrdinal?: number;\n    quarter?: number;\n    weekOfMonth?: number;\n    weekOfYear?: number;\n    yearForWeekOfYear?: number;\n    nanosecond?: number;\n    isLeapMonth: boolean;\n    timeZone?: string;\n    calendar?: string;\n  };\n}\n\n/**\n * The region used to determine when the system sends the notification.\n * @platform ios\n */\nexport interface Region {\n  type: string;\n  /**\n   * The identifier for the region object.\n   */\n  identifier: string;\n  /**\n   * Indicates whether notifications are generated upon entry into the region.\n   */\n  notifyOnEntry: boolean;\n  /**\n   * Indicates whether notifications are generated upon exit from the region.\n   */\n  notifyOnExit: boolean;\n}\n\n/**\n * A circular geographic region, specified as a center point and radius. Based on Core Location [`CLCircularRegion`](https://developer.apple.com/documentation/corelocation/clcircularregion) class.\n * @platform ios\n */\nexport interface CircularRegion extends Region {\n  type: 'circular';\n  /**\n   * The radius (measured in meters) that defines the geographic area’s outer boundary.\n   */\n  radius: number;\n  /**\n   * The center point of the geographic area.\n   */\n  center: {\n    latitude: number;\n    longitude: number;\n  };\n}\n\n/**\n * A region used to detect the presence of iBeacon devices. Based on Core Location [`CLBeaconRegion`](https://developer.apple.com/documentation/corelocation/clbeaconregion) class.\n * @platform ios\n */\nexport interface BeaconRegion extends Region {\n  type: 'beacon';\n  /**\n   * A Boolean value that indicates whether Core Location sends beacon notifications when the device’s display is on.\n   */\n  notifyEntryStateOnDisplay: boolean;\n  /**\n   * The major value from the beacon identity constraint that defines the beacon region.\n   */\n  major: number | null;\n  /**\n   * The minor value from the beacon identity constraint that defines the beacon region.\n   */\n  minor: number | null;\n  /**\n   * The UUID value from the beacon identity constraint that defines the beacon region.\n   */\n  uuid?: string;\n  /**\n   * The beacon identity constraint that defines the beacon region.\n   */\n  beaconIdentityConstraint?: {\n    uuid: string;\n    major: number | null;\n    minor: number | null;\n  };\n}\n\n/**\n * A trigger related to a [`UNLocationNotificationTrigger`](https://developer.apple.com/documentation/usernotifications/unlocationnotificationtrigger?language=objc).\n * @platform ios\n */\nexport interface LocationNotificationTrigger {\n  type: 'location';\n  repeats: boolean;\n  region: CircularRegion | BeaconRegion;\n}\n\n/**\n * A trigger related to an elapsed time interval. May be repeating (see `repeats` field).\n */\nexport interface TimeIntervalNotificationTrigger {\n  type: 'timeInterval';\n  repeats: boolean;\n  seconds: number;\n}\n\n/**\n * A trigger related to a daily notification.\n * > The same functionality will be achieved on iOS with a `CalendarNotificationTrigger`.\n * @platform android\n */\nexport interface DailyNotificationTrigger {\n  type: 'daily';\n  hour: number;\n  minute: number;\n}\n\n/**\n * A trigger related to a weekly notification.\n * > The same functionality will be achieved on iOS with a `CalendarNotificationTrigger`.\n * @platform android\n */\nexport interface WeeklyNotificationTrigger {\n  type: 'weekly';\n  weekday: number;\n  hour: number;\n  minute: number;\n}\n\n/**\n * A trigger related to a monthly notification.\n * > The same functionality will be achieved on iOS with a `CalendarNotificationTrigger`.\n * @platform android\n */\nexport interface MonthlyNotificationTrigger {\n  type: 'monthly';\n  day: number;\n  hour: number;\n  minute: number;\n}\n\n/**\n * A trigger related to a yearly notification.\n * > The same functionality will be achieved on iOS with a `CalendarNotificationTrigger`.\n * @platform android\n */\nexport interface YearlyNotificationTrigger {\n  type: 'yearly';\n  day: number;\n  month: number;\n  hour: number;\n  minute: number;\n}\n\n// @docsMissing\n/**\n * A Firebase `RemoteMessage` that caused the notification to be delivered to the app.\n */\nexport interface FirebaseRemoteMessage {\n  collapseKey: string | null;\n  data: Record<string, string>;\n  from: string | null;\n  messageId: string | null;\n  messageType: string | null;\n  originalPriority: number;\n  priority: number;\n  sentTime: number;\n  to: string | null;\n  ttl: number;\n  notification: null | FirebaseRemoteMessageNotification;\n}\n\n// @docsMissing\nexport interface FirebaseRemoteMessageNotification {\n  body: string | null;\n  bodyLocalizationArgs: string[] | null;\n  bodyLocalizationKey: string | null;\n  channelId: string | null;\n  clickAction: string | null;\n  color: string | null;\n  usesDefaultLightSettings: boolean;\n  usesDefaultSound: boolean;\n  usesDefaultVibrateSettings: boolean;\n  eventTime: number | null;\n  icon: string | null;\n  imageUrl: string | null;\n  lightSettings: number[] | null;\n  link: string | null;\n  localOnly: boolean;\n  notificationCount: number | null;\n  notificationPriority: number | null;\n  sound: string | null;\n  sticky: boolean;\n  tag: string | null;\n  ticker: string | null;\n  title: string | null;\n  titleLocalizationArgs: string[] | null;\n  titleLocalizationKey: string | null;\n  vibrateTimings: number[] | null;\n  visibility: number | null;\n}\n\n/**\n * Represents a notification trigger that is unknown to `expo-notifications` and that it didn't know how to serialize for JS.\n */\nexport interface UnknownNotificationTrigger {\n  type: 'unknown';\n}\n\n/**\n * A union type containing different triggers which may cause the notification to be delivered to the application.\n */\nexport type NotificationTrigger =\n  | PushNotificationTrigger\n  | LocationNotificationTrigger\n  | NotificationTriggerInput\n  | UnknownNotificationTrigger;\n\n/**\n * A trigger that will cause the notification to be delivered immediately.\n */\nexport type ChannelAwareTriggerInput = {\n  channelId: string;\n};\n\n/**\n * Schedulable trigger inputs (that are not a plain date value or time value)\n * must have the \"type\" property set to one of these values.\n */\nexport enum SchedulableTriggerInputTypes {\n  CALENDAR = 'calendar',\n  DAILY = 'daily',\n  WEEKLY = 'weekly',\n  MONTHLY = 'monthly',\n  YEARLY = 'yearly',\n  DATE = 'date',\n  TIME_INTERVAL = 'timeInterval',\n}\n\n/**\n * This trigger input will cause the notification to be delivered once or many times\n * (controlled by the value of `repeats`)\n * when the date components match the specified values.\n * Corresponds to native\n * [`UNCalendarNotificationTrigger`](https://developer.apple.com/documentation/usernotifications/uncalendarnotificationtrigger?language=objc).\n * @platform ios\n */\nexport type CalendarTriggerInput = {\n  type: SchedulableTriggerInputTypes.CALENDAR;\n  channelId?: string;\n  repeats?: boolean;\n  seconds?: number;\n  timezone?: string;\n  year?: number;\n  month?: number;\n  weekday?: number;\n  weekOfMonth?: number;\n  weekOfYear?: number;\n  weekdayOrdinal?: number;\n  day?: number;\n  hour?: number;\n  minute?: number;\n  second?: number;\n};\n\n/**\n * This trigger input will cause the notification to be delivered once per day\n * when the `hour` and `minute` date components match the specified values.\n */\nexport type DailyTriggerInput = {\n  type: SchedulableTriggerInputTypes.DAILY;\n  channelId?: string;\n  hour: number;\n  minute: number;\n};\n\n/**\n * This trigger input will cause the notification to be delivered once every week\n * when the `weekday`, `hour`, and `minute` date components match the specified values.\n * > **Note:** Weekdays are specified with a number from `1` through `7`, with `1` indicating Sunday.\n */\nexport type WeeklyTriggerInput = {\n  type: SchedulableTriggerInputTypes.WEEKLY;\n  channelId?: string;\n  weekday: number;\n  hour: number;\n  minute: number;\n};\n\n/**\n * This trigger input will cause the notification to be delivered once per month\n * when the `day`, `hour`, and `minute` date components match the specified values.\n * > **Note:** All properties are specified in JavaScript `Date` object's ranges (i.e. January is represented as 0).\n */\nexport type MonthlyTriggerInput = {\n  type: SchedulableTriggerInputTypes.MONTHLY;\n  channelId?: string;\n  day: number;\n  hour: number;\n  minute: number;\n};\n\n/**\n * This trigger input will cause the notification to be delivered once every year\n * when the `day`, `month`, `hour`, and `minute` date components match the specified values.\n * > **Note:** All properties are specified in JavaScript `Date` object's ranges (i.e. January is represented as 0).\n */\nexport type YearlyTriggerInput = {\n  type: SchedulableTriggerInputTypes.YEARLY;\n  channelId?: string;\n  day: number;\n  month: number;\n  hour: number;\n  minute: number;\n};\n\n/**\n * This trigger input will cause the notification to be delivered once\n * on the specified value of the `date` property. The value of `repeats` will be ignored\n * for this trigger type.\n */\nexport type DateTriggerInput = {\n  type: SchedulableTriggerInputTypes.DATE;\n  date: Date | number;\n  channelId?: string;\n};\n\n/**\n * This trigger input will cause the notification to be delivered once or many times\n * (depends on the `repeats` field) after `seconds` time elapse.\n * > **On iOS**, when `repeats` is `true`, the time interval must be 60 seconds or greater.\n * Otherwise, the notification won't be triggered.\n */\nexport type TimeIntervalTriggerInput = {\n  type: SchedulableTriggerInputTypes.TIME_INTERVAL;\n  channelId?: string;\n  repeats?: boolean;\n  seconds: number;\n};\n\n/**\n * Input for time-based, schedulable triggers.\n * For these triggers you can check the next trigger date with [`getNextTriggerDateAsync`](#getnexttriggerdateasynctrigger).\n * If you pass in a `number` (Unix timestamp) or `Date`, it will be processed as a\n * trigger input of type [`SchedulableTriggerInputTypes.DATE`](#date). Otherwise, the input must be\n * an object, with a `type` value set to one of the allowed values in [`SchedulableTriggerInputTypes`](#schedulabletriggerinputtypes).\n * If the input is an object, date components passed in will be validated, and\n * an error is thrown if they are outside their allowed range (for example, the `minute` and\n * `second` components must be between 0 and 59 inclusive).\n */\nexport type SchedulableNotificationTriggerInput =\n  | CalendarTriggerInput\n  | TimeIntervalTriggerInput\n  | DailyTriggerInput\n  | WeeklyTriggerInput\n  | MonthlyTriggerInput\n  | YearlyTriggerInput\n  | DateTriggerInput;\n\n/**\n * A type which represents possible triggers with which you can schedule notifications.\n * A `null` trigger means that the notification should be scheduled for delivery immediately.\n */\nexport type NotificationTriggerInput =\n  | null\n  | ChannelAwareTriggerInput\n  | SchedulableNotificationTriggerInput;\n\n/**\n * An enum corresponding to values appropriate for Android's [`Notification#priority`](https://developer.android.com/reference/android/app/Notification#priority) field.\n * @platform android\n */\nexport enum AndroidNotificationPriority {\n  MIN = 'min',\n  LOW = 'low',\n  DEFAULT = 'default',\n  HIGH = 'high',\n  MAX = 'max',\n}\n\n/**\n * An object represents notification's content.\n */\nexport type NotificationContent = {\n  /**\n   * Notification title - the bold text displayed above the rest of the content.\n   */\n  title: string | null;\n  /**\n   * On Android: `subText` - the display depends on the device.\n   *\n   * On iOS: `subtitle` - the bold text displayed between title and the rest of the content.\n   */\n  subtitle: string | null;\n  /**\n   * Notification body - the main content of the notification.\n   */\n  body: string | null;\n  /**\n   * Data associated with the notification, not displayed\n   */\n  data: {\n    [key: string]: unknown;\n  };\n  /**\n   * The identifier of the notification’s category.\n   */\n  categoryIdentifier: string | null;\n  // @docsMissing\n  sound: 'default' | 'defaultCritical' | 'custom' | null;\n} & (NotificationContentIos | NotificationContentAndroid);\n\n/**\n * See [Apple documentation](https://developer.apple.com/documentation/usernotifications/unnotificationcontent?language=objc) for more information on specific fields.\n */\nexport type NotificationContentIos = {\n  /**\n   * The name of the image or storyboard to use when your app launches because of the notification.\n   */\n  launchImageName: string | null;\n  /**\n   * The number that your app’s icon displays.\n   */\n  badge: number | null;\n  /**\n   * The visual and audio attachments to display alongside the notification’s main content.\n   */\n  attachments: NotificationContentAttachmentIos[];\n  /**\n   * The text the system adds to the notification summary to provide additional context.\n   */\n  summaryArgument?: string | null;\n  /**\n   * The number the system adds to the notification summary when the notification represents multiple items.\n   */\n  summaryArgumentCount?: number;\n  /**\n   * The identifier that groups related notifications.\n   */\n  threadIdentifier: string | null;\n  /**\n   * The value your app uses to determine which scene to display to handle the notification.\n   */\n  targetContentIdentifier?: string;\n  /**\n   * The notification’s importance and required delivery timing.\n   * Possible values:\n   * - 'passive' - the system adds the notification to the notification list without lighting up the screen or playing a sound\n   * - 'active' - the system presents the notification immediately, lights up the screen, and can play a sound\n   * - 'timeSensitive' - The system presents the notification immediately, lights up the screen, can play a sound, and breaks through system notification controls\n   * - 'critical - the system presents the notification immediately, lights up the screen, and bypasses the mute switch to play a sound\n   * @platform ios\n   */\n  interruptionLevel?: 'passive' | 'active' | 'timeSensitive' | 'critical';\n};\n\n// @docsMissing\n/**\n * @platform ios\n */\nexport type NotificationContentAttachmentIos = {\n  identifier: string | null;\n  url: string | null;\n  type: string | null;\n  typeHint?: string;\n  hideThumbnail?: boolean;\n  thumbnailClipArea?: { x: number; y: number; width: number; height: number };\n  thumbnailTime?: number;\n};\n\n/**\n * See [Android developer documentation](https://developer.android.com/reference/android/app/Notification#fields) for more information on specific fields.\n */\nexport type NotificationContentAndroid = {\n  /**\n   * Application badge number associated with the notification.\n   */\n  badge?: number;\n  /**\n   * Accent color (in `#AARRGGBB` or `#RRGGBB` format) to be applied by the standard Style templates when presenting this notification.\n   */\n  color?: string;\n  /**\n   * Relative priority for this notification. Priority is an indication of how much of the user's valuable attention should be consumed by this notification.\n   * Low-priority notifications may be hidden from the user in certain situations, while the user might be interrupted for a higher-priority notification.\n   * The system will make a determination about how to interpret this priority when presenting the notification.\n   */\n  priority?: AndroidNotificationPriority;\n  /**\n   * The pattern with which to vibrate.\n   */\n  vibrationPattern?: number[];\n};\n\n/**\n * An object represents a request to present a notification. It has content — how it's being represented, and a trigger — what triggers the notification.\n * Many notifications ([`Notification`](#notification)) may be triggered with the same request (for example, a repeating notification).\n */\nexport interface NotificationRequest {\n  identifier: string;\n  content: NotificationContent;\n  trigger: NotificationTrigger;\n}\n\n// TODO(simek): asses if we can base this type on `NotificationContent`, since most of the fields looks like repetition\n/**\n * An object which represents notification content that you pass in as a part of `NotificationRequestInput`.\n */\nexport type NotificationContentInput = {\n  /**\n   * Notification title - the bold text displayed above the rest of the content.\n   */\n  title?: string | null;\n  /**\n   * On Android: `subText` - the display depends on the device.\n   *\n   * On iOS: `subtitle` - the bold text displayed between title and the rest of the content.\n   */\n  subtitle?: string | null;\n  /**\n   * The main content of the notification.\n   */\n  body?: string | null;\n  /**\n   * Data associated with the notification, not displayed.\n   */\n  data?: Record<string, unknown>;\n  /**\n   * Application badge number associated with the notification.\n   */\n  badge?: number;\n  sound?: boolean | string;\n  /**\n   * The name of the image or storyboard to use when your app launches because of the notification.\n   */\n  launchImageName?: string;\n  /**\n   * The pattern with which to vibrate.\n   * @platform android\n   */\n  vibrate?: number[];\n  /**\n   * Relative priority for this notification. Priority is an indication of how much of the user's valuable attention should be consumed by this notification.\n   * Low-priority notifications may be hidden from the user in certain situations, while the user might be interrupted for a higher-priority notification.\n   * The system will make a determination about how to interpret this priority when presenting the notification.\n   * @platform android\n   */\n  priority?: string;\n  /**\n   * Accent color (in `#AARRGGBB` or `#RRGGBB` format) to be applied by the standard Style templates when presenting this notification.\n   * @platform android\n   */\n  color?: string;\n  /**\n   * If set to `false`, the notification will not be automatically dismissed when clicked.\n   * The setting will be used when the value is not provided or is invalid is set to `true`, and the notification\n   * will be dismissed automatically anyway. Corresponds directly to Android's `setAutoCancel` behavior.\n   *\n   * See [Android developer documentation](https://developer.android.com/reference/android/app/Notification.Builder#setAutoCancel(boolean))\n   * for more details.\n   * @platform android\n   */\n  autoDismiss?: boolean;\n  /**\n   * The identifier of the notification’s category.\n   * @platform ios\n   */\n  categoryIdentifier?: string;\n  /**\n   * If set to `true`, the notification cannot be dismissed by swipe. This setting defaults\n   * to `false` if not provided or is invalid. Corresponds directly do Android's `isOngoing` behavior.\n   * In Firebase terms this property of a notification is called `sticky`.\n   *\n   * See [Android developer documentation](https://developer.android.com/reference/android/app/Notification.Builder#setOngoing(boolean))\n   * and [Firebase documentation](https://firebase.google.com/docs/reference/fcm/rest/v1/projects.messages#AndroidNotification.FIELDS.sticky)\n   * for more details.\n   * @platform android\n   */\n  sticky?: boolean;\n  /**\n   * The visual and audio attachments to display alongside the notification’s main content.\n   * @platform ios\n   */\n  attachments?: NotificationContentAttachmentIos[];\n  /*\n   * The notification’s importance and required delivery timing.\n   * Possible values:\n   * - 'passive' - the system adds the notification to the notification list without lighting up the screen or playing a sound\n   * - 'active' - the system presents the notification immediately, lights up the screen, and can play a sound\n   * - 'timeSensitive' - The system presents the notification immediately, lights up the screen, can play a sound, and breaks through system notification controls\n   * - 'critical - the system presents the notification immediately, lights up the screen, and bypasses the mute switch to play a sound\n   * @platform ios\n   */\n  interruptionLevel?: 'passive' | 'active' | 'timeSensitive' | 'critical';\n};\n\n/**\n * An object which represents a notification request you can pass into `scheduleNotificationAsync`.\n */\nexport interface NotificationRequestInput {\n  identifier?: string;\n  content: NotificationContentInput;\n  trigger: NotificationTriggerInput;\n}\n\n/**\n * An object which represents a single notification that has been triggered by some request ([`NotificationRequest`](#notificationrequest)) at some point in time.\n */\nexport interface Notification {\n  date: number;\n  request: NotificationRequest;\n}\n\n/**\n * An object which represents user's interaction with the notification.\n * > **Note:** If the user taps on a notification, `actionIdentifier` will be equal to [`Notifications.DEFAULT_ACTION_IDENTIFIER`](#notificationsdefault_action_identifier).\n */\nexport interface NotificationResponse {\n  notification: Notification;\n  actionIdentifier: string;\n  userText?: string;\n}\n\n/**\n * An object which represents behavior that should be applied to the incoming notification. On Android, this influences whether the notification is shown, a sound is played, and priority. On iOS, this maps directly to [`UNNotificationPresentationOptions`](https://developer.apple.com/documentation/usernotifications/unnotificationpresentationoptions).\n * > On Android, setting `shouldPlaySound: false` will result in the drop-down notification alert **not** showing, no matter what the priority is.\n * > This setting will also override any channel-specific sounds you may have configured.\n */\nexport interface NotificationBehavior {\n  /**\n   * @deprecated instead, specify `shouldShowBanner` and / or `shouldShowList`\n   * */\n  shouldShowAlert?: boolean;\n  shouldShowBanner: boolean;\n  shouldShowList: boolean;\n  shouldPlaySound: boolean;\n  /**\n   * @platform ios\n   */\n  shouldSetBadge: boolean;\n  priority?: AndroidNotificationPriority;\n}\n\nexport interface NotificationAction {\n  /**\n   * A unique string that identifies this action. If a user takes this action (for example, selects this button in the system's Notification UI),\n   * your app will receive this `actionIdentifier` via the [`NotificationResponseReceivedListener`](#addnotificationresponsereceivedlistenerlistener).\n   */\n  identifier: string;\n  /**\n   * The title of the button triggering this action.\n   */\n  buttonTitle: string;\n  /**\n   * Object which, if provided, will result in a button that prompts the user for a text response.\n   */\n  textInput?: {\n    /**\n     * A string which will be used as the title for the button used for submitting the text response.\n     * @platform ios\n     */\n    submitButtonTitle: string;\n    /**\n     * A string that serves as a placeholder until the user begins typing. Defaults to no placeholder string.\n     */\n    placeholder: string;\n  };\n  /**\n   * Object representing the additional configuration options.\n   */\n  options?: {\n    /**\n     * Boolean indicating whether the button title will be highlighted a different color (usually red).\n     * This usually signifies a destructive action such as deleting data.\n     * @platform ios\n     */\n    isDestructive?: boolean;\n    /**\n     * Boolean indicating whether triggering the action will require authentication from the user.\n     * @platform ios\n     */\n    isAuthenticationRequired?: boolean;\n    /**\n     * Boolean indicating whether triggering this action foregrounds the app.\n     * If `false` and your app is killed (not just backgrounded), [`NotificationResponseReceived` listeners](#addnotificationresponsereceivedlistenerlistener)\n     * will not be triggered when a user selects this action.\n     * @default true\n     */\n    opensAppToForeground?: boolean;\n  };\n}\n\n// @docsMissing\nexport interface NotificationCategory {\n  identifier: string;\n  actions: NotificationAction[];\n  options?: NotificationCategoryOptions;\n}\n\n/**\n * @platform ios\n */\nexport type NotificationCategoryOptions = {\n  /**\n   * Customizable placeholder for the notification preview text. This is shown if the user has disabled notification previews for the app.\n   * Defaults to the localized iOS system default placeholder (`Notification`).\n   */\n  previewPlaceholder?: string;\n  /**\n   * Array of [Intent Class Identifiers](https://developer.apple.com/documentation/sirikit/intent_class_identifiers). When a notification is delivered,\n   * the presence of an intent identifier lets the system know that the notification is potentially related to the handling of a request made through Siri.\n   * @default []\n   */\n  intentIdentifiers?: string[];\n  /**\n   * A format string for the summary description used when the system groups the category’s notifications.\n   */\n  categorySummaryFormat?: string;\n  /**\n   * Indicates whether to send actions for handling when the notification is dismissed (the user must explicitly dismiss\n   * the notification interface - ignoring a notification or flicking away a notification banner does not trigger this action).\n   * @default false\n   */\n  customDismissAction?: boolean;\n  /**\n   * Indicates whether to allow CarPlay to display notifications of this type. **Apps must be approved for CarPlay to make use of this feature.**\n   * @default false\n   */\n  allowInCarPlay?: boolean;\n  /**\n   * Indicates whether to show the notification's title, even if the user has disabled notification previews for the app.\n   * @default false\n   */\n  showTitle?: boolean;\n  /**\n   * Indicates whether to show the notification's subtitle, even if the user has disabled notification previews for the app.\n   * @default false\n   */\n  showSubtitle?: boolean;\n  /**\n   * @deprecated the option is ignored by iOS. This option will be removed in a future release.\n   * Indicates whether to allow notifications to be automatically read by Siri when the user is using AirPods.\n   * @default false\n   */\n  allowAnnouncement?: boolean;\n};\n\nexport type MaybeNotificationResponse = NotificationResponse | null | undefined;\n\n/**\n * @deprecated use the [`EventSubscription`](#eventsubscription) type instead\n * */\nexport type Subscription = EventSubscription;\n\nexport {\n  PermissionExpiration,\n  PermissionResponse,\n  EventSubscription,\n  PermissionStatus,\n} from 'expo-modules-core';\n\n/**\n * Payload for the background notification handler task.\n * [Read more](#run-javascript-in-response-to-incoming-notifications).\n * */\nexport type NotificationTaskPayload =\n  | NotificationResponse\n  | {\n      /**\n       * Object describing the remote notification. `null` for headless background notifications.\n       */\n      notification: Record<string, unknown> | null;\n      /**\n       * `dataString` carries the data payload of the notification as JSON string.\n       */\n      data: {\n        dataString?: string;\n        [key: string]: unknown;\n      };\n      /**\n       * Detailed, raw object describing the remote notification. [See more](https://developer.apple.com/documentation/usernotifications/generating-a-remote-notification#Payload-key-reference).\n       * @platform ios\n       */\n      aps?: Record<string, unknown>;\n    };\n"]}