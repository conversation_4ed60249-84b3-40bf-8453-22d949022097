"""
Tests for Module 5: Recommendations Service
"""

import pytest
import json
from unittest.mock import AsyncMock, patch

from app.services.recommendations import recommendations_service, RecommendationsService


class TestRecommendationsService:
    """Test the recommendations service functionality."""
    
    @pytest.mark.asyncio
    async def test_generate_weekly_recommendations_success(
        self, 
        sample_weekly_entries, 
        sample_user_traits,
        mock_gemini_client
    ):
        """Test successful weekly recommendations generation."""
        
        # Mock Gemini response with function call
        mock_response = {
            "response": json.dumps({
                "name": "generate_recommendations",
                "args": {
                    "recommendations": [
                        {
                            "category": "wellness",
                            "title": "Prioritize Sleep",
                            "description": "Focus on getting consistent 7-8 hours of sleep",
                            "priority": "high",
                            "actionable_steps": ["Set bedtime routine", "Avoid screens before bed"],
                            "reasoning": "Sleep affects mood and energy levels",
                            "difficulty": "moderate",
                            "timeframe": "this week"
                        },
                        {
                            "category": "habits",
                            "title": "Build Exercise Consistency",
                            "description": "Create a sustainable exercise routine",
                            "priority": "medium",
                            "actionable_steps": ["Schedule workout times", "Start with 20 minutes"],
                            "reasoning": "Exercise improves overall wellbeing",
                            "difficulty": "moderate",
                            "timeframe": "next two weeks"
                        }
                    ]
                }
            }),
            "type": "function_call",
            "tokens": {"input": 100, "output": 50, "total": 150}
        }
        
        with patch.object(recommendations_service.client, 'call_gemini', return_value=mock_response):
            stats = {
                "quantitative_metrics": {
                    "sleep_hours": {"mean": 6.2, "trend": "declining"},
                    "mood_score": {"mean": 7.5, "trend": "stable"}
                },
                "habit_metrics": {
                    "exercise": {"completion_rate": 0.4}
                }
            }
            
            correlations = {
                "insights": [
                    {"insight": "Sleep quality affects mood", "strength": "strong"}
                ]
            }
            
            result = await recommendations_service.generate_weekly_recommendations(
                user_id="test_user",
                entries=sample_weekly_entries,
                stats=stats,
                correlations=correlations,
                user_traits=sample_user_traits
            )
            
            # Assertions
            assert isinstance(result, list)
            assert len(result) == 2
            
            # Check first recommendation
            rec1 = result[0]
            assert rec1["category"] == "wellness"
            assert rec1["title"] == "Prioritize Sleep"
            assert rec1["priority"] == "high"
            assert isinstance(rec1["actionable_steps"], list)
            assert len(rec1["actionable_steps"]) > 0
            
            # Check second recommendation
            rec2 = result[1]
            assert rec2["category"] == "habits"
            assert rec2["title"] == "Build Exercise Consistency"
            assert rec2["priority"] == "medium"
    
    @pytest.mark.asyncio
    async def test_generate_habit_recommendations(
        self, 
        sample_user_traits,
        mock_gemini_client
    ):
        """Test habit-specific recommendations generation."""
        
        mock_response = {
            "response": json.dumps([
                {
                    "category": "habits",
                    "title": "Improve Meditation Consistency",
                    "description": "Build a sustainable daily meditation practice",
                    "priority": "high",
                    "actionable_steps": ["Start with 5 minutes", "Use guided meditations", "Set reminder"],
                    "reasoning": "Consistency is key for meditation benefits",
                    "difficulty": "easy",
                    "timeframe": "this week"
                }
            ]),
            "type": "text",
            "tokens": {"input": 50, "output": 30, "total": 80}
        }
        
        with patch.object(recommendations_service.client, 'call_gemini', return_value=mock_response):
            habit_data = {
                "completion_rate": 0.3,
                "average_duration": 8,
                "consistency_score": 0.4
            }
            
            result = await recommendations_service.generate_habit_recommendations(
                user_id="test_user",
                habit_name="meditation",
                habit_data=habit_data,
                user_traits=sample_user_traits
            )
            
            # Assertions
            assert isinstance(result, list)
            assert len(result) >= 1
            
            rec = result[0]
            assert rec["category"] == "habits"
            assert "meditation" in rec["title"].lower() or "meditation" in rec["description"].lower()
            assert isinstance(rec["actionable_steps"], list)
    
    @pytest.mark.asyncio
    async def test_generate_correlation_recommendations(
        self, 
        sample_user_traits,
        mock_gemini_client
    ):
        """Test correlation-based recommendations."""
        
        mock_response = {
            "response": "Based on your data, try exercising in the morning when your energy is highest. This could help you maintain consistency and get better results from your workouts.",
            "type": "text",
            "tokens": {"input": 40, "output": 25, "total": 65}
        }
        
        with patch.object(recommendations_service.client, 'call_gemini', return_value=mock_response):
            correlation = {
                "insight": "Exercise performance correlates with morning energy levels",
                "strength": "moderate",
                "variables": ["exercise_minutes", "morning_energy"]
            }
            
            result = await recommendations_service.generate_correlation_recommendations(
                user_id="test_user",
                correlation=correlation,
                user_traits=sample_user_traits
            )
            
            # Assertions
            assert isinstance(result, dict)
            assert result["category"] == "insights"
            assert "title" in result
            assert "description" in result
            assert isinstance(result["actionable_steps"], list)
    
    @pytest.mark.asyncio
    async def test_fallback_recommendations(self, sample_user_traits):
        """Test fallback recommendations when AI fails."""
        
        # Test with formal tone
        formal_traits = {**sample_user_traits, "tone": "formal"}
        
        result = recommendations_service._get_fallback_recommendations(formal_traits)
        
        assert isinstance(result, list)
        assert len(result) >= 2
        
        # Check formal language
        rec = result[0]
        assert rec["category"] in ["mindfulness", "habits", "wellness"]
        assert isinstance(rec["actionable_steps"], list)
        
        # Test with informal tone
        informal_traits = {**sample_user_traits, "tone": "informal"}
        
        result = recommendations_service._get_fallback_recommendations(informal_traits)
        
        assert isinstance(result, list)
        assert len(result) >= 2
        
        # Check informal language
        rec = result[0]
        assert rec["category"] in ["wellness", "goals", "habits"]
    
    def test_validate_recommendations(self):
        """Test recommendation validation."""
        
        # Valid recommendations
        valid_recs = [
            {
                "category": "wellness",
                "title": "Test Recommendation",
                "description": "Test description",
                "priority": "high",
                "actionable_steps": ["step 1", "step 2"],
                "reasoning": "Test reasoning"
            },
            {
                "category": "habits",
                "title": "Another Recommendation",
                "description": "Another description"
                # Missing some optional fields
            }
        ]
        
        result = recommendations_service._validate_recommendations(valid_recs)
        
        assert len(result) == 2
        
        # Check first recommendation (complete)
        rec1 = result[0]
        assert rec1["category"] == "wellness"
        assert rec1["title"] == "Test Recommendation"
        assert rec1["priority"] == "high"
        
        # Check second recommendation (with defaults)
        rec2 = result[1]
        assert rec2["category"] == "habits"
        assert rec2["priority"] == "medium"  # Default
        assert rec2["difficulty"] == "moderate"  # Default
        assert isinstance(rec2["actionable_steps"], list)
        
        # Invalid recommendations should be filtered out
        invalid_recs = [
            {"title": "Missing category"},  # Missing required fields
            "not a dict",  # Wrong type
            {}  # Empty dict
        ]
        
        result = recommendations_service._validate_recommendations(invalid_recs)
        assert len(result) == 0
    
    @pytest.mark.asyncio
    async def test_parse_recommendations_from_text(self):
        """Test parsing recommendations from text response."""
        
        # Test with valid JSON in text
        text_with_json = '''Here are your recommendations:
        [
            {
                "category": "wellness",
                "title": "Get Better Sleep",
                "description": "Improve your sleep quality",
                "priority": "high",
                "actionable_steps": ["Set bedtime", "Avoid caffeine"]
            }
        ]
        Hope this helps!'''
        
        result = await recommendations_service._parse_recommendations_from_text(text_with_json)
        
        assert isinstance(result, list)
        assert len(result) >= 1
        
        if len(result) > 0 and "Get Better Sleep" in str(result):
            # Successfully parsed JSON
            rec = next((r for r in result if r.get("title") == "Get Better Sleep"), None)
            if rec:
                assert rec["category"] == "wellness"
                assert rec["priority"] == "high"
        
        # Test with plain text (no JSON)
        plain_text = "You should focus on getting better sleep and exercising more regularly."
        
        result = await recommendations_service._parse_recommendations_from_text(plain_text)
        
        assert isinstance(result, list)
        assert len(result) == 1
        
        rec = result[0]
        assert rec["category"] == "general"
        assert rec["title"] == "Personal Growth Opportunity"
        assert plain_text in rec["description"]
    
    def test_create_recommendations_user_prompt(self, sample_weekly_entries):
        """Test user prompt creation for recommendations."""
        
        stats = {
            "quantitative_metrics": {
                "mood_score": {"mean": 7.5, "trend": "improving"},
                "sleep_hours": {"mean": 6.2, "trend": "declining"}
            },
            "habit_metrics": {
                "exercise": {"completion_rate": 0.6},
                "meditation": {"completion_rate": 0.3}
            }
        }
        
        correlations = {
            "insights": [
                {"insight": "Sleep affects mood significantly", "strength": "strong"},
                {"insight": "Exercise boosts energy levels", "strength": "moderate"}
            ]
        }
        
        prompt = recommendations_service._create_recommendations_user_prompt(
            sample_weekly_entries, stats, correlations
        )
        
        assert isinstance(prompt, str)
        assert "USER DATA ANALYSIS" in prompt
        assert "improving" in prompt  # Should mention improving mood
        assert "declining" in prompt  # Should mention declining sleep
        assert "exercise" in prompt.lower()
        assert "meditation" in prompt.lower()
        assert "Sleep affects mood" in prompt
    
    def test_create_recommendations_system_prompt(self, sample_user_traits):
        """Test system prompt creation for recommendations."""
        
        prompt = recommendations_service._create_recommendations_system_prompt(
            sample_user_traits, "Previous feedback: More specific tips please"
        )
        
        assert isinstance(prompt, str)
        assert "life coach" in prompt.lower()
        assert sample_user_traits["tone"] in prompt
        assert sample_user_traits["style"] in prompt
        assert "Previous feedback" in prompt
        assert "generate_recommendations function" in prompt
        
        # Test without feedback
        prompt_no_feedback = recommendations_service._create_recommendations_system_prompt(
            sample_user_traits, None
        )
        
        assert "Previous feedback" not in prompt_no_feedback
    
    @pytest.mark.asyncio
    async def test_error_handling(self, sample_weekly_entries, sample_user_traits):
        """Test error handling in recommendations generation."""
        
        # Mock Gemini client to raise an exception
        with patch.object(recommendations_service.client, 'call_gemini', side_effect=Exception("API Error")):
            stats = {"quantitative_metrics": {}, "habit_metrics": {}}
            correlations = {"insights": []}
            
            result = await recommendations_service.generate_weekly_recommendations(
                user_id="test_user",
                entries=sample_weekly_entries,
                stats=stats,
                correlations=correlations,
                user_traits=sample_user_traits
            )
            
            # Should return fallback recommendations
            assert isinstance(result, list)
            assert len(result) >= 2
            
            # Should be valid recommendations
            for rec in result:
                assert "title" in rec
                assert "description" in rec
                assert "category" in rec
                assert isinstance(rec["actionable_steps"], list)


class TestRecommendationsIntegration:
    """Integration tests for recommendations service."""
    
    @pytest.mark.asyncio
    async def test_recommendations_service_instance(self):
        """Test that the global service instance is properly configured."""
        
        assert recommendations_service is not None
        assert isinstance(recommendations_service, RecommendationsService)
        assert recommendations_service.client is not None
    
    def test_recommendation_categories(self):
        """Test that all expected recommendation categories are supported."""
        
        expected_categories = ["habits", "wellness", "productivity", "mindfulness", "social", "goals"]
        
        # This is tested implicitly through the function schema in generate_weekly_recommendations
        # The schema defines these exact categories as valid enum values
        
        # Test fallback recommendations include valid categories
        formal_recs = recommendations_service._get_fallback_recommendations({"tone": "formal"})
        informal_recs = recommendations_service._get_fallback_recommendations({"tone": "informal"})
        
        all_categories = set()
        for rec in formal_recs + informal_recs:
            all_categories.add(rec["category"])
        
        # Should use valid categories
        for category in all_categories:
            assert category in expected_categories + ["general", "insights"]  # Allow additional categories
    
    def test_recommendation_structure(self):
        """Test that recommendations have the expected structure."""
        
        recs = recommendations_service._get_fallback_recommendations({"tone": "informal"})
        
        required_fields = ["category", "title", "description", "priority", "actionable_steps", "reasoning"]
        optional_fields = ["expected_impact", "difficulty", "timeframe"]
        
        for rec in recs:
            # Check required fields
            for field in required_fields:
                assert field in rec, f"Missing required field: {field}"
                assert rec[field] is not None, f"Field {field} should not be None"
            
            # Check field types
            assert isinstance(rec["actionable_steps"], list)
            assert len(rec["actionable_steps"]) > 0
            assert rec["priority"] in ["high", "medium", "low"]
            
            if "difficulty" in rec:
                assert rec["difficulty"] in ["easy", "moderate", "challenging"]
