"""
Module 6: Validation Service
Detects outliers and inconsistencies using Python logic and mini-Gemini calls.
"""

import numpy as np
import pandas as pd
import json
import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from scipy import stats

from .gemini_client import gemini_client

logger = logging.getLogger(__name__)


class ValidationService:
    """Service for validating data quality and detecting anomalies."""
    
    def __init__(self):
        self.client = gemini_client
        self.outlier_threshold = 2.5  # Z-score threshold for outliers
        self.consistency_threshold = 0.7  # Minimum consistency score
    
    async def validate_weekly_data(
        self,
        user_id: str,
        entries: List[Dict[str, Any]],
        stats: Dict[str, Any],
        user_traits: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Validate weekly data for outliers and inconsistencies.
        
        Args:
            user_id: User identifier for token tracking
            entries: List of journal entries
            stats: Weekly statistics
            user_traits: User personality traits
            
        Returns:
            Dict containing validation results and flags
        """
        try:
            validation_results = {
                "data_quality_score": 0.0,
                "outliers": [],
                "inconsistencies": [],
                "missing_data": [],
                "quality_flags": [],
                "recommendations": [],
                "summary": {}
            }
            
            # Convert entries to DataFrame for analysis
            df = self._entries_to_dataframe(entries)
            
            # Detect statistical outliers
            outliers = self._detect_statistical_outliers(df)
            validation_results["outliers"] = outliers
            
            # Check for inconsistencies
            inconsistencies = await self._detect_inconsistencies(user_id, entries, user_traits)
            validation_results["inconsistencies"] = inconsistencies
            
            # Check for missing data patterns
            missing_data = self._detect_missing_data(df, entries)
            validation_results["missing_data"] = missing_data
            
            # Generate quality flags
            quality_flags = self._generate_quality_flags(outliers, inconsistencies, missing_data)
            validation_results["quality_flags"] = quality_flags
            
            # Calculate overall data quality score
            quality_score = self._calculate_quality_score(validation_results)
            validation_results["data_quality_score"] = quality_score
            
            # Generate validation recommendations
            recommendations = await self._generate_validation_recommendations(
                user_id, validation_results, user_traits
            )
            validation_results["recommendations"] = recommendations
            
            # Create summary
            validation_results["summary"] = self._create_validation_summary(validation_results)
            
            return validation_results
            
        except Exception as e:
            logger.error(f"Error validating weekly data: {e}")
            return self._get_empty_validation_results(f"Validation error: {str(e)}")
    
    def _entries_to_dataframe(self, entries: List[Dict[str, Any]]) -> pd.DataFrame:
        """Convert entries to DataFrame for statistical analysis."""
        data = []
        
        for entry in entries:
            try:
                # Parse metrics
                metrics = entry.get("metrics", {})
                if isinstance(metrics, str):
                    metrics = json.loads(metrics)
                
                # Create row with date and metrics
                row = {
                    "date": pd.to_datetime(entry.get("date", entry.get("created_at"))),
                    "entry_length": len(entry.get("text", "")),
                    "reflection_length": len(entry.get("reflection", "")),
                    **{k: v for k, v in metrics.items() if isinstance(v, (int, float))}
                }
                data.append(row)
                
            except Exception as e:
                logger.warning(f"Error processing entry for validation: {e}")
                continue
        
        if not data:
            return pd.DataFrame()
        
        df = pd.DataFrame(data)
        df.set_index("date", inplace=True)
        df.sort_index(inplace=True)
        
        return df
    
    def _detect_statistical_outliers(self, df: pd.DataFrame) -> List[Dict[str, Any]]:
        """Detect statistical outliers using Z-score and IQR methods."""
        outliers = []
        
        if df.empty:
            return outliers
        
        numeric_columns = df.select_dtypes(include=[np.number]).columns
        
        for column in numeric_columns:
            series = df[column].dropna()
            if len(series) < 3:  # Need at least 3 points
                continue
            
            try:
                # Z-score method
                z_scores = np.abs(stats.zscore(series))
                z_outliers = series[z_scores > self.outlier_threshold]
                
                # IQR method
                Q1 = series.quantile(0.25)
                Q3 = series.quantile(0.75)
                IQR = Q3 - Q1
                lower_bound = Q1 - 1.5 * IQR
                upper_bound = Q3 + 1.5 * IQR
                iqr_outliers = series[(series < lower_bound) | (series > upper_bound)]
                
                # Combine outliers
                all_outliers = pd.concat([z_outliers, iqr_outliers]).drop_duplicates()
                
                for date, value in all_outliers.items():
                    outlier = {
                        "metric": column,
                        "date": date.isoformat(),
                        "value": float(value),
                        "z_score": float(z_scores.loc[date]) if date in z_scores.index else None,
                        "series_mean": float(series.mean()),
                        "series_std": float(series.std()),
                        "severity": self._classify_outlier_severity(z_scores.loc[date] if date in z_scores.index else 0),
                        "method": "statistical"
                    }
                    outliers.append(outlier)
                    
            except Exception as e:
                logger.warning(f"Error detecting outliers for {column}: {e}")
                continue
        
        return outliers
    
    async def _detect_inconsistencies(
        self,
        user_id: str,
        entries: List[Dict[str, Any]],
        user_traits: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """Detect logical inconsistencies using AI analysis."""
        inconsistencies = []
        
        if len(entries) < 2:
            return inconsistencies
        
        try:
            # Prepare data for AI analysis
            entry_summaries = []
            for i, entry in enumerate(entries):
                summary = {
                    "day": i + 1,
                    "date": entry.get("date", ""),
                    "metrics": entry.get("metrics", {}),
                    "habits": entry.get("habits", []),
                    "qualitative_habits": entry.get("qualitative_habits", []),
                    "reflection": entry.get("reflection", "")[:200]  # Truncate for analysis
                }
                entry_summaries.append(summary)
            
            system_prompt = f"""You are a data quality analyst who identifies logical inconsistencies in personal journal data.

USER PROFILE:
- Communication style: {user_traits.get('tone', 'informal')} and {user_traits.get('style', 'conversational')}
- Personality traits: {json.dumps(user_traits.get('traits', {}), indent=2)}

Analyze the journal entries for logical inconsistencies such as:
1. Contradictory statements (e.g., "slept 8 hours" but "mood very low due to lack of sleep")
2. Impossible values (e.g., 25 hours of sleep, negative mood scores)
3. Inconsistent patterns (e.g., claims of daily exercise but 0 exercise minutes)
4. Conflicting habit reports (e.g., "didn't meditate" but meditation_minutes > 0)

Return findings as JSON array:
[
  {{
    "type": "contradiction|impossible_value|pattern_inconsistency|conflicting_data",
    "description": "Clear description of the inconsistency",
    "affected_entries": ["date1", "date2"],
    "severity": "high|medium|low",
    "suggestion": "How to resolve this inconsistency"
  }}
]

If no significant inconsistencies are found, return an empty array []."""
            
            user_prompt = f"Analyze these journal entries for inconsistencies:\n\n{json.dumps(entry_summaries, indent=2)}"
            
            response = await self.client.call_gemini(
                system=system_prompt,
                user=user_prompt,
                temperature=0.3,  # Lower temperature for analytical tasks
                max_tokens=1000,
                user_id=user_id
            )
            
            # Parse response
            try:
                # Extract JSON from response
                import re
                json_match = re.search(r'\[.*\]', response["response"], re.DOTALL)
                if json_match:
                    inconsistencies_data = json.loads(json_match.group())
                    inconsistencies = self._validate_inconsistencies(inconsistencies_data)
            except:
                # If parsing fails, create a general inconsistency note
                if "inconsistency" in response["response"].lower() or "contradiction" in response["response"].lower():
                    inconsistencies = [{
                        "type": "general",
                        "description": "Potential inconsistencies detected in data patterns",
                        "affected_entries": [],
                        "severity": "low",
                        "suggestion": "Review recent entries for accuracy"
                    }]
            
        except Exception as e:
            logger.warning(f"Error detecting inconsistencies: {e}")
        
        return inconsistencies
    
    def _detect_missing_data(self, df: pd.DataFrame, entries: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Detect patterns of missing data."""
        missing_data = []
        
        if df.empty:
            return missing_data
        
        # Check for missing metrics
        numeric_columns = df.select_dtypes(include=[np.number]).columns
        
        for column in numeric_columns:
            missing_count = df[column].isna().sum()
            total_count = len(df)
            missing_percentage = (missing_count / total_count) * 100
            
            if missing_percentage > 30:  # More than 30% missing
                missing_data.append({
                    "type": "missing_metric",
                    "metric": column,
                    "missing_count": int(missing_count),
                    "total_count": int(total_count),
                    "missing_percentage": float(missing_percentage),
                    "severity": "high" if missing_percentage > 70 else "medium"
                })
        
        # Check for missing reflections
        empty_reflections = sum(1 for entry in entries if not entry.get("reflection", "").strip())
        if empty_reflections > 0:
            reflection_missing_percentage = (empty_reflections / len(entries)) * 100
            if reflection_missing_percentage > 20:
                missing_data.append({
                    "type": "missing_reflection",
                    "missing_count": empty_reflections,
                    "total_count": len(entries),
                    "missing_percentage": float(reflection_missing_percentage),
                    "severity": "medium" if reflection_missing_percentage > 50 else "low"
                })
        
        # Check for missing habits data
        empty_habits = sum(1 for entry in entries if not entry.get("habits", []) and not entry.get("qualitative_habits", []))
        if empty_habits > 0:
            habits_missing_percentage = (empty_habits / len(entries)) * 100
            if habits_missing_percentage > 30:
                missing_data.append({
                    "type": "missing_habits",
                    "missing_count": empty_habits,
                    "total_count": len(entries),
                    "missing_percentage": float(habits_missing_percentage),
                    "severity": "medium"
                })
        
        return missing_data
    
    def _generate_quality_flags(
        self,
        outliers: List[Dict[str, Any]],
        inconsistencies: List[Dict[str, Any]],
        missing_data: List[Dict[str, Any]]
    ) -> List[str]:
        """Generate quality flags based on validation results."""
        flags = []
        
        # Outlier flags
        high_severity_outliers = [o for o in outliers if o.get("severity") == "high"]
        if len(high_severity_outliers) > 2:
            flags.append("multiple_high_outliers")
        elif len(outliers) > 5:
            flags.append("many_outliers")
        
        # Inconsistency flags
        high_severity_inconsistencies = [i for i in inconsistencies if i.get("severity") == "high"]
        if high_severity_inconsistencies:
            flags.append("high_inconsistency")
        elif inconsistencies:
            flags.append("minor_inconsistencies")
        
        # Missing data flags
        high_missing = [m for m in missing_data if m.get("severity") == "high"]
        if high_missing:
            flags.append("significant_missing_data")
        elif missing_data:
            flags.append("some_missing_data")
        
        # Overall quality flags
        if not flags:
            flags.append("high_quality_data")
        elif len(flags) > 3:
            flags.append("data_quality_concerns")
        
        return flags
    
    def _calculate_quality_score(self, validation_results: Dict[str, Any]) -> float:
        """Calculate overall data quality score (0-1)."""
        score = 1.0
        
        # Deduct for outliers
        outliers = validation_results.get("outliers", [])
        high_outliers = len([o for o in outliers if o.get("severity") == "high"])
        score -= min(0.3, high_outliers * 0.1)
        
        # Deduct for inconsistencies
        inconsistencies = validation_results.get("inconsistencies", [])
        high_inconsistencies = len([i for i in inconsistencies if i.get("severity") == "high"])
        score -= min(0.4, high_inconsistencies * 0.15)
        
        # Deduct for missing data
        missing_data = validation_results.get("missing_data", [])
        for missing in missing_data:
            missing_pct = missing.get("missing_percentage", 0)
            score -= min(0.3, missing_pct / 100 * 0.5)
        
        return max(0.0, score)
    
    async def _generate_validation_recommendations(
        self,
        user_id: str,
        validation_results: Dict[str, Any],
        user_traits: Dict[str, Any]
    ) -> List[str]:
        """Generate recommendations based on validation results."""
        recommendations = []
        
        # Recommendations for outliers
        outliers = validation_results.get("outliers", [])
        if outliers:
            recommendations.append("Review entries with unusual values to ensure accuracy")
        
        # Recommendations for inconsistencies
        inconsistencies = validation_results.get("inconsistencies", [])
        if inconsistencies:
            recommendations.append("Check for contradictory information in your entries")
        
        # Recommendations for missing data
        missing_data = validation_results.get("missing_data", [])
        for missing in missing_data:
            if missing["type"] == "missing_reflection":
                recommendations.append("Try to include a brief reflection in each entry")
            elif missing["type"] == "missing_habits":
                recommendations.append("Consider tracking at least a few key habits daily")
            elif missing["type"] == "missing_metric":
                metric = missing["metric"].replace("_", " ")
                recommendations.append(f"Consider tracking {metric} more consistently")
        
        # General recommendations
        quality_score = validation_results.get("data_quality_score", 1.0)
        if quality_score < 0.7:
            recommendations.append("Focus on data accuracy and completeness for better insights")
        
        return recommendations[:5]  # Limit to 5 recommendations
    
    def _classify_outlier_severity(self, z_score: float) -> str:
        """Classify outlier severity based on Z-score."""
        abs_z = abs(z_score)
        if abs_z > 3.5:
            return "high"
        elif abs_z > 2.5:
            return "medium"
        else:
            return "low"
    
    def _validate_inconsistencies(self, inconsistencies_data: List[Dict]) -> List[Dict[str, Any]]:
        """Validate and clean inconsistencies data."""
        validated = []
        
        for item in inconsistencies_data:
            if isinstance(item, dict) and "description" in item:
                validated_item = {
                    "type": item.get("type", "general"),
                    "description": item.get("description", ""),
                    "affected_entries": item.get("affected_entries", []),
                    "severity": item.get("severity", "medium"),
                    "suggestion": item.get("suggestion", "Review and correct if needed")
                }
                validated.append(validated_item)
        
        return validated
    
    def _create_validation_summary(self, validation_results: Dict[str, Any]) -> Dict[str, Any]:
        """Create summary of validation results."""
        return {
            "total_outliers": len(validation_results.get("outliers", [])),
            "total_inconsistencies": len(validation_results.get("inconsistencies", [])),
            "total_missing_data_issues": len(validation_results.get("missing_data", [])),
            "quality_flags_count": len(validation_results.get("quality_flags", [])),
            "overall_quality": "high" if validation_results.get("data_quality_score", 0) > 0.8 else 
                             "medium" if validation_results.get("data_quality_score", 0) > 0.6 else "low"
        }
    
    def _get_empty_validation_results(self, reason: str = "No data") -> Dict[str, Any]:
        """Return empty validation results."""
        return {
            "data_quality_score": 0.0,
            "outliers": [],
            "inconsistencies": [],
            "missing_data": [],
            "quality_flags": ["insufficient_data"],
            "recommendations": ["Collect more data for meaningful validation"],
            "summary": {
                "total_outliers": 0,
                "total_inconsistencies": 0,
                "total_missing_data_issues": 0,
                "quality_flags_count": 1,
                "overall_quality": "unknown",
                "reason": reason
            }
        }


# Global service instance
validation_service = ValidationService()
