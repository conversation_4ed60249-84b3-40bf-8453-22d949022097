"""
Usage tracking model for monitoring token consumption.
"""

from sqlalchemy import Column, Integer, String, DateTime, Date
from sqlalchemy.sql import func
from ..database import Base


class TokenUsage(Base):
    """Token usage tracking by user and month."""
    
    __tablename__ = "usage"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(String(255), nullable=False, index=True)
    
    # Time period
    month = Column(Date, nullable=False)  # First day of the month
    
    # Token counts
    tokens_used = Column(Integer, nullable=False, default=0)
    input_tokens = Column(Integer, nullable=False, default=0)
    output_tokens = Column(Integer, nullable=False, default=0)
    
    # Request counts
    api_calls = Column(Integer, nullable=False, default=0)
    
    # Plan information
    plan = Column(String(20), nullable=False, default="FREE")
    token_limit = Column(Integer, nullable=False, default=10000)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    def __repr__(self):
        return f"<TokenUsage(user_id={self.user_id}, month={self.month}, tokens={self.tokens_used})>"


class UserSubscription(Base):
    """User subscription and plan information."""
    
    __tablename__ = "subscriptions"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(String(255), nullable=False, unique=True, index=True)
    
    # Plan details
    plan = Column(String(20), nullable=False, default="FREE")
    status = Column(String(20), nullable=False, default="active")
    
    # Billing
    billing_cycle = Column(String(20), nullable=True)  # 'monthly', 'yearly'
    next_billing_date = Column(Date, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    def __repr__(self):
        return f"<UserSubscription(user_id={self.user_id}, plan={self.plan})>"
