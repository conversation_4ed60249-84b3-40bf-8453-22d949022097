{"expo": {"name": "HabitStory", "slug": "habit-story", "version": "1.0.0", "sdkVersion": "53.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "newArchEnabled": true, "splash": {"image": "./assets/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.habitstory.app"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true, "package": "com.habitstory.app", "permissions": ["NOTIFICATIONS", "SCHEDULE_EXACT_ALARM", "NOTIFICATIONS", "SCHEDULE_EXACT_ALARM"]}, "web": {"favicon": "./assets/favicon.png"}, "plugins": ["expo-sqlite", "expo-secure-store", ["expo-notifications", {"icon": "./assets/icon.png", "color": "#ffffff"}]], "extra": {"eas": {"projectId": "c4eb73a8-ddac-46f4-8ee0-75ad5ecded05"}}, "owner": "irochep", "runtimeVersion": "1.0.0", "updates": {"url": "https://u.expo.dev/c4eb73a8-ddac-46f4-8ee0-75ad5ecded05"}}}