"""
FastAPI main application for HabitStory backend.
Provides AI-powered habit tracking and analysis pipeline.
"""

from fastapi import FastAP<PERSON>, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager
import logging
from typing import AsyncGenerator

from .database import engine, Base
from .routers import entries, reports, feedback, auth
from .config import settings
from .tasks.cleanup import start_background_tasks
from .middleware import TokenRotationMiddleware, SecurityHeadersMiddleware
from .services.ai_service_init import initialize_ai_services

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI) -> AsyncGenerator[None, None]:
    """Application lifespan manager."""
    # Startup
    logger.info("Starting HabitStory backend...")
    
    # Create database tables
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    
    logger.info("Database tables created successfully")

    # Initialize AI services
    try:
        initialize_ai_services()
        logger.info("AI services initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize AI services: {e}")

    # Start background tasks
    start_background_tasks()

    yield
    
    # Shutdown
    logger.info("Shutting down HabitStory backend...")


# Create FastAPI app
app = FastAPI(
    title="HabitStory API",
    description="AI-powered habit tracking and analysis backend",
    version="1.0.0",
    lifespan=lifespan
)

# Add security middleware
app.add_middleware(SecurityHeadersMiddleware)
app.add_middleware(TokenRotationMiddleware)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(auth.router, prefix="/api/v1", tags=["auth"])
app.include_router(entries.router, prefix="/api/v1", tags=["entries"])
app.include_router(reports.router, prefix="/api/v1", tags=["reports"])
app.include_router(feedback.router, prefix="/api/v1", tags=["feedback"])


@app.get("/")
async def root():
    """Root endpoint."""
    return {
        "message": "HabitStory API",
        "version": "1.0.0",
        "status": "running"
    }


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {"status": "healthy"}


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True
    )
