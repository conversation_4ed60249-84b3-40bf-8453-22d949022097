{"version": 3, "file": "NotificationPermissionsModule.js", "sourceRoot": "", "sources": ["../src/NotificationPermissionsModule.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,gBAAgB,EAAE,QAAQ,EAAE,MAAM,mBAAmB,CAAC;AAQ/D,SAAS,uBAAuB,CAC9B,MAA0C;IAE1C,QAAQ,MAAM,EAAE,CAAC;QACf,KAAK,SAAS;YACZ,OAAO;gBACL,MAAM,EAAE,gBAAgB,CAAC,OAAO;gBAChC,OAAO,EAAE,OAAO;gBAChB,WAAW,EAAE,KAAK;gBAClB,OAAO,EAAE,IAAI;aACd,CAAC;QACJ,KAAK,QAAQ;YACX,OAAO;gBACL,MAAM,EAAE,gBAAgB,CAAC,MAAM;gBAC/B,OAAO,EAAE,OAAO;gBAChB,WAAW,EAAE,KAAK;gBAClB,OAAO,EAAE,KAAK;aACf,CAAC;QACJ;YACE,OAAO;gBACL,MAAM,EAAE,gBAAgB,CAAC,YAAY;gBACrC,OAAO,EAAE,OAAO;gBAChB,WAAW,EAAE,IAAI;gBACjB,OAAO,EAAE,KAAK;aACf,CAAC;IACN,CAAC;AACH,CAAC;AAED,KAAK,UAAU,sBAAsB,CAAC,EACpC,SAAS,GAGV;IACC,IAAI,CAAC,QAAQ,CAAC,cAAc,EAAE,CAAC;QAC7B,OAAO,uBAAuB,CAAC,QAAQ,CAAC,CAAC;IAC3C,CAAC;IAED,MAAM,EAAE,YAAY,GAAG,EAAE,EAAE,GAAG,MAAa,CAAC;IAC5C,IAAI,OAAO,YAAY,CAAC,iBAAiB,KAAK,WAAW,EAAE,CAAC;QAC1D,IAAI,MAAM,GAAG,YAAY,CAAC,UAAU,CAAC;QACrC,IAAI,SAAS,EAAE,CAAC;YACd,MAAM,GAAG,MAAM,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;gBAC7C,IAAI,QAAQ,GAAG,KAAK,CAAC;gBACrB,SAAS,WAAW,CAAC,MAAc;oBACjC,IAAI,CAAC,QAAQ,EAAE,CAAC;wBACd,QAAQ,GAAG,IAAI,CAAC;wBAChB,OAAO,CAAC,MAAM,CAAC,CAAC;oBAClB,CAAC;gBACH,CAAC;gBACD,sEAAsE;gBACtE,YAAY,CAAC,iBAAiB,CAAC,WAAW,CAAC,EAAE,IAAI,CAAC,WAAW,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;YAChF,CAAC,CAAC,CAAC;QACL,CAAC;QACD,OAAO,uBAAuB,CAAC,MAAM,CAAC,CAAC;IACzC,CAAC;SAAM,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,EAAE,WAAW,EAAE,KAAK,EAAE,CAAC;QAC7E,8DAA8D;QAC9D,MAAM,KAAK,GAAG,MAAM,SAAS,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE,IAAI,EAAE,eAAe,EAAE,CAAC,CAAC;QAC3E,OAAO,uBAAuB,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;IAC9C,CAAC;IACD,0EAA0E;IAC1E,OAAO,uBAAuB,CAAC,QAAQ,CAAC,CAAC;AAC3C,CAAC;AAED,eAAe;IACb,WAAW,EAAE,GAAG,EAAE,GAAE,CAAC;IACrB,eAAe,EAAE,GAAG,EAAE,GAAE,CAAC;IACzB,KAAK,CAAC,mBAAmB;QACvB,OAAO,sBAAsB,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC,CAAC;IACtD,CAAC;IACD,KAAK,CAAC,uBAAuB,CAC3B,OAA6C;QAE7C,OAAO,sBAAsB,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;IACrD,CAAC;CAC+B,CAAC", "sourcesContent": ["import { PermissionStatus, Platform } from 'expo-modules-core';\n\nimport {\n  NativeNotificationPermissionsRequest,\n  NotificationPermissionsStatus,\n} from './NotificationPermissions.types';\nimport { NotificationPermissionsModule } from './NotificationPermissionsModule.types';\n\nfunction convertPermissionStatus(\n  status?: NotificationPermission | 'prompt'\n): NotificationPermissionsStatus {\n  switch (status) {\n    case 'granted':\n      return {\n        status: PermissionStatus.GRANTED,\n        expires: 'never',\n        canAskAgain: false,\n        granted: true,\n      };\n    case 'denied':\n      return {\n        status: PermissionStatus.DENIED,\n        expires: 'never',\n        canAskAgain: false,\n        granted: false,\n      };\n    default:\n      return {\n        status: PermissionStatus.UNDETERMINED,\n        expires: 'never',\n        canAskAgain: true,\n        granted: false,\n      };\n  }\n}\n\nasync function resolvePermissionAsync({\n  shouldAsk,\n}: {\n  shouldAsk: boolean;\n}): Promise<NotificationPermissionsStatus> {\n  if (!Platform.isDOMAvailable) {\n    return convertPermissionStatus('denied');\n  }\n\n  const { Notification = {} } = window as any;\n  if (typeof Notification.requestPermission !== 'undefined') {\n    let status = Notification.permission;\n    if (shouldAsk) {\n      status = await new Promise((resolve, reject) => {\n        let resolved = false;\n        function resolveOnce(status: string) {\n          if (!resolved) {\n            resolved = true;\n            resolve(status);\n          }\n        }\n        // Some browsers require a callback argument and some return a Promise\n        Notification.requestPermission(resolveOnce)?.then(resolveOnce)?.catch(reject);\n      });\n    }\n    return convertPermissionStatus(status);\n  } else if (typeof navigator !== 'undefined' && navigator?.permissions?.query) {\n    // TODO(Bacon): Support `push` in the future when it's stable.\n    const query = await navigator.permissions.query({ name: 'notifications' });\n    return convertPermissionStatus(query.state);\n  }\n  // Platforms like iOS Safari don't support Notifications so return denied.\n  return convertPermissionStatus('denied');\n}\n\nexport default {\n  addListener: () => {},\n  removeListeners: () => {},\n  async getPermissionsAsync(): Promise<NotificationPermissionsStatus> {\n    return resolvePermissionAsync({ shouldAsk: false });\n  },\n  async requestPermissionsAsync(\n    request: NativeNotificationPermissionsRequest\n  ): Promise<NotificationPermissionsStatus> {\n    return resolvePermissionAsync({ shouldAsk: true });\n  },\n} as NotificationPermissionsModule;\n"]}