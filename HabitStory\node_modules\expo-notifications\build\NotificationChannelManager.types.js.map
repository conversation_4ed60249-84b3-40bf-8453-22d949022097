{"version": 3, "file": "NotificationChannelManager.types.js", "sourceRoot": "", "sources": ["../src/NotificationChannelManager.types.ts"], "names": [], "mappings": "AAEA,eAAe;AACf,MAAM,CAAN,IAAY,6BAKX;AALD,WAAY,6BAA6B;IACvC,uFAAW,CAAA;IACX,qFAAU,CAAA;IACV,uFAAW,CAAA;IACX,qFAAU,CAAA;AACZ,CAAC,EALW,6BAA6B,KAA7B,6BAA6B,QAKxC;AAED,eAAe;AACf,MAAM,CAAN,IAAY,uBAMX;AAND,WAAY,uBAAuB;IACjC,2EAAW,CAAA;IACX,yEAAU,CAAA;IACV,uEAAS,CAAA;IACT,uEAAS,CAAA;IACT,qFAAgB,CAAA;AAClB,CAAC,EANW,uBAAuB,KAAvB,uBAAuB,QAMlC;AAED,eAAe;AACf,MAAM,CAAN,IAAY,iBASX;AATD,WAAY,iBAAiB;IAC3B,+DAAW,CAAA;IACX,uEAAe,CAAA;IACf,yDAAQ,CAAA;IACR,uDAAO,CAAA;IACP,uDAAO,CAAA;IACP,+DAAW,CAAA;IACX,yDAAQ,CAAA;IACR,uDAAO,CAAA;AACT,CAAC,EATW,iBAAiB,KAAjB,iBAAiB,QAS5B;AAED,eAAe;AACf,MAAM,CAAN,IAAY,iBAgBX;AAhBD,WAAY,iBAAiB;IAC3B,+DAAW,CAAA;IACX,2DAAS,CAAA;IACT,uFAAuB,CAAA;IACvB,6GAAkC,CAAA;IAClC,2DAAS,CAAA;IACT,yEAAgB,CAAA;IAChB,2FAAyB,CAAA;IACzB,qHAAsC,CAAA;IACtC,qHAAsC,CAAA;IACtC,qHAAsC,CAAA;IACtC,sFAAuB,CAAA;IACvB,kGAA6B,CAAA;IAC7B,8GAAmC,CAAA;IACnC,gGAA4B,CAAA;IAC5B,0DAAS,CAAA;AACX,CAAC,EAhBW,iBAAiB,KAAjB,iBAAiB,QAgB5B", "sourcesContent": ["import { ProxyNativeModule } from 'expo-modules-core';\n\n// @docsMissing\nexport enum AndroidNotificationVisibility {\n  UNKNOWN = 0,\n  PUBLIC = 1,\n  PRIVATE = 2,\n  SECRET = 3,\n}\n\n// @docsMissing\nexport enum AndroidAudioContentType {\n  UNKNOWN = 0,\n  SPEECH = 1,\n  MUSIC = 2,\n  MOVIE = 3,\n  SONIFICATION = 4,\n}\n\n// @docsMissing\nexport enum AndroidImportance {\n  UNKNOWN = 0,\n  UNSPECIFIED = 1,\n  NONE = 2,\n  MIN = 3,\n  LOW = 4,\n  DEFAULT = 5,\n  HIGH = 6,\n  MAX = 7,\n}\n\n// @docsMissing\nexport enum AndroidAudioUsage {\n  UNKNOWN = 0,\n  MEDIA = 1,\n  VOICE_COMMUNICATION = 2,\n  VOICE_COMMUNICATION_SIGNALLING = 3,\n  ALARM = 4,\n  NOTIFICATION = 5,\n  NOTIFICATION_RINGTONE = 6,\n  NOTIFICATION_COMMUNICATION_REQUEST = 7,\n  NOTIFICATION_COMMUNICATION_INSTANT = 8,\n  NOTIFICATION_COMMUNICATION_DELAYED = 9,\n  NOTIFICATION_EVENT = 10,\n  ASSISTANCE_ACCESSIBILITY = 11,\n  ASSISTANCE_NAVIGATION_GUIDANCE = 12,\n  ASSISTANCE_SONIFICATION = 13,\n  GAME = 14,\n}\n\n// @docsMissing\nexport interface AudioAttributes {\n  usage: AndroidAudioUsage;\n  contentType: AndroidAudioContentType;\n  flags: {\n    enforceAudibility: boolean;\n    requestHardwareAudioVideoSynchronization: boolean;\n  };\n}\n\n// We're making inner flags required to set intentionally.\n// Not providing `true` for a flag makes it false, it doesn't make sense\n// to let it be left undefined.\nexport type AudioAttributesInput = Partial<AudioAttributes>;\n\n/**\n * An object which represents a notification channel.\n * @platform android\n */\nexport interface NotificationChannel {\n  id: string;\n  name: string | null;\n  importance: AndroidImportance;\n  bypassDnd: boolean;\n  description: string | null;\n  groupId?: string | null;\n  lightColor: string;\n  lockscreenVisibility: AndroidNotificationVisibility;\n  showBadge: boolean;\n  sound: 'default' | 'custom' | null;\n  audioAttributes: AudioAttributes;\n  vibrationPattern: number[] | null;\n  enableLights: boolean;\n  enableVibrate: boolean;\n}\n\nexport type RequiredBy<T, K extends keyof T> = Partial<Omit<T, K>> & Required<Pick<T, K>>;\n\n/**\n * An object which represents a notification channel to be set.\n * @platform android\n */\nexport type NotificationChannelInput = RequiredBy<\n  Omit<\n    NotificationChannel,\n    | 'id' // id is handled separately as a function argument\n    | 'audioAttributes' // need to make it AudioAttributesInput\n    | 'sound'\n  > & { audioAttributes?: AudioAttributesInput; sound?: string | null },\n  'name' | 'importance'\n>;\n\nexport interface NotificationChannelManager extends ProxyNativeModule {\n  getNotificationChannelsAsync?: () => Promise<NotificationChannel[] | null>;\n  getNotificationChannelAsync?: (channelId: string) => Promise<NotificationChannel | null>;\n  setNotificationChannelAsync?: (\n    channelId: string,\n    channelConfiguration: NotificationChannelInput\n  ) => Promise<NotificationChannel | null>;\n  deleteNotificationChannelAsync?: (channelId: string) => Promise<void>;\n}\n"]}