import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Alert,
  ActivityIndicator,
  Animated,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';
import * as SecureStore from 'expo-secure-store';

import { useOpenAI } from '../hooks/useOpenAI';

const SettingsScreen: React.FC = () => {
  const [apiKey, setApiKey] = useState('');
  const [userName, setUserName] = useState('');
  const [isTestingConnection, setIsTestingConnection] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [fadeAnim] = useState(new Animated.Value(0));

  const { testConnection, checkApiKey } = useOpenAI();

  useEffect(() => {
    loadSettings();
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 500,
      useNativeDriver: true,
    }).start();
  }, []);

  const loadSettings = async () => {
    try {
      const storedApiKey = await SecureStore.getItemAsync('openai_api_key');
      const storedUserName = await SecureStore.getItemAsync('user_name');

      if (storedApiKey) {
        setApiKey(storedApiKey);
      }
      if (storedUserName) {
        setUserName(storedUserName);
      }
    } catch (error) {
      console.error('Error loading settings:', error);
    }
  };

  const updateApiKey = async (newApiKey: string) => {
    try {
      await SecureStore.setItemAsync('openai_api_key', newApiKey);
    } catch (error) {
      console.error('Error saving API key:', error);
      throw error;
    }
  };

  const updateUserName = async (newUserName: string) => {
    try {
      await SecureStore.setItemAsync('user_name', newUserName);
    } catch (error) {
      console.error('Error saving user name:', error);
      throw error;
    }
  };

  const handleTestConnection = async () => {
    if (!apiKey.trim()) {
      Alert.alert('Missing API Key', 'Please enter your OpenAI API key first.');
      return;
    }

    Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    setIsTestingConnection(true);
    
    try {
      await updateApiKey(apiKey.trim());
      const isConnected = await testConnection();
      
      if (isConnected) {
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
        Alert.alert(
          'Connection Successful!',
          'Your OpenAI API key is working correctly.',
          [{ text: 'OK' }]
        );
      } else {
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
        Alert.alert(
          'Connection Failed',
          'Unable to connect to OpenAI. Please check your API key and internet connection.',
          [{ text: 'OK' }]
        );
      }
    } catch (error) {
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
      Alert.alert(
        'Connection Error',
        error instanceof Error ? error.message : 'Failed to test connection',
        [{ text: 'OK' }]
      );
    } finally {
      setIsTestingConnection(false);
    }
  };

  const handleSaveSettings = async () => {
    if (!userName.trim()) {
      Alert.alert('Missing Name', 'Please enter your name.');
      return;
    }

    if (!apiKey.trim()) {
      Alert.alert('Missing API Key', 'Please enter your OpenAI API key.');
      return;
    }

    Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    setIsSaving(true);

    try {
      await Promise.all([
        updateUserName(userName.trim()),
        updateApiKey(apiKey.trim())
      ]);

      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
      Alert.alert(
        'Settings Saved',
        'Your settings have been updated successfully.',
        [{ text: 'OK' }]
      );
    } catch (error) {
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
      Alert.alert(
        'Save Failed',
        error instanceof Error ? error.message : 'Failed to save settings',
        [{ text: 'OK' }]
      );
    } finally {
      setIsSaving(false);
    }
  };

  const handleLogout = () => {
    Alert.alert(
      'Clear Data',
      'Are you sure you want to clear all your data? This will remove your API key and settings from this device.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Clear Data',
          style: 'destructive',
          onPress: async () => {
            try {
              Haptics.notificationAsync(Haptics.NotificationFeedbackType.Warning);
              await SecureStore.deleteItemAsync('openai_api_key');
              await SecureStore.deleteItemAsync('user_name');
              setApiKey('');
              setUserName('');
              Alert.alert('Success', 'All data has been cleared.');
            } catch (error) {
              Alert.alert('Error', 'Failed to clear data');
            }
          },
        },
      ]
    );
  };

  return (
    <SafeAreaView className="flex-1 bg-gray-50">
      <ScrollView className="flex-1">
        <LinearGradient
          colors={['#4F46E5', '#7C3AED']}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          className="px-6 py-8 rounded-b-3xl"
        >
          <Text className="text-3xl font-bold text-white mb-2">
            Settings
          </Text>
          <Text className="text-white/90 text-lg">
            Configure your account and API
          </Text>
        </LinearGradient>

        <View className="px-6 py-4">
          {/* User Information */}
          <Animated.View
            style={{ opacity: fadeAnim }}
            className="bg-white/80 backdrop-blur-lg p-6 rounded-2xl shadow-lg mb-6"
          >
            <LinearGradient
              colors={['rgba(79, 70, 229, 0.1)', 'rgba(124, 58, 237, 0.1)']}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
              className="absolute inset-0 rounded-2xl"
            />
            <View className="flex-row items-center mb-4">
              <Ionicons name="person" size={24} color="#4F46E5" />
              <Text className="text-xl font-bold text-gray-800 ml-2">
                User Information
              </Text>
            </View>
            
            <View className="mb-4">
              <Text className="text-gray-700 font-medium mb-2">Name</Text>
              <View className="bg-white/60 rounded-xl overflow-hidden shadow-sm">
                <TextInput
                  className="p-4 text-base text-gray-700"
                  placeholder="Enter your name"
                  value={userName}
                  onChangeText={setUserName}
                  autoCapitalize="words"
                />
              </View>
            </View>
          </Animated.View>

          {/* API Configuration */}
          <View className="bg-white/80 backdrop-blur-lg p-6 rounded-2xl shadow-lg mb-6">
            <LinearGradient
              colors={['rgba(79, 70, 229, 0.1)', 'rgba(124, 58, 237, 0.1)']}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
              className="absolute inset-0 rounded-2xl"
            />
            <View className="flex-row items-center mb-4">
              <Ionicons name="key" size={24} color="#4F46E5" />
              <Text className="text-xl font-bold text-gray-800 ml-2">
                OpenAI API Configuration
              </Text>
            </View>
            
            <View className="mb-3">
              <Text className="text-gray-700 font-medium mb-2">API Key</Text>
              <View className="bg-white/60 rounded-xl overflow-hidden shadow-sm">
                <TextInput
                  className="p-4 text-base text-gray-700"
                  placeholder="sk-..."
                  value={apiKey}
                  onChangeText={setApiKey}
                  secureTextEntry={true}
                  autoCapitalize="none"
                  autoCorrect={false}
                />
              </View>
            </View>
            
            <Text className="text-gray-500 text-sm mb-4 flex-row items-center">
              <Ionicons name="shield-checkmark" size={16} color="#6B7280" />
              <Text> Your API key is stored securely on your device and never shared.</Text>
            </Text>

            <TouchableOpacity
              className={`rounded-xl py-4 px-6 mb-3 ${isTestingConnection || !apiKey.trim() ? 'bg-gray-300' : 'bg-gradient-to-r from-blue-600 to-purple-600'}`}
              onPress={handleTestConnection}
              disabled={isTestingConnection || !apiKey.trim()}
            >
              {isTestingConnection ? (
                <View className="flex-row items-center justify-center">
                  <ActivityIndicator color="white" size="small" />
                  <Text className="text-white font-semibold ml-2">Testing...</Text>
                </View>
              ) : (
                <View className="flex-row items-center justify-center">
                  <Ionicons name="cloud-done" size={20} color="white" />
                  <Text className="text-white font-semibold ml-2">Test Connection</Text>
                </View>
              )}
            </TouchableOpacity>
          </View>

          {/* API Key Help */}
          <View className="bg-white/80 backdrop-blur-lg p-6 rounded-2xl shadow-lg mb-6">
            <LinearGradient
              colors={['rgba(59, 130, 246, 0.1)', 'rgba(147, 197, 253, 0.1)']}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
              className="absolute inset-0 rounded-2xl"
            />
            <View className="flex-row items-center mb-4">
              <Ionicons name="information-circle" size={24} color="#3B82F6" />
              <Text className="text-xl font-bold text-blue-800 ml-2">
                How to get your API Key
              </Text>
            </View>
            
            <View className="space-y-2">
              {[
                { step: 1, text: 'Visit platform.openai.com' },
                { step: 2, text: 'Sign in or create an account' },
                { step: 3, text: 'Go to API Keys section' },
                { step: 4, text: 'Create a new secret key' },
                { step: 5, text: 'Copy and paste it above' },
              ].map((item) => (
                <View key={item.step} className="flex-row items-center">
                  <View className="w-6 h-6 rounded-full bg-blue-100 items-center justify-center mr-3">
                    <Text className="text-blue-800 font-medium">{item.step}</Text>
                  </View>
                  <Text className="text-blue-800">{item.text}</Text>
                </View>
              ))}
            </View>
          </View>

          {/* Action Buttons */}
          <TouchableOpacity
            className={`rounded-xl py-4 px-6 mb-4 ${isSaving || !userName.trim() || !apiKey.trim() ? 'bg-gray-300' : 'bg-gradient-to-r from-green-600 to-emerald-600'}`}
            onPress={handleSaveSettings}
            disabled={isSaving || !userName.trim() || !apiKey.trim()}
          >
            {isSaving ? (
              <View className="flex-row items-center justify-center">
                <ActivityIndicator color="white" size="small" />
                <Text className="text-white font-semibold ml-2">Saving...</Text>
              </View>
            ) : (
              <View className="flex-row items-center justify-center">
                <Ionicons name="save" size={20} color="white" />
                <Text className="text-white font-semibold text-lg ml-2">Save Settings</Text>
              </View>
            )}
          </TouchableOpacity>

          <TouchableOpacity
            className="rounded-xl py-4 px-6 mb-6 bg-gradient-to-r from-red-600 to-pink-600"
            onPress={handleLogout}
          >
            <View className="flex-row items-center justify-center">
              <Ionicons name="trash" size={20} color="white" />
              <Text className="text-white font-semibold text-lg ml-2">Clear All Data</Text>
            </View>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default SettingsScreen;
