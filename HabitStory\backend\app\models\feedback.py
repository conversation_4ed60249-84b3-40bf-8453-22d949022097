"""
Feedback model for storing user feedback on reports.
"""

from sqlalchemy import Column, Integer, String, Text, DateTime, ForeignKey
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from ..database import Base


class ReportFeedback(Base):
    """User feedback on weekly reports."""
    
    __tablename__ = "feedback"
    
    id = Column(Integer, primary_key=True, index=True)
    report_id = Column(Integer, ForeignKey("reports.id"), nullable=False)
    
    # Feedback data
    sentiment = Column(String(20), nullable=False)  # 'positive', 'negative', 'neutral'
    rating = Column(Integer, nullable=True)  # 1-5 scale
    comment = Column(Text, nullable=True)
    
    # Specific feedback categories
    content_helpful = Column(Integer, nullable=True)  # 1-5 scale
    tone_appropriate = Column(Integer, nullable=True)  # 1-5 scale
    insights_valuable = Column(Integer, nullable=True)  # 1-5 scale
    recommendations_useful = Column(Integer, nullable=True)  # 1-5 scale
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationship
    report = relationship("WeeklyReport", backref="feedback")
    
    def __repr__(self):
        return f"<ReportFeedback(report_id={self.report_id}, sentiment={self.sentiment})>"
