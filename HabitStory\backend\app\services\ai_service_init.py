"""
Initialize and register AI clients with the unified service.
"""

import logging
from .ai_client import AIClientFactory, AIProvider, unified_ai_service
from .gemini_ai_client import gemini_ai_client
from .openai_ai_client import openai_ai_client

logger = logging.getLogger(__name__)


def initialize_ai_services():
    """Initialize and register all available AI clients."""
    registered_count = 0
    
    # Register Gemini client
    try:
        if gemini_ai_client and gemini_ai_client.api_key:
            AIClientFactory.register_client(AIProvider.GEMINI, gemini_ai_client)
            registered_count += 1
            logger.info("Gemini AI client registered successfully")
        else:
            logger.warning("Gemini AI client not registered - API key missing")
    except Exception as e:
        logger.error(f"Failed to register Gemini client: {e}")
    
    # Register OpenAI client if available
    try:
        if openai_ai_client and openai_ai_client.api_key:
            AIClientFactory.register_client(AIProvider.OPENAI, openai_ai_client)
            registered_count += 1
            logger.info("OpenAI AI client registered successfully")
        else:
            logger.warning("OpenAI AI client not registered - API key missing or client unavailable")
    except Exception as e:
        logger.error(f"Failed to register OpenAI client: {e}")
    
    if registered_count == 0:
        logger.error("No AI clients registered! Application may not function properly.")
        raise RuntimeError("No AI clients available")
    
    logger.info(f"AI service initialization complete. {registered_count} clients registered.")
    return registered_count


async def health_check_ai_services():
    """Perform health check on all registered AI services."""
    try:
        health_results = await unified_ai_service.health_check_all()
        
        healthy_count = sum(1 for status in health_results.values() if status)
        total_count = len(health_results)
        
        logger.info(f"AI services health check: {healthy_count}/{total_count} services healthy")
        
        for provider, is_healthy in health_results.items():
            status = "✓ Healthy" if is_healthy else "✗ Unhealthy"
            logger.info(f"  {provider}: {status}")
        
        return health_results
        
    except Exception as e:
        logger.error(f"AI services health check failed: {e}")
        return {}


def get_ai_service_info():
    """Get information about the AI service configuration."""
    try:
        info = unified_ai_service.get_provider_info()
        logger.info(f"AI Service Info: {info}")
        return info
    except Exception as e:
        logger.error(f"Failed to get AI service info: {e}")
        return {}


# Initialize services when module is imported
try:
    initialize_ai_services()
except Exception as e:
    logger.error(f"Failed to initialize AI services: {e}")
    # Don't raise here to allow the application to start even if AI services fail
