import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  TextInput,
  Modal,
  StyleSheet,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { WebView } from 'react-native-webview';
import { useNavigation, useRoute } from '@react-navigation/native';
import { useReports } from '../hooks/useReports';
import { getAllReports } from '../lib/db';


interface RouteParams {
  reportId: number;
}

const ReportWebViewScreen: React.FC = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { reportId } = route.params as RouteParams;

  console.log('ReportWebViewScreen - Route params:', route.params);
  console.log('ReportWebViewScreen - Report ID:', reportId);

  const [report, setReport] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [showFeedbackModal, setShowFeedbackModal] = useState(false);
  const [feedbackText, setFeedbackText] = useState('');
  const [isSubmittingFeedback, setIsSubmittingFeedback] = useState(false);
  const [showNativeView, setShowNativeView] = useState(false);
  
  const { submitFeedback } = useReports();

  useEffect(() => {
    loadReport();
  }, [reportId]);

  const loadReport = async () => {
    setIsLoading(true);
    try {
      console.log('Loading report with ID:', reportId);
      const reports = await getAllReports();
      console.log('All reports:', reports.length);
      const foundReport = reports.find(r => r.id === reportId);
      console.log('Found report:', foundReport ? 'Yes' : 'No');
      if (foundReport) {
        console.log('Report has HTML content:', foundReport.html_content ? 'Yes' : 'No');
        console.log('HTML content length:', foundReport.html_content?.length || 0);

        // Clean the HTML content to ensure it's properly formatted
        if (foundReport.html_content) {
          // Remove any potential markdown formatting that might interfere
          let cleanHtml = foundReport.html_content.trim();

          console.log('🔧 Original HTML starts with:', cleanHtml.substring(0, 50));

          // Remove markdown code blocks if present (more aggressive cleaning)
          cleanHtml = cleanHtml.replace(/^```html\s*/gm, '');
          cleanHtml = cleanHtml.replace(/^```\s*/gm, '');
          cleanHtml = cleanHtml.replace(/```\s*$/gm, '');
          cleanHtml = cleanHtml.trim();

          console.log('🔧 After cleaning, HTML starts with:', cleanHtml.substring(0, 50));

          // Ensure proper HTML structure
          if (!cleanHtml.includes('<!DOCTYPE html>')) {
            cleanHtml = `<!DOCTYPE html>\n${cleanHtml}`;
          }

          foundReport.html_content = cleanHtml;
          console.log('🔧 Final cleaned HTML content length:', cleanHtml.length);
          console.log('🔧 Final HTML preview:', cleanHtml.substring(0, 200));
        }
      }
      setReport(foundReport);
    } catch (error) {
      console.error('Error loading report:', error);
      Alert.alert('Error', 'Failed to load report');
    } finally {
      setIsLoading(false);
    }
  };

  const handleFeedback = async (rating: number) => {
    setIsSubmittingFeedback(true);
    
    try {
      await submitFeedback(reportId, rating, feedbackText.trim() || null);
      
      Alert.alert(
        'Thank You!',
        'Your feedback helps improve future reports.',
        [{ text: 'OK', onPress: () => setShowFeedbackModal(false) }]
      );
      
      // Reload report to show updated feedback
      await loadReport();
    } catch (error) {
      console.error('Error submitting feedback:', error);
      Alert.alert('Error', 'Failed to submit feedback');
    } finally {
      setIsSubmittingFeedback(false);
    }
  };

  const openFeedbackModal = () => {
    setFeedbackText('');
    setShowFeedbackModal(true);
  };

  if (isLoading) {
    return (
      <SafeAreaView style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#0ea5e9" />
        <Text style={styles.loadingText}>Loading report...</Text>
      </SafeAreaView>
    );
  }

  if (!report) {
    return (
      <SafeAreaView style={styles.errorContainer}>
        <Text style={styles.errorTitle}>
          Report not found
        </Text>
        <TouchableOpacity
          style={styles.goBackButton}
          onPress={() => navigation.goBack()}
        >
          <Text style={styles.goBackButtonText}>Go Back</Text>
        </TouchableOpacity>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          onPress={() => navigation.goBack()}
          style={styles.closeButton}
        >
          <Text style={styles.closeButtonText}>← Close</Text>
        </TouchableOpacity>

        <View style={styles.headerCenter}>
          <Text style={styles.headerTitle}>
            📊 Weekly Report
          </Text>
          <Text style={styles.headerDate}>
            {new Date(report.created_at).toLocaleDateString()}
          </Text>
        </View>

        <View style={styles.headerActions}>
          <TouchableOpacity
            onPress={() => {
              if (report && report.html_content) {
                setShowNativeView(!showNativeView);
              } else {
                Alert.alert('Error', 'No report content available');
              }
            }}
            style={styles.actionButton}
          >
            <Text style={styles.actionButtonText}>
              {showNativeView ? '🌐' : '📄'}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            onPress={openFeedbackModal}
            style={styles.rateButton}
          >
            <Text style={styles.rateButtonText}>💬 Rate</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Content Indicator */}
      <View style={styles.contentIndicator}>
        <Text style={styles.contentIndicatorText}>
          📄 Scroll down to read your personalized weekly insights
        </Text>
      </View>

      {/* Content View */}
      {report && report.html_content ? (
        showNativeView ? (
          // Native Text View
          <View style={styles.nativeViewContainer}>
            <View style={styles.nativeViewCard}>
              <Text style={styles.nativeViewTitle}>
                📊 Your Weekly Report
              </Text>
              <Text style={styles.nativeViewText}>
                {report.html_content
                  .replace(/<[^>]*>/g, '') // Remove HTML tags
                  .replace(/&nbsp;/g, ' ')
                  .replace(/&amp;/g, '&')
                  .replace(/&lt;/g, '<')
                  .replace(/&gt;/g, '>')
                  .replace(/\s+/g, ' ')
                  .trim()}
              </Text>
            </View>
          </View>
        ) : (
          // WebView
          <WebView
            source={{
              html: report.html_content,
              baseUrl: ''
            }}
            style={{
              flex: 1,
              backgroundColor: '#ffffff',
              opacity: 1,
              minHeight: 400
            }}
            scalesPageToFit={true}
            showsVerticalScrollIndicator={true}
            bounces={false}
            scrollEnabled={true}
            nestedScrollEnabled={true}
            onError={(syntheticEvent) => {
              const { nativeEvent } = syntheticEvent;
              console.error('WebView error: ', nativeEvent);
              Alert.alert('WebView Error', `Error loading content: ${nativeEvent.description}`);
            }}
            onLoad={() => {
              console.log('WebView loaded successfully');
            }}
            onLoadEnd={() => {
              console.log('WebView finished loading');
            }}
            onLoadStart={() => {
              console.log('WebView started loading');
            }}
            onMessage={(event) => console.log('WebView message:', event.nativeEvent.data)}
            javaScriptEnabled={true}
            domStorageEnabled={true}
            allowsInlineMediaPlayback={true}
            mediaPlaybackRequiresUserAction={false}
            startInLoadingState={true}
            mixedContentMode="compatibility"
            cacheEnabled={false}
            incognito={true}
            renderLoading={() => (
              <View style={styles.webViewLoading}>
                <ActivityIndicator size="large" color="#6366F1" />
                <Text style={styles.webViewLoadingText}>Loading your weekly insights...</Text>
                <Text style={styles.webViewLoadingSubtext}>This may take a moment</Text>
              </View>
            )}
            onHttpError={(syntheticEvent) => {
              console.error('WebView HTTP error:', syntheticEvent.nativeEvent);
            }}
            onRenderProcessGone={(syntheticEvent) => {
              console.error('WebView render process gone:', syntheticEvent.nativeEvent);
            }}
          />
        )
      ) : (
        <View style={styles.noContentContainer}>
          <View style={styles.noContentCard}>
            <Text style={styles.noContentIcon}>📊</Text>
            <Text style={styles.noContentTitle}>
              No Report Content
            </Text>
            <Text style={styles.noContentDescription}>
              This report doesn't have any content yet. This might happen if:
            </Text>
            <View style={styles.noContentReasons}>
              <Text style={styles.noContentReason}>• The report generation failed</Text>
              <Text style={styles.noContentReason}>• No journal entries for this week</Text>
              <Text style={styles.noContentReason}>• AI service is unavailable</Text>
            </View>
            <TouchableOpacity
              onPress={() => navigation.goBack()}
              style={styles.noContentButton}
            >
              <Text style={styles.noContentButtonText}>← Go Back</Text>
            </TouchableOpacity>
          </View>
        </View>
      )}

      {/* Feedback Modal */}
      <Modal
        visible={showFeedbackModal}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setShowFeedbackModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContainer}>
            {/* Modal Header */}
            <View style={styles.modalHeader}>
              <View>
                <Text style={styles.modalTitle}>
                  💭 Rate This Report
                </Text>
                <Text style={styles.modalSubtitle}>
                  Help us improve your weekly insights
                </Text>
              </View>
              <TouchableOpacity
                onPress={() => setShowFeedbackModal(false)}
                style={styles.modalCloseButton}
              >
                <Text style={styles.modalCloseText}>✕</Text>
              </TouchableOpacity>
            </View>

            <Text style={styles.ratingQuestion}>
              How helpful was this weekly summary?
            </Text>

            {/* Rating Buttons */}
            <View style={styles.ratingButtons}>
              <TouchableOpacity
                style={styles.helpfulButton}
                onPress={() => handleFeedback(1)}
                disabled={isSubmittingFeedback}
              >
                <Text style={styles.ratingEmoji}>👍</Text>
                <Text style={styles.helpfulButtonTitle}>Helpful</Text>
                <Text style={styles.helpfulButtonSubtitle}>This was useful!</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.notHelpfulButton}
                onPress={() => handleFeedback(-1)}
                disabled={isSubmittingFeedback}
              >
                <Text style={styles.ratingEmoji}>👎</Text>
                <Text style={styles.notHelpfulButtonTitle}>Not Helpful</Text>
                <Text style={styles.notHelpfulButtonSubtitle}>Needs improvement</Text>
              </TouchableOpacity>
            </View>

            {/* Optional Text Feedback */}
            <View style={styles.feedbackSection}>
              <Text style={styles.feedbackSectionTitle}>
                💬 Additional Comments (Optional)
              </Text>
              <TextInput
                style={styles.feedbackInput}
                placeholder="What would make future reports better? Any specific insights you'd like to see?"
                value={feedbackText}
                onChangeText={setFeedbackText}
                multiline
                textAlignVertical="top"
                placeholderTextColor="#9CA3AF"
              />
            </View>

            {isSubmittingFeedback && (
              <View style={styles.submittingContainer}>
                <ActivityIndicator size="large" color="#6366F1" />
                <Text style={styles.submittingTitle}>Submitting feedback...</Text>
                <Text style={styles.submittingSubtitle}>Thank you for helping us improve!</Text>
              </View>
            )}

            {/* Skip Feedback Button */}
            {!isSubmittingFeedback && (
              <TouchableOpacity
                onPress={() => setShowFeedbackModal(false)}
                style={styles.skipButton}
              >
                <Text style={styles.skipButtonText}>
                  Skip for now
                </Text>
              </TouchableOpacity>
            )}
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    backgroundColor: 'white',
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    color: '#6b7280',
    marginTop: 16,
  },
  errorContainer: {
    flex: 1,
    backgroundColor: 'white',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 16,
  },
  errorTitle: {
    color: '#374151',
    fontSize: 18,
    textAlign: 'center',
    marginBottom: 16,
  },
  goBackButton: {
    backgroundColor: '#3b82f6',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  goBackButtonText: {
    color: 'white',
    fontWeight: '600',
  },
  container: {
    flex: 1,
    backgroundColor: 'white',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#4f46e5',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  closeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 20,
    paddingHorizontal: 12,
    paddingVertical: 8,
  },
  closeButtonText: {
    color: 'white',
    fontWeight: '600',
  },
  headerCenter: {
    flex: 1,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: 'white',
  },
  headerDate: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 14,
  },
  headerActions: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 20,
    paddingHorizontal: 8,
    paddingVertical: 8,
  },
  actionButtonText: {
    color: 'white',
    fontWeight: '600',
  },
  rateButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 20,
    paddingHorizontal: 12,
    paddingVertical: 8,
  },
  rateButtonText: {
    color: 'white',
    fontWeight: '600',
  },
  contentIndicator: {
    backgroundColor: '#eff6ff',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#dbeafe',
  },
  contentIndicatorText: {
    color: '#1d4ed8',
    textAlign: 'center',
    fontSize: 14,
  },
  nativeViewContainer: {
    flex: 1,
    backgroundColor: '#f9fafb',
    paddingHorizontal: 16,
    paddingVertical: 16,
  },
  nativeViewCard: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  nativeViewTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#2563eb',
    marginBottom: 16,
  },
  nativeViewText: {
    color: '#374151',
    lineHeight: 24,
    fontSize: 16,
  },
  webViewLoading: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f9fafb',
  },
  webViewLoadingText: {
    color: '#6b7280',
    marginTop: 8,
  },
  webViewLoadingSubtext: {
    color: '#9ca3af',
    marginTop: 4,
    fontSize: 14,
  },
  noContentContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  noContentCard: {
    backgroundColor: '#fed7aa',
    borderRadius: 16,
    padding: 24,
    alignItems: 'center',
  },
  noContentIcon: {
    fontSize: 64,
    marginBottom: 16,
  },
  noContentTitle: {
    color: '#9a3412',
    fontWeight: 'bold',
    fontSize: 20,
    marginBottom: 8,
  },
  noContentDescription: {
    color: '#c2410c',
    textAlign: 'center',
    marginBottom: 16,
  },
  noContentReasons: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    width: '100%',
  },
  noContentReason: {
    color: '#374151',
    marginBottom: 8,
  },
  noContentButton: {
    backgroundColor: '#ea580c',
    borderRadius: 12,
    paddingVertical: 12,
    paddingHorizontal: 24,
    marginTop: 16,
  },
  noContentButtonText: {
    color: 'white',
    fontWeight: 'bold',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContainer: {
    backgroundColor: 'white',
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    padding: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -4 },
    shadowOpacity: 0.2,
    shadowRadius: 16,
    elevation: 8,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 24,
  },
  modalTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#374151',
  },
  modalSubtitle: {
    color: '#6b7280',
    marginTop: 4,
  },
  modalCloseButton: {
    backgroundColor: '#f3f4f6',
    borderRadius: 20,
    padding: 8,
  },
  modalCloseText: {
    color: '#6b7280',
    fontSize: 20,
  },
  ratingQuestion: {
    color: '#374151',
    fontSize: 18,
    marginBottom: 24,
    textAlign: 'center',
  },
  ratingButtons: {
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 24,
    marginBottom: 32,
  },
  helpfulButton: {
    backgroundColor: '#dcfce7',
    borderRadius: 16,
    padding: 24,
    alignItems: 'center',
    flex: 1,
    marginHorizontal: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  notHelpfulButton: {
    backgroundColor: '#fee2e2',
    borderRadius: 16,
    padding: 24,
    alignItems: 'center',
    flex: 1,
    marginHorizontal: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  ratingEmoji: {
    fontSize: 48,
    marginBottom: 12,
  },
  helpfulButtonTitle: {
    color: '#166534',
    fontWeight: 'bold',
    fontSize: 18,
  },
  helpfulButtonSubtitle: {
    color: '#16a34a',
    fontSize: 14,
    marginTop: 4,
  },
  notHelpfulButtonTitle: {
    color: '#991b1b',
    fontWeight: 'bold',
    fontSize: 18,
  },
  notHelpfulButtonSubtitle: {
    color: '#dc2626',
    fontSize: 14,
    marginTop: 4,
  },
  feedbackSection: {
    backgroundColor: '#f9fafb',
    borderRadius: 16,
    padding: 16,
    marginBottom: 24,
  },
  feedbackSectionTitle: {
    color: '#374151',
    fontWeight: 'bold',
    marginBottom: 12,
    fontSize: 18,
  },
  feedbackInput: {
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: '#e5e7eb',
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    minHeight: 100,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  submittingContainer: {
    alignItems: 'center',
    paddingVertical: 24,
    backgroundColor: '#eff6ff',
    borderRadius: 16,
  },
  submittingTitle: {
    color: '#1d4ed8',
    marginTop: 12,
    fontWeight: '500',
    fontSize: 18,
  },
  submittingSubtitle: {
    color: '#2563eb',
    marginTop: 4,
  },
  skipButton: {
    backgroundColor: '#e5e7eb',
    borderRadius: 12,
    paddingVertical: 12,
    paddingHorizontal: 24,
    marginTop: 16,
  },
  skipButtonText: {
    color: '#374151',
    fontWeight: '500',
    textAlign: 'center',
  },
});

export default ReportWebViewScreen;
