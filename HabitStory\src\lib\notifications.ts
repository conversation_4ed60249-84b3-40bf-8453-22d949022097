import * as Notifications from 'expo-notifications';
import * as Device from 'expo-device';
import { Platform } from 'react-native';

// Configure notification behavior
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: false,
  }),
});

// Notification types
export enum NotificationType {
  DAILY_REMINDER = 'daily_reminder',
  WEEKLY_SUMMARY = 'weekly_summary',
}

// Request notification permissions
export const requestNotificationPermissions = async (): Promise<boolean> => {
  try {
    if (!Device.isDevice) {
      console.log('Notifications only work on physical devices');
      return false;
    }

    const { status: existingStatus } = await Notifications.getPermissionsAsync();
    let finalStatus = existingStatus;

    if (existingStatus !== 'granted') {
      const { status } = await Notifications.requestPermissionsAsync();
      finalStatus = status;
    }

    if (finalStatus !== 'granted') {
      console.log('Notification permissions not granted');
      return false;
    }

    // Configure notification channel for Android
    if (Platform.OS === 'android') {
      await Notifications.setNotificationChannelAsync('default', {
        name: 'HabitStory Reminders',
        importance: Notifications.AndroidImportance.DEFAULT,
        vibrationPattern: [0, 250, 250, 250],
        lightColor: '#0ea5e9',
      });
    }

    return true;
  } catch (error) {
    console.error('Error requesting notification permissions:', error);
    return false;
  }
};

// Schedule daily reminder notification
export const scheduleDailyReminder = async (hour: number = 21, minute: number = 0): Promise<string | null> => {
  try {
    const hasPermission = await requestNotificationPermissions();
    if (!hasPermission) {
      throw new Error('Notification permissions not granted');
    }

    // Cancel existing daily reminders
    await cancelNotificationsByType(NotificationType.DAILY_REMINDER);

    // Schedule new daily reminder
    const notificationId = await Notifications.scheduleNotificationAsync({
      content: {
        title: '📖 Time to reflect on your day',
        body: 'How was your day? Take a moment to journal your thoughts and experiences.',
        data: { type: NotificationType.DAILY_REMINDER },
        sound: true,
      },
      trigger: {
        hour,
        minute,
        repeats: true,
      },
    });

    console.log('Daily reminder scheduled:', notificationId);
    return notificationId;
  } catch (error) {
    console.error('Error scheduling daily reminder:', error);
    return null;
  }
};

// Schedule weekly report generation and notification (every Sunday)
export const scheduleWeeklyReport = async (hour: number = 10, minute: number = 0): Promise<string | null> => {
  return await scheduleWeeklySummary(0, hour, minute); // Sunday = 0
};

// Schedule weekly summary notification
export const scheduleWeeklySummary = async (dayOfWeek: number = 0, hour: number = 10, minute: number = 0): Promise<string | null> => {
  try {
    const hasPermission = await requestNotificationPermissions();
    if (!hasPermission) {
      throw new Error('Notification permissions not granted');
    }

    // Cancel existing weekly reminders
    await cancelNotificationsByType(NotificationType.WEEKLY_SUMMARY);

    // Schedule new weekly reminder
    const notificationId = await Notifications.scheduleNotificationAsync({
      content: {
        title: '📊 Your weekly summary is ready!',
        body: 'Discover insights from your week and see how you\'ve grown.',
        data: { type: NotificationType.WEEKLY_SUMMARY },
        sound: true,
      },
      trigger: {
        weekday: dayOfWeek + 1, // Expo uses 1-7 (Sunday = 1)
        hour,
        minute,
        repeats: true,
      },
    });

    console.log('Weekly summary scheduled:', notificationId);
    return notificationId;
  } catch (error) {
    console.error('Error scheduling weekly summary:', error);
    return null;
  }
};

// Cancel notifications by type
export const cancelNotificationsByType = async (type: NotificationType): Promise<void> => {
  try {
    const scheduledNotifications = await Notifications.getAllScheduledNotificationsAsync();
    
    const notificationsToCancel = scheduledNotifications
      .filter(notification => notification.content.data?.type === type)
      .map(notification => notification.identifier);

    if (notificationsToCancel.length > 0) {
      await Notifications.cancelScheduledNotificationsAsync(notificationsToCancel);
      console.log(`Cancelled ${notificationsToCancel.length} notifications of type ${type}`);
    }
  } catch (error) {
    console.error('Error cancelling notifications:', error);
  }
};

// Cancel all notifications
export const cancelAllNotifications = async (): Promise<void> => {
  try {
    await Notifications.cancelAllScheduledNotificationsAsync();
    console.log('All notifications cancelled');
  } catch (error) {
    console.error('Error cancelling all notifications:', error);
  }
};

// Get all scheduled notifications
export const getScheduledNotifications = async (): Promise<Notifications.NotificationRequest[]> => {
  try {
    return await Notifications.getAllScheduledNotificationsAsync();
  } catch (error) {
    console.error('Error getting scheduled notifications:', error);
    return [];
  }
};

// Send immediate notification (for testing)
export const sendTestNotification = async (title: string, body: string): Promise<string | null> => {
  try {
    const hasPermission = await requestNotificationPermissions();
    if (!hasPermission) {
      throw new Error('Notification permissions not granted');
    }

    const notificationId = await Notifications.scheduleNotificationAsync({
      content: {
        title,
        body,
        data: { type: 'test' },
        sound: true,
      },
      trigger: null, // Send immediately
    });

    return notificationId;
  } catch (error) {
    console.error('Error sending test notification:', error);
    return null;
  }
};

// Initialize notifications (call this when app starts)
export const initializeNotifications = async (): Promise<void> => {
  try {
    const hasPermission = await requestNotificationPermissions();
    
    if (hasPermission) {
      // Schedule default notifications if none exist
      const scheduled = await getScheduledNotifications();
      
      const hasDailyReminder = scheduled.some(
        n => n.content.data?.type === NotificationType.DAILY_REMINDER
      );
      const hasWeeklyReminder = scheduled.some(
        n => n.content.data?.type === NotificationType.WEEKLY_SUMMARY
      );

      if (!hasDailyReminder) {
        await scheduleDailyReminder(21, 0); // 9 PM daily
      }

      if (!hasWeeklyReminder) {
        await scheduleWeeklyReport(10, 0); // Sunday 10 AM
      }

      console.log('Notifications initialized successfully');
    }
  } catch (error) {
    console.error('Error initializing notifications:', error);
  }
};

// Auto-generate weekly report (called by background task or notification handler)
export const autoGenerateWeeklyReport = async (): Promise<boolean> => {
  try {
    console.log('Auto-generating weekly report...');

    // This would be called by a background task or when the notification is triggered
    // The actual report generation would happen in the app when user opens it
    // For now, we just log that it should happen

    console.log('Weekly report generation triggered - user should open app to generate');
    return true;
  } catch (error) {
    console.error('Error in auto-generate weekly report:', error);
    return false;
  }
};

// Handle notification response (when user taps notification)
export const handleNotificationResponse = (response: Notifications.NotificationResponse): void => {
  const notificationType = response.notification.request.content.data?.type;

  console.log('Notification tapped:', notificationType);

  // You can add navigation logic here based on notification type
  switch (notificationType) {
    case NotificationType.DAILY_REMINDER:
      // Navigate to journal screen
      console.log('Should navigate to journal screen');
      break;
    case NotificationType.WEEKLY_SUMMARY:
      // Navigate to reports screen and potentially auto-generate report
      console.log('Should navigate to reports screen');
      autoGenerateWeeklyReport();
      break;
    default:
      break;
  }
};

// Set up notification listeners
export const setupNotificationListeners = (): (() => void) => {
  // Listen for notifications received while app is in foreground
  const notificationListener = Notifications.addNotificationReceivedListener(notification => {
    console.log('Notification received:', notification);
  });

  // Listen for notification responses (when user taps notification)
  const responseListener = Notifications.addNotificationResponseReceivedListener(handleNotificationResponse);

  // Return cleanup function
  return () => {
    Notifications.removeNotificationSubscription(notificationListener);
    Notifications.removeNotificationSubscription(responseListener);
  };
};
