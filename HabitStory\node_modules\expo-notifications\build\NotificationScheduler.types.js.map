{"version": 3, "file": "NotificationScheduler.types.js", "sourceRoot": "", "sources": ["../src/NotificationScheduler.types.ts"], "names": [], "mappings": "", "sourcesContent": ["import { ProxyNativeModule } from 'expo-modules-core';\n\nimport { NotificationRequest, NotificationContentInput } from './Notifications.types';\n\nexport interface NotificationSchedulerModule extends ProxyNativeModule {\n  getAllScheduledNotificationsAsync?: () => Promise<NotificationRequest[]>;\n  scheduleNotificationAsync?: (\n    identifier: string,\n    notificationContent: NotificationContentInput,\n    trigger: NativeNotificationTriggerInput\n  ) => Promise<string>;\n  cancelScheduledNotificationAsync?: (identifier: string) => Promise<void>;\n  cancelAllScheduledNotificationsAsync?: () => Promise<void>;\n  getNextTriggerDateAsync?: (trigger: NativeNotificationTriggerInput) => Promise<number>;\n}\n\nexport interface NativeChannelAwareTriggerInput {\n  type: 'channel';\n  channelId?: string;\n}\n\n// ISO8601 calendar pattern-matching\nexport interface NativeCalendarTriggerInput {\n  type: 'calendar';\n  channelId?: string;\n  repeats?: boolean;\n  timezone?: string;\n  year?: number;\n  month?: number;\n  weekday?: number;\n  weekOfMonth?: number;\n  weekOfYear?: number;\n  weekdayOrdinal?: number;\n  day?: number;\n  hour?: number;\n  minute?: number;\n  second?: number;\n}\n\nexport interface NativeTimeIntervalTriggerInput {\n  type: 'timeInterval';\n  channelId?: string;\n  repeats: boolean;\n  seconds: number;\n}\n\nexport interface NativeDailyTriggerInput {\n  type: 'daily';\n  channelId?: string;\n  hour: number;\n  minute: number;\n}\n\nexport interface NativeWeeklyTriggerInput {\n  type: 'weekly';\n  channelId?: string;\n  weekday: number;\n  hour: number;\n  minute: number;\n}\n\nexport interface NativeYearlyTriggerInput {\n  type: 'yearly';\n  channelId?: string;\n  day: number;\n  month: number;\n  hour: number;\n  minute: number;\n}\n\nexport interface NativeMonthlyTriggerInput {\n  type: 'monthly';\n  channelId?: string;\n  day: number;\n  hour: number;\n  minute: number;\n}\n\nexport interface NativeDateTriggerInput {\n  type: 'date';\n  channelId?: string;\n  timestamp: number; // seconds since 1970\n}\n\nexport type NativeNotificationTriggerInput =\n  | null\n  | NativeChannelAwareTriggerInput\n  | NativeDateTriggerInput\n  | NativeCalendarTriggerInput\n  | NativeTimeIntervalTriggerInput\n  | NativeDailyTriggerInput\n  | NativeWeeklyTriggerInput\n  | NativeMonthlyTriggerInput\n  | NativeYearlyTriggerInput;\n"]}