import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Alert,
  ActivityIndicator,
  StyleSheet,
  Animated,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';

import { useEntries } from '../hooks/useEntries';
import { useReports } from '../hooks/useReports';
import { useGemini } from '../hooks/useGemini';
import { getWeekDateRange } from '../lib/db';

const ReportScreen: React.FC = () => {
  const navigation = useNavigation();
  const [isGenerating, setIsGenerating] = useState(false);
  const [weekEntries, setWeekEntries] = useState<any[]>([]);
  const [fadeAnim] = useState(new Animated.Value(0));
  
  const { getEntriesForWeek } = useEntries();
  const { reports, createReport, refreshReports } = useReports();
  const { generateSummary, error: geminiError } = useGemini();

  useEffect(() => {
    loadWeekEntries();
    refreshReports();
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 500,
      useNativeDriver: true,
    }).start();
  }, []);

  // Debug: Log reports when they change
  useEffect(() => {
    console.log('Reports updated:', reports.length);
    reports.forEach((report, index) => {
      console.log(`Report ${index + 1}:`, {
        id: report.id,
        hasContent: !!report.html_content,
        contentLength: report.html_content?.length || 0,
        created: report.created_at
      });
    });
  }, [reports]);

  const loadWeekEntries = async () => {
    try {
      const entries = await getEntriesForWeek(new Date());
      setWeekEntries(entries);
    } catch (error) {
      console.error('Error loading week entries:', error);
    }
  };

  const handleGenerateReport = async () => {
    if (weekEntries.length === 0) {
      Alert.alert(
        'No Entries',
        'You need at least one journal entry this week to generate a report.',
        [{ text: 'OK' }]
      );
      return;
    }

    Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    setIsGenerating(true);

    try {
      const htmlContent = await generateSummary(weekEntries);
      const { start, end } = getWeekDateRange(new Date());
      const reportId = await createReport(start, end, htmlContent);
      
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
      Alert.alert(
        'Report Generated!',
        'Your personalized weekly summary is ready.',
        [
          {
            text: 'View Report',
            onPress: () => {
              navigation.navigate('ReportWebView' as never, { reportId } as never);
            },
          },
        ]
      );
    } catch (error) {
      console.error('Error generating report:', error);
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
      Alert.alert(
        'Generation Failed',
        error instanceof Error ? error.message : 'Failed to generate report. Please try again.',
        [{ text: 'OK' }]
      );
    } finally {
      setIsGenerating(false);
    }
  };

  const renderReportCard = (report: any) => {
    const startDate = new Date(report.week_start);
    const endDate = new Date(report.week_end);

    return (
      <TouchableOpacity
        key={report.id}
        style={styles.reportCard}
        onPress={() => {
          Haptics.selectionAsync();
          navigation.navigate('ReportWebView' as never, { reportId: report.id } as never);
        }}
        activeOpacity={0.7}
      >
        <View style={styles.cardHeader}>
          <View style={styles.cardTitleContainer}>
            <View style={styles.iconContainer}>
              <Ionicons name="document-text" size={20} color="#2563eb" />
            </View>
            <View>
              <Text style={styles.cardTitle}>
                Week of {startDate.toLocaleDateString('en-US', { month: 'long', day: 'numeric' })}
              </Text>
              <Text style={styles.cardSubtitle}>
                {startDate.toLocaleDateString()} - {endDate.toLocaleDateString()}
              </Text>
            </View>
          </View>
          <View style={styles.dateTag}>
            <Text style={styles.dateTagText}>
              {new Date(report.created_at).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}
            </Text>
          </View>
        </View>

        {report.feedback_rating && (
          <View style={styles.feedbackContainer}>
            <Ionicons
              name={report.feedback_rating > 0 ? 'thumbs-up' : 'thumbs-down'}
              size={16}
              color={report.feedback_rating > 0 ? '#059669' : '#DC2626'}
            />
            <Text style={styles.feedbackText}>
              Feedback: {report.feedback_rating > 0 ? 'Helpful' : 'Not Helpful'}
            </Text>
          </View>
        )}

        <View style={styles.cardFooter}>
          <Text style={styles.tapText}>Tap to view full report</Text>
          <Ionicons name="chevron-forward" size={16} color="#6b7280" />
        </View>
      </TouchableOpacity>
    );
  };

  const currentWeekRange = getWeekDateRange(new Date());

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        <View style={styles.header}>
          <Text style={styles.headerTitle}>Weekly Reports</Text>
          <Text style={styles.headerSubtitle}>
            AI-powered insights from your journal
          </Text>
        </View>

        <View style={styles.content}>
          {/* Current Week Section */}
          <View style={styles.currentWeekCard}>
            <View style={styles.sectionHeader}>
              <View style={styles.sectionIconContainer}>
                <Ionicons name="today" size={24} color="#2563eb" />
              </View>
              <Text style={styles.sectionTitle}>This Week</Text>
            </View>

            <View style={styles.infoRow}>
              <Ionicons name="calendar-outline" size={16} color="#6B7280" />
              <Text style={styles.infoText}>
                {new Date(currentWeekRange.start).toLocaleDateString()} - {new Date(currentWeekRange.end).toLocaleDateString()}
              </Text>
            </View>

            <View style={styles.statusRow}>
              <View style={styles.infoRow}>
                <Ionicons name="book-outline" size={16} color="#6B7280" />
                <Text style={styles.infoText}>
                  {weekEntries.length} {weekEntries.length === 1 ? 'entry' : 'entries'} this week
                </Text>
              </View>
              {weekEntries.length > 0 && (
                <View style={styles.readyTag}>
                  <Text style={styles.readyTagText}>Ready to Generate</Text>
                </View>
              )}
            </View>

            {geminiError && (
              <View style={styles.errorContainer}>
                <View style={styles.errorHeader}>
                  <Ionicons name="alert-circle" size={20} color="#DC2626" />
                  <Text style={styles.errorTitle}>Error</Text>
                </View>
                <Text style={styles.errorText}>{geminiError}</Text>
              </View>
            )}

            <TouchableOpacity
              style={[
                styles.generateButton,
                (isGenerating || weekEntries.length === 0) && styles.generateButtonDisabled
              ]}
              onPress={handleGenerateReport}
              disabled={isGenerating || weekEntries.length === 0}
            >
              {isGenerating && <ActivityIndicator color="white" size="small" style={styles.buttonLoader} />}
              <Ionicons name="sparkles" size={20} color="white" style={styles.buttonIcon} />
              <Text style={styles.generateButtonText}>
                {isGenerating ? 'Generating Report...' : 'Generate Weekly Report'}
              </Text>
            </TouchableOpacity>
          </View>

          {/* Previous Reports */}
          <View style={styles.reportsSection}>
            <View style={styles.sectionHeader}>
              <View style={[styles.sectionIconContainer, { backgroundColor: '#f3e8ff' }]}>
                <Ionicons name="time" size={24} color="#7c3aed" />
              </View>
              <Text style={styles.sectionTitle}>Previous Reports</Text>
            </View>

            {reports.length === 0 ? (
              <View style={styles.emptyReportsCard}>
                <View style={styles.emptyIconContainer}>
                  <Ionicons name="documents-outline" size={48} color="#6B7280" />
                </View>
                <Text style={styles.emptyTitle}>No reports yet</Text>
                <Text style={styles.emptyText}>
                  Create your first weekly summary above!
                </Text>
              </View>
            ) : (
              reports.map((report) => renderReportCard(report))
            )}
          </View>

          <View style={styles.bottomPadding} />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollView: {
    flex: 1,
  },
  header: {
    backgroundColor: '#4F46E5',
    padding: 24,
    paddingTop: 40,
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 8,
  },
  headerSubtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.9)',
  },
  content: {
    padding: 16,
  },
  currentWeekCard: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 20,
    marginBottom: 24,
    borderWidth: 1,
    borderColor: '#e5e7eb',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionIconContainer: {
    backgroundColor: '#dbeafe',
    borderRadius: 20,
    padding: 12,
    marginRight: 12,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#374151',
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  infoText: {
    fontSize: 14,
    color: '#6b7280',
    marginLeft: 8,
  },
  statusRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  readyTag: {
    backgroundColor: '#dcfce7',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: '#bbf7d0',
  },
  readyTagText: {
    color: '#166534',
    fontSize: 12,
    fontWeight: '600',
  },
  errorContainer: {
    backgroundColor: '#fef2f2',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#fecaca',
  },
  errorHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  errorTitle: {
    color: '#dc2626',
    fontWeight: '600',
    marginLeft: 8,
  },
  errorText: {
    color: '#dc2626',
    fontSize: 14,
  },
  generateButton: {
    backgroundColor: '#4F46E5',
    borderRadius: 12,
    paddingVertical: 16,
    paddingHorizontal: 24,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  generateButtonDisabled: {
    backgroundColor: '#d1d5db',
  },
  buttonLoader: {
    marginRight: 8,
  },
  buttonIcon: {
    marginRight: 8,
  },
  generateButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  reportsSection: {
    marginBottom: 24,
  },
  reportCard: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#e5e7eb',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  cardTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  iconContainer: {
    backgroundColor: '#dbeafe',
    borderRadius: 16,
    padding: 8,
    marginRight: 12,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 4,
  },
  cardSubtitle: {
    fontSize: 14,
    color: '#6b7280',
  },
  dateTag: {
    backgroundColor: '#f3e8ff',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 16,
  },
  dateTagText: {
    color: '#7c3aed',
    fontSize: 12,
    fontWeight: '500',
  },
  feedbackContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 12,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#e5e7eb',
  },
  feedbackText: {
    fontSize: 14,
    color: '#6b7280',
    fontWeight: '500',
    marginLeft: 8,
  },
  cardFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 16,
  },
  tapText: {
    fontSize: 14,
    color: '#6b7280',
  },
  emptyReportsCard: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 32,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#e5e7eb',
  },
  emptyIconContainer: {
    backgroundColor: '#f3f4f6',
    borderRadius: 32,
    padding: 16,
    marginBottom: 16,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#6b7280',
    marginBottom: 8,
  },
  emptyText: {
    fontSize: 14,
    color: '#9ca3af',
    textAlign: 'center',
  },
  bottomPadding: {
    height: 80,
  },
});

export default ReportScreen;
