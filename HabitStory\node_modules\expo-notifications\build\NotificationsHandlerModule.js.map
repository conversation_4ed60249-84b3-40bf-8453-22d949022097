{"version": 3, "file": "NotificationsHandlerModule.js", "sourceRoot": "", "sources": ["../src/NotificationsHandlerModule.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,QAAQ,EAAE,MAAM,mBAAmB,CAAC;AAI7C,IAAI,mBAAmB,GAAG,KAAK,CAAC;AAEhC,eAAe;IACb,WAAW,EAAE,GAAG,EAAE;QAChB,IAAI,CAAC,mBAAmB,EAAE,CAAC;YACzB,OAAO,CAAC,IAAI,CACV,6EAA6E,QAAQ,CAAC,EAAE,+CAA+C,CACxI,CAAC;YACF,mBAAmB,GAAG,IAAI,CAAC;QAC7B,CAAC;IACH,CAAC;IACD,eAAe,EAAE,GAAG,EAAE,GAAE,CAAC;CACI,CAAC", "sourcesContent": ["import { Platform } from 'expo-modules-core';\n\nimport { NotificationsHandlerModule } from './NotificationsHandlerModule.types';\n\nlet warningHasBeenShown = false;\n\nexport default {\n  addListener: () => {\n    if (!warningHasBeenShown) {\n      console.warn(\n        `[expo-notifications] Notifications handling is not yet fully supported on ${Platform.OS}. Handling notifications will have no effect.`\n      );\n      warningHasBeenShown = true;\n    }\n  },\n  removeListeners: () => {},\n} as NotificationsHandlerModule;\n"]}