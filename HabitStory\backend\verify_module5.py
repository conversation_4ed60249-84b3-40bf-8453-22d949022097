#!/usr/bin/env python3
"""
Simple verification script for Module 5: Recommendations Service
"""

import sys
import os

# Add the app directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '.'))

def verify_module5():
    """Verify Module 5: Recommendations Service implementation."""
    print("🔍 Verifying Module 5: Recommendations Service")
    print("=" * 50)
    
    try:
        # Test 1: Import the service
        print("1. Testing imports...")
        from app.services.recommendations import recommendations_service, RecommendationsService
        print("   ✅ Successfully imported recommendations service")
        
        # Test 2: Check service instance
        print("2. Testing service instance...")
        assert recommendations_service is not None
        assert isinstance(recommendations_service, RecommendationsService)
        print("   ✅ Service instance is properly configured")
        
        # Test 3: Check service methods
        print("3. Testing service methods...")
        methods = [
            'generate_weekly_recommendations',
            'generate_habit_recommendations', 
            'generate_correlation_recommendations',
            '_create_recommendations_system_prompt',
            '_create_recommendations_user_prompt',
            '_validate_recommendations',
            '_get_fallback_recommendations'
        ]
        
        for method in methods:
            assert hasattr(recommendations_service, method), f"Missing method: {method}"
        print(f"   ✅ All {len(methods)} required methods are present")
        
        # Test 4: Test fallback recommendations
        print("4. Testing fallback recommendations...")
        user_traits = {
            "tone": "informal",
            "style": "conversational", 
            "traits": {"optimistic": True}
        }
        
        fallback_recs = recommendations_service._get_fallback_recommendations(user_traits)
        assert isinstance(fallback_recs, list)
        assert len(fallback_recs) >= 2
        
        # Verify recommendation structure
        for rec in fallback_recs:
            required_fields = ["category", "title", "description", "priority", "actionable_steps", "reasoning"]
            for field in required_fields:
                assert field in rec, f"Missing field {field} in recommendation"
            assert isinstance(rec["actionable_steps"], list)
            assert len(rec["actionable_steps"]) > 0
        
        print(f"   ✅ Generated {len(fallback_recs)} valid fallback recommendations")
        
        # Test 5: Test validation function
        print("5. Testing recommendation validation...")
        test_recs = [
            {
                "category": "wellness",
                "title": "Test Recommendation",
                "description": "Test description",
                "priority": "high",
                "actionable_steps": ["step 1", "step 2"],
                "reasoning": "Test reasoning"
            },
            {
                "title": "Incomplete rec"  # Missing required fields
            },
            "invalid"  # Wrong type
        ]
        
        validated = recommendations_service._validate_recommendations(test_recs)
        assert len(validated) == 1  # Only the valid one should remain
        assert validated[0]["title"] == "Test Recommendation"
        print("   ✅ Recommendation validation working correctly")
        
        # Test 6: Test prompt generation
        print("6. Testing prompt generation...")
        system_prompt = recommendations_service._create_recommendations_system_prompt(
            user_traits, "Previous feedback"
        )
        assert isinstance(system_prompt, str)
        assert len(system_prompt) > 100
        assert "life coach" in system_prompt.lower()
        assert user_traits["tone"] in system_prompt
        print("   ✅ System prompt generation working")
        
        user_prompt = recommendations_service._create_recommendations_user_prompt(
            [], {"quantitative_metrics": {}, "habit_metrics": {}}, {"insights": []}
        )
        assert isinstance(user_prompt, str)
        assert "USER DATA ANALYSIS" in user_prompt
        print("   ✅ User prompt generation working")
        
        print()
        print("🎉 Module 5: Recommendations Service - ALL TESTS PASSED!")
        print()
        print("✅ Verified Features:")
        print("   • Service instantiation and configuration")
        print("   • All required methods present")
        print("   • Fallback recommendations generation")
        print("   • Recommendation validation and structure")
        print("   • Prompt generation for AI interactions")
        print("   • Error handling and data validation")
        print()
        print("🚀 Module 5 is COMPLETE and ready for production!")
        
        return True
        
    except Exception as e:
        print(f"❌ Verification failed: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = verify_module5()
    sys.exit(0 if success else 1)
