{"version": 3, "file": "warnOfExpoGoPushUsage.js", "sourceRoot": "", "sources": ["../src/warnOfExpoGoPushUsage.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,iBAAiB,EAAE,MAAM,MAAM,CAAC;AACzC,OAAO,EAAE,QAAQ,EAAE,MAAM,mBAAmB,CAAC;AAE7C,IAAI,OAAO,GAAG,KAAK,CAAC;AAEpB,MAAM,CAAC,MAAM,qBAAqB,GAAG,GAAG,EAAE;IACxC,IAAI,OAAO,IAAI,iBAAiB,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC;QAC/C,OAAO,GAAG,IAAI,CAAC;QACf,MAAM,OAAO,GAAG,kSAAkS,CAAC;QAEnT,IAAI,QAAQ,CAAC,EAAE,KAAK,SAAS,EAAE,CAAC;YAC9B,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QACzB,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACxB,CAAC;IACH,CAAC;AACH,CAAC,CAAC", "sourcesContent": ["import { isRunningInExpoGo } from 'expo';\nimport { Platform } from 'expo-modules-core';\n\nlet didWarn = false;\n\nexport const warnOfExpoGoPushUsage = () => {\n  if (__DEV__ && isRunningInExpoGo() && !didWarn) {\n    didWarn = true;\n    const message = `expo-notifications: Android Push notifications (remote notifications) functionality provided by expo-notifications was removed from Expo Go with the release of SDK 53. Use a development build instead of Expo Go. Read more at https://docs.expo.dev/develop/development-builds/introduction/.`;\n\n    if (Platform.OS === 'android') {\n      console.error(message);\n    } else {\n      console.warn(message);\n    }\n  }\n};\n"]}