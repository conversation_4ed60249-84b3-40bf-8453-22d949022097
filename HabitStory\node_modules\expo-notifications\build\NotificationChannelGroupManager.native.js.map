{"version": 3, "file": "NotificationChannelGroupManager.native.js", "sourceRoot": "", "sources": ["../src/NotificationChannelGroupManager.native.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,mBAAmB,EAAE,MAAM,mBAAmB,CAAC;AAIxD,eAAe,mBAAmB,CAChC,qCAAqC,CACtC,CAAC", "sourcesContent": ["import { requireNativeModule } from 'expo-modules-core';\n\nimport { NotificationChannelGroupManager } from './NotificationChannelGroupManager.types';\n\nexport default requireNativeModule<NotificationChannelGroupManager>(\n  'ExpoNotificationChannelGroupManager'\n);\n"]}