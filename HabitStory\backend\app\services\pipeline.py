"""
Pipeline Orchestrator
Coordinates all 9 modules to generate comprehensive weekly reports.
"""

import asyncio
import logging
import time
from typing import Dict, List, Any, Optional
from datetime import datetime

from .parsing import parsing_service
from .metrics import metrics_service
from .correlations import correlations_service
from .storytelling import storytelling_service
from .recommendations import recommendations_service
from .validation import validation_service
from .questions import questions_service
from .visualization import visualization_service
from .report_builder import report_builder_service

logger = logging.getLogger(__name__)


class WeeklyReportPipeline:
    """Orchestrates the 9-module pipeline for weekly report generation."""
    
    def __init__(self):
        self.modules = {
            1: ("Parsing", parsing_service),
            2: ("Metrics", metrics_service),
            3: ("Correlations", correlations_service),
            4: ("Storytelling", storytelling_service),
            5: ("Recommendations", recommendations_service),
            6: ("Validation", validation_service),
            7: ("Questions", questions_service),
            8: ("Visualization", visualization_service),
            9: ("Report Builder", report_builder_service)
        }
    
    async def generate_weekly_report(
        self,
        user_id: str,
        entries: List[Dict[str, Any]],
        user_traits: Optional[Dict[str, Any]] = None,
        previous_feedback: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Generate a comprehensive weekly report using all 9 modules.
        
        Args:
            user_id: User identifier for token tracking
            entries: List of journal entries for the week
            user_traits: User personality traits and communication style
            previous_feedback: Previous feedback to incorporate
            
        Returns:
            Dict containing the final report and all intermediate results
        """
        start_time = time.time()
        pipeline_results = {
            "user_id": user_id,
            "generation_start": datetime.now().isoformat(),
            "modules_executed": [],
            "errors": [],
            "warnings": [],
            "final_report": None,
            "metadata": {}
        }
        
        try:
            logger.info(f"Starting weekly report pipeline for user {user_id}")
            
            # Validate inputs
            if not entries:
                raise ValueError("No journal entries provided")
            
            if not user_traits:
                user_traits = self._get_default_user_traits()
            
            # Module 1: Parse entries (if needed - entries might already be parsed)
            logger.info("Module 1: Parsing entries")
            parsed_entries = await self._ensure_entries_parsed(user_id, entries, user_traits)
            pipeline_results["modules_executed"].append("parsing")
            
            # Module 2: Calculate metrics and statistics
            logger.info("Module 2: Calculating metrics")
            stats = metrics_service.calculate_weekly_stats(parsed_entries)
            pipeline_results["stats"] = stats
            pipeline_results["modules_executed"].append("metrics")
            
            # Module 3: Find correlations
            logger.info("Module 3: Finding correlations")
            correlations = correlations_service.find_correlations(parsed_entries)
            pipeline_results["correlations"] = correlations
            pipeline_results["modules_executed"].append("correlations")
            
            # Module 6: Validate data quality (run early to inform other modules)
            logger.info("Module 6: Validating data quality")
            validation_results = await validation_service.validate_weekly_data(
                user_id, parsed_entries, stats, user_traits
            )
            pipeline_results["validation"] = validation_results
            pipeline_results["modules_executed"].append("validation")
            
            # Module 8: Generate visualizations
            logger.info("Module 8: Generating visualizations")
            visualizations = visualization_service.generate_weekly_visualizations(
                parsed_entries, stats, correlations
            )
            pipeline_results["visualizations"] = visualizations
            pipeline_results["modules_executed"].append("visualization")
            
            # Module 4: Generate story narrative
            logger.info("Module 4: Generating story")
            story = await storytelling_service.generate_weekly_story(
                user_id, parsed_entries, stats, correlations, user_traits, previous_feedback
            )
            pipeline_results["story"] = story
            pipeline_results["modules_executed"].append("storytelling")
            
            # Module 5: Generate recommendations
            logger.info("Module 5: Generating recommendations")
            recommendations = await recommendations_service.generate_weekly_recommendations(
                user_id, parsed_entries, stats, correlations, user_traits, previous_feedback
            )
            pipeline_results["recommendations"] = recommendations
            pipeline_results["modules_executed"].append("recommendations")
            
            # Module 7: Generate follow-up questions
            logger.info("Module 7: Generating questions")
            questions = await questions_service.generate_weekly_questions(
                user_id, parsed_entries, stats, correlations, user_traits, validation_results
            )
            pipeline_results["questions"] = questions
            pipeline_results["modules_executed"].append("questions")
            
            # Module 9: Build final HTML report
            logger.info("Module 9: Building final report")
            final_report = await report_builder_service.build_weekly_report(
                user_id, parsed_entries, stats, correlations, story, 
                recommendations, questions, visualizations, validation_results,
                user_traits, previous_feedback
            )
            pipeline_results["final_report"] = final_report
            pipeline_results["modules_executed"].append("report_builder")
            
            # Calculate execution metadata
            end_time = time.time()
            pipeline_results["metadata"] = {
                "total_execution_time": end_time - start_time,
                "entries_processed": len(parsed_entries),
                "modules_completed": len(pipeline_results["modules_executed"]),
                "data_quality_score": validation_results.get("data_quality_score", 0),
                "visualizations_generated": len(visualizations),
                "recommendations_count": len(recommendations),
                "questions_count": len(questions),
                "generation_end": datetime.now().isoformat()
            }
            
            logger.info(f"Pipeline completed successfully in {end_time - start_time:.2f} seconds")
            return pipeline_results
            
        except Exception as e:
            logger.error(f"Pipeline execution failed: {e}")
            pipeline_results["errors"].append(str(e))
            
            # Generate fallback report
            try:
                fallback_report = await self._generate_fallback_report(
                    user_id, entries, user_traits, str(e)
                )
                pipeline_results["final_report"] = fallback_report
                pipeline_results["metadata"] = {
                    "total_execution_time": time.time() - start_time,
                    "fallback_generated": True,
                    "error": str(e)
                }
            except Exception as fallback_error:
                logger.error(f"Fallback report generation failed: {fallback_error}")
                pipeline_results["errors"].append(f"Fallback failed: {str(fallback_error)}")
            
            return pipeline_results
    
    async def generate_daily_feedback(
        self,
        user_id: str,
        entry: Dict[str, Any],
        user_traits: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Generate quick daily feedback for a single entry.
        
        Args:
            user_id: User identifier
            entry: Single journal entry
            user_traits: User personality traits
            
        Returns:
            Dict containing daily feedback
        """
        try:
            logger.info(f"Generating daily feedback for user {user_id}")
            
            if not user_traits:
                user_traits = self._get_default_user_traits()
            
            # Parse the entry if needed
            if not entry.get("habits") or not entry.get("metrics"):
                parsed_data = await parsing_service.parse_entry(
                    entry.get("text", ""), user_id, user_traits
                )
                entry.update(parsed_data)
            
            # Generate quick insights
            insights = []
            
            # Check metrics for notable values
            metrics = entry.get("metrics", {})
            for metric, value in metrics.items():
                if isinstance(value, (int, float)):
                    if metric == "mood_score" and value >= 8:
                        insights.append(f"Great mood today! Your {metric.replace('_', ' ')} of {value} shows you're feeling positive.")
                    elif metric == "stress_level" and value <= 3:
                        insights.append(f"Low stress levels today ({value}) - you're managing well!")
                    elif metric == "sleep_hours" and value >= 8:
                        insights.append(f"Excellent sleep! {value} hours will help you feel energized.")
            
            # Check habits
            habits = entry.get("qualitative_habits", [])
            completed_habits = [h for h in habits if h.get("completed", False)]
            if completed_habits:
                insights.append(f"You completed {len(completed_habits)} habits today - keep up the momentum!")
            
            # Generate encouragement based on user traits
            tone = user_traits.get("tone", "informal")
            if tone == "formal":
                encouragement = "Your consistent effort in self-reflection demonstrates admirable commitment to personal growth."
            else:
                encouragement = "You're doing great by taking time to reflect on your day! Every entry is a step forward."
            
            return {
                "insights": insights,
                "encouragement": encouragement,
                "reflection_prompt": "What's one thing from today that you want to remember?",
                "parsed_data": {
                    "habits": entry.get("habits", []),
                    "qualitative_habits": entry.get("qualitative_habits", []),
                    "metrics": entry.get("metrics", {}),
                    "reflection": entry.get("reflection", ""),
                    "user_traits": entry.get("user_traits", user_traits)
                }
            }
            
        except Exception as e:
            logger.error(f"Error generating daily feedback: {e}")
            return {
                "insights": [],
                "encouragement": "Thank you for taking time to reflect today!",
                "reflection_prompt": "How are you feeling about your day?",
                "error": str(e)
            }
    
    async def _ensure_entries_parsed(
        self,
        user_id: str,
        entries: List[Dict[str, Any]],
        user_traits: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """Ensure all entries are properly parsed."""
        parsed_entries = []
        
        for entry in entries:
            try:
                # Check if entry is already parsed
                if (entry.get("habits") is not None and 
                    entry.get("metrics") is not None and 
                    entry.get("reflection") is not None):
                    parsed_entries.append(entry)
                else:
                    # Parse the entry
                    text = entry.get("text", "")
                    if text:
                        parsed_data = await parsing_service.parse_entry(
                            text, user_id, user_traits
                        )
                        # Merge parsed data with original entry
                        merged_entry = {**entry, **parsed_data}
                        parsed_entries.append(merged_entry)
                    else:
                        # Add empty parsed data
                        empty_parsed = {
                            "habits": [],
                            "qualitative_habits": [],
                            "metrics": {},
                            "reflection": "",
                            "user_traits": user_traits
                        }
                        merged_entry = {**entry, **empty_parsed}
                        parsed_entries.append(merged_entry)
                        
            except Exception as e:
                logger.warning(f"Error parsing entry: {e}")
                # Add entry with minimal parsed data
                minimal_parsed = {
                    "habits": [],
                    "qualitative_habits": [],
                    "metrics": {},
                    "reflection": "Unable to parse automatically",
                    "user_traits": user_traits
                }
                merged_entry = {**entry, **minimal_parsed}
                parsed_entries.append(merged_entry)
        
        return parsed_entries
    
    def _get_default_user_traits(self) -> Dict[str, Any]:
        """Get default user traits when none provided."""
        return {
            "tone": "informal",
            "style": "conversational",
            "traits": {
                "reflective": True,
                "growth_oriented": True
            },
            "trait_evidence": {
                "reflective": ["engages in journaling"],
                "growth_oriented": ["seeks personal insights"]
            }
        }
    
    async def _generate_fallback_report(
        self,
        user_id: str,
        entries: List[Dict[str, Any]],
        user_traits: Dict[str, Any],
        error_message: str
    ) -> str:
        """Generate a fallback report when the pipeline fails."""
        try:
            # Create basic stats
            basic_stats = {
                "period": {
                    "total_entries": len(entries),
                    "start_date": entries[0].get("date", "Unknown") if entries else "Unknown",
                    "end_date": entries[-1].get("date", "Unknown") if entries else "Unknown"
                }
            }
            
            # Generate simple story
            tone = user_traits.get("tone", "informal")
            if tone == "formal":
                story = f"This week you maintained your commitment to self-reflection with {len(entries)} journal entries. Your dedication to personal growth is commendable."
            else:
                story = f"What a week! You wrote {len(entries)} journal entries, showing your awesome commitment to growth and self-awareness."
            
            # Basic recommendations
            recommendations = [
                {
                    "title": "Keep Journaling",
                    "description": "Your consistent journaling practice is building valuable self-awareness.",
                    "actionable_steps": ["Continue daily entries", "Reflect on patterns", "Celebrate progress"]
                }
            ]
            
            # Use report builder for fallback
            return await report_builder_service._generate_fallback_report(
                user_traits, story, recommendations
            )
            
        except Exception as e:
            logger.error(f"Fallback report generation failed: {e}")
            return f"""<!DOCTYPE html>
<html><head><title>Weekly Report</title></head>
<body style="font-family: system-ui; padding: 20px; text-align: center;">
<h1>Weekly Summary</h1>
<p>Thank you for your {len(entries)} journal entries this week!</p>
<p>Your commitment to self-reflection is valuable for personal growth.</p>
<p><em>Report generation encountered technical difficulties, but your progress continues!</em></p>
</body></html>"""


# Global pipeline instance
weekly_report_pipeline = WeeklyReportPipeline()
