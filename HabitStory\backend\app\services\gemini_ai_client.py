"""
Gemini AI client implementation using the unified interface.
"""

import httpx
import asyncio
import json
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta

from .ai_client import BaseAIClient, AIProvider, AIMessage, AIResponse
from ..config import settings

logger = logging.getLogger(__name__)


class TokenCounter:
    """Token counting utility for Gemini."""
    
    def __init__(self):
        # Use a simple approximation for token counting
        # 1 token ≈ 4 characters for most text
        self.chars_per_token = 4
    
    def count_tokens(self, text: str) -> int:
        """Count approximate tokens in text."""
        return len(text) // self.chars_per_token


class GeminiAIClient(BaseAIClient):
    """Gemini AI client implementation."""
    
    def __init__(self):
        super().__init__(AIProvider.GEMINI)
        self.api_key = settings.gemini_api_key
        self.api_url = settings.gemini_api_url
        self.timeout = settings.gemini_timeout
        self.max_retries = settings.gemini_max_retries
        self.token_counter = TokenCounter()
        
        # Rate limiting
        self.last_request_time = None
        self.min_request_interval = 1.0  # Minimum seconds between requests
        
        if not self.api_key:
            logger.warning("Gemini API key not configured")
    
    def count_tokens(self, text: str) -> int:
        """Count tokens in the given text."""
        return self.token_counter.count_tokens(text)
    
    async def _check_rate_limit(self):
        """Implement basic rate limiting."""
        if self.last_request_time:
            elapsed = datetime.now().timestamp() - self.last_request_time
            if elapsed < self.min_request_interval:
                wait_time = self.min_request_interval - elapsed
                await asyncio.sleep(wait_time)
        
        self.last_request_time = datetime.now().timestamp()
    
    def _messages_to_prompt(self, messages: List[AIMessage]) -> str:
        """Convert messages to a single prompt for Gemini."""
        prompt_parts = []
        
        for message in messages:
            if message.role == "system":
                prompt_parts.append(f"System Instructions: {message.content}")
            elif message.role == "user":
                prompt_parts.append(f"User: {message.content}")
            elif message.role == "assistant":
                prompt_parts.append(f"Assistant: {message.content}")
        
        return "\n\n".join(prompt_parts)
    
    async def _make_request(
        self,
        prompt: str,
        temperature: float = 0.7,
        max_tokens: int = 2048,
        functions: Optional[List[Dict]] = None
    ) -> Dict[str, Any]:
        """Make a single request to Gemini API."""
        await self._check_rate_limit()
        
        # Prepare request payload
        payload = {
            "contents": [{
                "parts": [{
                    "text": prompt
                }]
            }],
            "generationConfig": {
                "temperature": temperature,
                "topK": 40,
                "topP": 0.95,
                "maxOutputTokens": max_tokens,
            }
        }
        
        # Add function calling if provided
        if functions:
            payload["tools"] = [{
                "functionDeclarations": functions
            }]
        
        headers = {
            "Content-Type": "application/json",
        }
        
        async with httpx.AsyncClient(timeout=self.timeout) as client:
            response = await client.post(
                f"{self.api_url}?key={self.api_key}",
                json=payload,
                headers=headers
            )
            response.raise_for_status()
            return response.json()
    
    async def generate_response(
        self,
        messages: List[AIMessage],
        temperature: float = 0.7,
        max_tokens: int = 2048,
        functions: Optional[List[Dict]] = None,
        user_id: Optional[str] = None
    ) -> AIResponse:
        """Generate a response from Gemini."""
        if not self.api_key:
            raise ValueError("Gemini API key not configured")
        
        # Convert messages to prompt
        prompt = self._messages_to_prompt(messages)
        
        # Count input tokens
        input_tokens = self.count_tokens(prompt)
        
        # Track token usage if user_id provided
        if user_id:
            await self._track_token_usage(user_id, input_tokens, "input")
        
        last_exception = None
        
        for attempt in range(self.max_retries + 1):
            try:
                logger.info(f"Gemini API call attempt {attempt + 1}/{self.max_retries + 1}")
                
                response_data = await self._make_request(
                    prompt,
                    temperature=temperature,
                    max_tokens=max_tokens,
                    functions=functions
                )
                
                # Extract response content
                if (response_data.get("candidates") and 
                    len(response_data["candidates"]) > 0 and
                    response_data["candidates"][0].get("content") and
                    response_data["candidates"][0]["content"].get("parts") and
                    len(response_data["candidates"][0]["content"]["parts"]) > 0):
                    
                    content = response_data["candidates"][0]["content"]["parts"][0]["text"]
                    
                    # Count output tokens
                    output_tokens = self.count_tokens(content)
                    total_tokens = input_tokens + output_tokens
                    
                    # Track output token usage
                    if user_id:
                        await self._track_token_usage(user_id, output_tokens, "output")
                    
                    # Create response
                    return AIResponse(
                        content=content,
                        provider=self.provider,
                        model="gemini-1.5-pro",
                        tokens_used=total_tokens,
                        input_tokens=input_tokens,
                        output_tokens=output_tokens,
                        metadata={
                            "attempt": attempt + 1,
                            "raw_response": response_data
                        }
                    )
                else:
                    raise ValueError("Invalid response format from Gemini API")
                    
            except httpx.HTTPStatusError as e:
                last_exception = e
                if e.response.status_code == 429:  # Rate limit
                    wait_time = 2 ** attempt  # Exponential backoff
                    logger.warning(f"Rate limited, waiting {wait_time} seconds")
                    await asyncio.sleep(wait_time)
                elif e.response.status_code == 400:
                    logger.error(f"Bad request: {e.response.text}")
                    raise ValueError(f"Invalid request: {e.response.text}")
                elif e.response.status_code == 403:
                    logger.error("Invalid API key")
                    raise ValueError("Invalid Gemini API key")
                else:
                    logger.error(f"HTTP error {e.response.status_code}: {e.response.text}")
                    if attempt == self.max_retries:
                        raise
                    await asyncio.sleep(2 ** attempt)
                    
            except (httpx.RequestError, asyncio.TimeoutError) as e:
                last_exception = e
                if attempt == self.max_retries:
                    logger.error(f"Request failed after {self.max_retries + 1} attempts: {e}")
                    raise ValueError(f"Failed to connect to Gemini API: {e}")
                
                wait_time = 2 ** attempt
                logger.warning(f"Request failed, retrying in {wait_time} seconds: {e}")
                await asyncio.sleep(wait_time)
                
            except Exception as e:
                last_exception = e
                logger.error(f"Unexpected error: {e}")
                if attempt == self.max_retries:
                    raise
                await asyncio.sleep(2 ** attempt)
        
        # If we get here, all retries failed
        raise ValueError(f"Gemini API call failed after {self.max_retries + 1} attempts: {last_exception}")
    
    async def health_check(self) -> bool:
        """Check if Gemini API is healthy."""
        try:
            test_messages = [
                AIMessage("system", "You are a test assistant."),
                AIMessage("user", "Respond with 'OK' if you can process this message.")
            ]
            
            response = await self.generate_response(
                messages=test_messages,
                temperature=0,
                max_tokens=10,
                user_id="health_check"
            )
            
            return "ok" in response.content.lower()
            
        except Exception as e:
            logger.error(f"Gemini health check failed: {e}")
            return False
    
    async def _track_token_usage(self, user_id: str, tokens: int, token_type: str):
        """Track token usage for a user (placeholder for database integration)."""
        # This will be implemented when we add the database models
        logger.info(f"Token usage - User: {user_id}, Tokens: {tokens}, Type: {token_type}")


# Create global Gemini client instance
gemini_ai_client = GeminiAIClient()
