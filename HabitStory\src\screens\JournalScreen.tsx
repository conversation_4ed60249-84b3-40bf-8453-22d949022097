import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Alert,
  ActivityIndicator,
  KeyboardAvoidingView,
  Platform,
  Animated,
  StyleSheet,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';

import { useGemini } from '../hooks/useGemini';
import { useEntries } from '../hooks/useEntries';
import { useHabitTracking } from '../hooks/useHabitTracking';
import { upsertTraits, formatDate } from '../lib/db';

const JournalScreen: React.FC = () => {
  const [entryText, setEntryText] = useState('');
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [todayEntry, setTodayEntry] = useState<any>(null);
  const [isAutoSaving, setIsAutoSaving] = useState(false);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);
  const [autoSaveTimeout, setAutoSaveTimeout] = useState<NodeJS.Timeout | null>(null);
  const [fadeAnim] = useState(new Animated.Value(0));

  const { parseEntryText, error: geminiError, clearError } = useGemini();
  const { addEntry, upsertEntry, getEntryByDate, editEntry, error: entriesError } = useEntries();
  const { trackHabit, todayHabits, error: habitError } = useHabitTracking();

  const today = formatDate(new Date());

  useEffect(() => {
    loadTodayEntry();
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 500,
      useNativeDriver: true,
    }).start();
  }, []);

  // Auto-save functionality
  useEffect(() => {
    if (autoSaveTimeout) {
      clearTimeout(autoSaveTimeout);
    }

    if (entryText.trim() && entryText !== (todayEntry?.text || '')) {
      const timeout = setTimeout(() => {
        handleAutoSave();
      }, 3000);
      setAutoSaveTimeout(timeout);
    }

    return () => {
      if (autoSaveTimeout) {
        clearTimeout(autoSaveTimeout);
      }
    };
  }, [entryText]);

  const loadTodayEntry = async () => {
    try {
      const entry = await getEntryByDate(today);
      if (entry) {
        setTodayEntry(entry);
        setEntryText(entry.text || '');
      }
    } catch (error) {
      console.error('Failed to load today entry:', error);
    }
  };

  const handleAutoSave = async () => {
    if (!entryText.trim()) return;

    setIsAutoSaving(true);
    try {
      // Use upsert to handle both new and existing entries
      const savedEntry = await upsertEntry(
        today,
        entryText,
        JSON.parse(todayEntry?.habits || '[]'),
        JSON.parse(todayEntry?.metrics || '{}'),
        todayEntry?.reflection || ''
      );
      setTodayEntry(savedEntry);
      setLastSaved(new Date());
    } catch (error) {
      console.error('Auto-save failed:', error);
    } finally {
      setIsAutoSaving(false);
    }
  };

  const handleAnalyze = async () => {
    if (!entryText.trim()) {
      Alert.alert('Error', 'Please write something in your journal first.');
      return;
    }

    setIsAnalyzing(true);
    clearError();

    try {
      // First, save the entry text if not already saved
      if (!todayEntry || todayEntry.text !== entryText) {
        await handleAutoSave();
      }

      // Parse the entry with Gemini
      const analysis = await parseEntryText(entryText);

      // Save the analysis results using upsert to handle existing entries
      const habits = analysis.habits || [];
      const metrics = analysis.metrics || {};
      const reflection = analysis.reflection || '';

      const savedEntry = await upsertEntry(
        today,
        entryText,
        habits,
        metrics,
        reflection
      );

      setTodayEntry(savedEntry);

      // Track habits
      if (analysis.habits && Array.isArray(analysis.habits)) {
        for (const habit of analysis.habits) {
          await trackHabit(habit, 'personal', today, true, 0.8);
        }
      }

      // Track qualitative habits if available
      if (analysis.qualitative_habits && Array.isArray(analysis.qualitative_habits)) {
        for (const habit of analysis.qualitative_habits) {
          await trackHabit(
            habit.name,
            habit.category || 'personal',
            today,
            habit.completed || true,
            habit.confidence || 0.8
          );
        }
      }

      // Update user traits if provided
      if (analysis.user_traits) {
        await upsertTraits(
          analysis.user_traits.tone || 'neutral',
          analysis.user_traits.style || 'casual',
          analysis.user_traits.traits || {},
          analysis.user_traits.trait_evidence || {}
        );
      }

      // Haptic feedback for success
      await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);

      Alert.alert(
        'Analysis Complete!',
        'Your journal entry has been analyzed and insights have been extracted.',
        [{ text: 'Great!', style: 'default' }]
      );

    } catch (error) {
      console.error('Analysis failed:', error);
      await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);

      Alert.alert(
        'Analysis Failed',
        error instanceof Error ? error.message : 'Something went wrong. Please try again.',
        [{ text: 'OK', style: 'default' }]
      );
    } finally {
      setIsAnalyzing(false);
    }
  };

  const renderAnalysisResults = () => {
    if (!todayEntry) return null;

    const habits = JSON.parse(todayEntry.habits || '[]');
    const metrics = JSON.parse(todayEntry.metrics || '{}');

    return (
      <Animated.View style={[styles.analysisCard, { opacity: fadeAnim }]}>
        <Text style={styles.analysisTitle}>Analysis Results</Text>

        {todayHabits.length > 0 && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Habits Tracked</Text>
            {todayHabits.map((habit, index) => (
              <View
                key={index}
                style={[
                  styles.habitItem,
                  { backgroundColor: habit.completed ? '#dcfce7' : '#fee2e2' }
                ]}
              >
                <View style={styles.habitLeft}>
                  <View style={[
                    styles.habitIcon,
                    { backgroundColor: habit.completed ? '#bbf7d0' : '#fecaca' }
                  ]}>
                    <Ionicons
                      name={habit.completed ? 'checkmark' : 'close'}
                      size={20}
                      color={habit.completed ? '#059669' : '#DC2626'}
                    />
                  </View>
                  <Text style={[
                    styles.habitName,
                    { color: habit.completed ? '#166534' : '#991b1b' }
                  ]}>
                    {habit.habit_name}
                  </Text>
                </View>
                <View style={styles.habitCategory}>
                  <Text style={styles.habitCategoryText}>{habit.habit_category}</Text>
                </View>
              </View>
            ))}
          </View>
        )}

        {habits.length > 0 && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Activities</Text>
            <View style={styles.tagContainer}>
              {habits.map((habit: string, index: number) => (
                <View key={index} style={styles.activityTag}>
                  <Text style={styles.activityTagText}>{habit}</Text>
                </View>
              ))}
            </View>
          </View>
        )}

        {Object.keys(metrics).length > 0 && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Metrics</Text>
            <View style={styles.metricsContainer}>
              {Object.entries(metrics).map(([key, value]) => (
                <View key={key} style={styles.metricRow}>
                  <Text style={styles.metricKey}>{key.replace('_', ' ')}</Text>
                  <Text style={styles.metricValue}>{value}</Text>
                </View>
              ))}
            </View>
          </View>
        )}

        {todayEntry.reflection && (
          <View>
            <Text style={styles.sectionTitle}>Reflection</Text>
            <View style={styles.reflectionContainer}>
              <Text style={styles.reflectionText}>{todayEntry.reflection}</Text>
            </View>
          </View>
        )}
      </Animated.View>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardView}
      >
        <ScrollView style={styles.scrollView}>
          <View style={styles.header}>
            <Text style={styles.headerTitle}>Daily Journal</Text>
            <Text style={styles.headerDate}>
              {new Date().toLocaleDateString('en-US', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric',
              })}
            </Text>
          </View>

          <View style={styles.content}>
            {renderAnalysisResults()}

            <View style={styles.journalSection}>
              <View style={styles.journalHeader}>
                <Text style={styles.journalTitle}>
                  How was your day?
                </Text>
                <Text style={styles.charCount}>
                  {entryText.length} chars
                </Text>
              </View>

              <View style={styles.inputContainer}>
                <TextInput
                  style={styles.textInput}
                  placeholder="Write about your day... Include activities, feelings, habits, sleep, exercise, or anything meaningful to you."
                  value={entryText}
                  onChangeText={setEntryText}
                  multiline
                  textAlignVertical="top"
                  placeholderTextColor="#9ca3af"
                />

                <View style={styles.saveStatus}>
                  <View style={styles.saveStatusContent}>
                    <View style={styles.saveStatusLeft}>
                      {isAutoSaving && (
                        <>
                          <ActivityIndicator size="small" color="#4F46E5" />
                          <Text style={styles.savingText}>Saving...</Text>
                        </>
                      )}
                      {!isAutoSaving && lastSaved && (
                        <View style={styles.savedStatus}>
                          <Ionicons name="checkmark-circle" size={16} color="#059669" />
                          <Text style={styles.savedText}>
                            Saved at {lastSaved.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                          </Text>
                        </View>
                      )}
                      {!isAutoSaving && !lastSaved && entryText.trim().length > 0 && (
                        <Text style={styles.autoSaveText}>
                          <Ionicons name="time-outline" size={16} /> Auto-save in 3s...
                        </Text>
                      )}
                    </View>
                  </View>
                </View>
              </View>
            </View>

            {(geminiError || entriesError) && (
              <View style={styles.errorContainer}>
                <View style={styles.errorHeader}>
                  <Ionicons name="alert-circle" size={24} color="#DC2626" />
                  <Text style={styles.errorTitle}>Error</Text>
                </View>
                <Text style={styles.errorText}>{geminiError || entriesError}</Text>
              </View>
            )}

            <TouchableOpacity
              style={[
                styles.analyzeButton,
                (isAnalyzing || !entryText.trim()) ? styles.analyzeButtonDisabled : styles.analyzeButtonEnabled
              ]}
              onPress={handleAnalyze}
              disabled={isAnalyzing || !entryText.trim()}
            >
              {isAnalyzing ? (
                <View style={styles.analyzingContent}>
                  <ActivityIndicator color="white" size="small" />
                  <Text style={styles.analyzingText}>Analyzing...</Text>
                </View>
              ) : (
                <Text style={styles.analyzeButtonText}>
                  {todayEntry ? 'Update Analysis' : 'Analyze Entry'}
                </Text>
              )}
            </TouchableOpacity>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9fafb',
  },
  keyboardView: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    backgroundColor: '#4f46e5',
    paddingHorizontal: 24,
    paddingVertical: 32,
    borderBottomLeftRadius: 24,
    borderBottomRightRadius: 24,
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 8,
  },
  headerDate: {
    color: 'rgba(255, 255, 255, 0.9)',
    fontSize: 18,
  },
  content: {
    paddingHorizontal: 24,
    paddingVertical: 16,
  },
  analysisCard: {
    backgroundColor: 'rgba(59, 130, 246, 0.1)',
    padding: 24,
    borderRadius: 16,
    marginBottom: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  analysisTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#374151',
    marginBottom: 16,
  },
  section: {
    marginBottom: 16,
  },
  sectionTitle: {
    fontWeight: '600',
    color: '#374151',
    marginBottom: 8,
  },
  habitItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 12,
    marginBottom: 8,
    borderRadius: 12,
  },
  habitLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  habitIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  habitName: {
    marginLeft: 12,
    fontWeight: '500',
  },
  habitCategory: {
    backgroundColor: 'rgba(255, 255, 255, 0.5)',
    borderRadius: 16,
    paddingHorizontal: 12,
    paddingVertical: 4,
  },
  habitCategoryText: {
    fontSize: 12,
    color: '#6b7280',
  },
  tagContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  activityTag: {
    backgroundColor: '#dbeafe',
    borderRadius: 16,
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginRight: 8,
    marginBottom: 8,
  },
  activityTagText: {
    color: '#1e40af',
  },
  metricsContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.5)',
    borderRadius: 12,
    padding: 16,
  },
  metricRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  metricKey: {
    color: '#374151',
    textTransform: 'capitalize',
  },
  metricValue: {
    color: '#2563eb',
    fontWeight: '600',
  },
  reflectionContainer: {
    backgroundColor: '#e9d5ff',
    borderRadius: 12,
    padding: 16,
  },
  reflectionText: {
    color: '#6b21a8',
    fontStyle: 'italic',
  },
  journalSection: {
    marginBottom: 24,
  },
  journalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  journalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#374151',
  },
  charCount: {
    fontSize: 14,
    color: '#6b7280',
  },
  inputContainer: {
    backgroundColor: 'white',
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
    overflow: 'hidden',
  },
  textInput: {
    padding: 16,
    fontSize: 16,
    minHeight: 200,
    color: '#374151',
    lineHeight: 24,
  },
  saveStatus: {
    backgroundColor: '#f9fafb',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderTopWidth: 1,
    borderTopColor: '#f3f4f6',
  },
  saveStatusContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  saveStatusLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  savingText: {
    fontSize: 14,
    color: '#2563eb',
    marginLeft: 8,
  },
  savedStatus: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  savedText: {
    fontSize: 14,
    color: '#059669',
    marginLeft: 4,
  },
  autoSaveText: {
    fontSize: 14,
    color: '#6b7280',
  },
  errorContainer: {
    backgroundColor: '#fef2f2',
    borderWidth: 1,
    borderColor: '#fecaca',
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
  },
  errorHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  errorTitle: {
    color: '#991b1b',
    fontWeight: '500',
    marginLeft: 8,
  },
  errorText: {
    color: '#b91c1c',
  },
  analyzeButton: {
    borderRadius: 12,
    paddingVertical: 16,
    paddingHorizontal: 24,
    marginBottom: 24,
  },
  analyzeButtonEnabled: {
    backgroundColor: '#3b82f6',
  },
  analyzeButtonDisabled: {
    backgroundColor: '#d1d5db',
  },
  analyzingContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  analyzingText: {
    color: 'white',
    fontWeight: '600',
    marginLeft: 8,
  },
  analyzeButtonText: {
    color: 'white',
    fontWeight: '600',
    textAlign: 'center',
    fontSize: 18,
  },
});

export default JournalScreen;
