  AbiSplit com.android.build.api.dsl  AndroidSourceDirectorySet com.android.build.api.dsl  Cmake com.android.build.api.dsl  
CmakeFlags com.android.build.api.dsl  CompileOptions com.android.build.api.dsl  ExternalNativeBuild com.android.build.api.dsl  ExternalNativeBuildFlags com.android.build.api.dsl  Ndk com.android.build.api.dsl  Splits com.android.build.api.dsl  isEnable "com.android.build.api.dsl.AbiSplit  
noCompress *com.android.build.api.dsl.AndroidResources  srcDir 3com.android.build.api.dsl.AndroidSourceDirectorySet  java *com.android.build.api.dsl.AndroidSourceSet  buildConfig 'com.android.build.api.dsl.BuildFeatures  prefab 'com.android.build.api.dsl.BuildFeatures  path com.android.build.api.dsl.Cmake  	arguments $com.android.build.api.dsl.CmakeFlags  androidResources )com.android.build.api.dsl.CommonExtension  
buildFeatures )com.android.build.api.dsl.CommonExtension  compileOptions )com.android.build.api.dsl.CommonExtension  
defaultConfig )com.android.build.api.dsl.CommonExtension  externalNativeBuild )com.android.build.api.dsl.CommonExtension  	namespace )com.android.build.api.dsl.CommonExtension  
sourceSets )com.android.build.api.dsl.CommonExtension  splits )com.android.build.api.dsl.CommonExtension  sourceCompatibility (com.android.build.api.dsl.CompileOptions  targetCompatibility (com.android.build.api.dsl.CompileOptions  cmake -com.android.build.api.dsl.ExternalNativeBuild  cmake 2com.android.build.api.dsl.ExternalNativeBuildFlags  
abiFilters com.android.build.api.dsl.Ndk  isEnable com.android.build.api.dsl.Split  abi  com.android.build.api.dsl.Splits  buildConfigField *com.android.build.api.dsl.VariantDimension  externalNativeBuild *com.android.build.api.dsl.VariantDimension  ndk *com.android.build.api.dsl.VariantDimension  resValue *com.android.build.api.dsl.VariantDimension  AndroidComponentsExtension com.android.build.api.variant  JniLibsPackaging com.android.build.api.variant  	Packaging com.android.build.api.variant  Sources com.android.build.api.variant  Variant com.android.build.api.variant  VariantSelector com.android.build.api.variant  finalizeDsl 8com.android.build.api.variant.AndroidComponentsExtension  
onVariants 8com.android.build.api.variant.AndroidComponentsExtension  selector 8com.android.build.api.variant.AndroidComponentsExtension  sources 'com.android.build.api.variant.Component  name /com.android.build.api.variant.ComponentIdentity  excludes .com.android.build.api.variant.JniLibsPackaging  
pickFirsts .com.android.build.api.variant.JniLibsPackaging  jniLibs 'com.android.build.api.variant.Packaging  Flat /com.android.build.api.variant.SourceDirectories  Layered /com.android.build.api.variant.SourceDirectories  addGeneratedSourceDirectory /com.android.build.api.variant.SourceDirectories  addStaticSourceDirectory /com.android.build.api.variant.SourceDirectories  addStaticSourceDirectory 4com.android.build.api.variant.SourceDirectories.Flat  addGeneratedSourceDirectory 7com.android.build.api.variant.SourceDirectories.Layered  assets %com.android.build.api.variant.Sources  java %com.android.build.api.variant.Sources  res %com.android.build.api.variant.Sources  name %com.android.build.api.variant.Variant  	packaging %com.android.build.api.variant.Variant  sources %com.android.build.api.variant.Variant  all -com.android.build.api.variant.VariantSelector  LibraryExtension com.android.build.gradle  
sourceSets )com.android.build.gradle.LibraryExtension  AndroidSourceFile com.android.build.gradle.api  AndroidSourceSet com.android.build.gradle.api  srcFile .com.android.build.gradle.api.AndroidSourceFile  manifest -com.android.build.gradle.api.AndroidSourceSet  	dependsOn /com.android.build.gradle.internal.tasks.factory  AndroidComponentsExtension com.facebook.react  Boolean com.facebook.react  BundleHermesCTask com.facebook.react  	Directory com.facebook.react  DirectoryProperty com.facebook.react  File com.facebook.react  +GenerateAutolinkingNewArchitecturesFileTask com.facebook.react  GenerateCodegenArtifactsTask com.facebook.react  GenerateCodegenSchemaTask com.facebook.react  GeneratePackageListTask com.facebook.react  Inject com.facebook.react  	JsonUtils com.facebook.react  Jvm com.facebook.react  ListProperty com.facebook.react  MutableList com.facebook.react  Pair com.facebook.react  Plugin com.facebook.react  PrivateReactExtension com.facebook.react  Project com.facebook.react  Property com.facebook.react  Provider com.facebook.react  ReactExtension com.facebook.react  ReactPlugin com.facebook.react  ReactRootProjectPlugin com.facebook.react  RegularFileProperty com.facebook.react  String com.facebook.react  Suppress com.facebook.react  System com.facebook.react  Task com.facebook.react  Variant com.facebook.react  any com.facebook.react  apply com.facebook.react  capitalizeCompat com.facebook.react  &configureBackwardCompatibilityReactMap com.facebook.react   configureBuildConfigFieldsForApp com.facebook.react  &configureBuildConfigFieldsForLibraries com.facebook.react  configureDependencies com.facebook.react  configureDevPorts com.facebook.react  configureJavaToolChains com.facebook.react  !configureJsEnginePackagingOptions com.facebook.react  configureNamespaceForLibraries com.facebook.react   configureNewArchPackagingOptions com.facebook.react  configureReactNativeNdk com.facebook.react  configureReactTasks com.facebook.react  configureRepositories com.facebook.react  	dependsOn com.facebook.react  detectedCliFile com.facebook.react  detectedEntryFile com.facebook.react  	emptyList com.facebook.react  equals com.facebook.react  exitProcess com.facebook.react  filter com.facebook.react  	filterNot com.facebook.react  findPackageJsonFile com.facebook.react  forEach com.facebook.react  fromAutolinkingConfigJson com.facebook.react  fromPackageJson com.facebook.react  getGradleDependenciesToApply com.facebook.react  isNewArchEnabled com.facebook.react  
isNotBlank com.facebook.react  
isNotEmpty com.facebook.react  java com.facebook.react  let com.facebook.react  listOf com.facebook.react  mapOf com.facebook.react  
mutableListOf com.facebook.react  needsCodegenFromPackageJson com.facebook.react  projectPathToLibraryName com.facebook.react  readVersionAndGroupStrings com.facebook.react  showJSCRemovalMessage com.facebook.react  substringAfterLast com.facebook.react  to com.facebook.react  toIntOrNull com.facebook.react  
trimIndent com.facebook.react  Boolean !com.facebook.react.ReactExtension  	Companion !com.facebook.react.ReactExtension  DirectoryProperty !com.facebook.react.ReactExtension  File !com.facebook.react.ReactExtension  Inject !com.facebook.react.ReactExtension  	JsonUtils !com.facebook.react.ReactExtension  ListProperty !com.facebook.react.ReactExtension  MutableList !com.facebook.react.ReactExtension  Pair !com.facebook.react.ReactExtension  Project !com.facebook.react.ReactExtension  Property !com.facebook.react.ReactExtension  RegularFileProperty !com.facebook.react.ReactExtension  String !com.facebook.react.ReactExtension  bundleAssetName !com.facebook.react.ReactExtension  
bundleCommand !com.facebook.react.ReactExtension  bundleConfig !com.facebook.react.ReactExtension  cliFile !com.facebook.react.ReactExtension  
codegenDir !com.facebook.react.ReactExtension  codegenJavaPackageName !com.facebook.react.ReactExtension  debuggableVariants !com.facebook.react.ReactExtension  	emptyList !com.facebook.react.ReactExtension  enableBundleCompression !com.facebook.react.ReactExtension  enableHermesOnlyInVariants !com.facebook.react.ReactExtension  enableSoCleanup !com.facebook.react.ReactExtension  	entryFile !com.facebook.react.ReactExtension  extraPackagerArgs !com.facebook.react.ReactExtension  filter !com.facebook.react.ReactExtension  	filterNot !com.facebook.react.ReactExtension  fromAutolinkingConfigJson !com.facebook.react.ReactExtension  getGradleDependenciesToApply !com.facebook.react.ReactExtension  
hermesCommand !com.facebook.react.ReactExtension  hermesFlags !com.facebook.react.ReactExtension  java !com.facebook.react.ReactExtension  	jsRootDir !com.facebook.react.ReactExtension  libraryName !com.facebook.react.ReactExtension  listOf !com.facebook.react.ReactExtension  mapOf !com.facebook.react.ReactExtension  
mutableListOf !com.facebook.react.ReactExtension  nodeExecutableAndArgs !com.facebook.react.ReactExtension  objects !com.facebook.react.ReactExtension  project !com.facebook.react.ReactExtension  projectPathToLibraryName !com.facebook.react.ReactExtension  reactNativeDir !com.facebook.react.ReactExtension  root !com.facebook.react.ReactExtension  to !com.facebook.react.ReactExtension  Boolean +com.facebook.react.ReactExtension.Companion  	JsonUtils +com.facebook.react.ReactExtension.Companion  String +com.facebook.react.ReactExtension.Companion  	emptyList +com.facebook.react.ReactExtension.Companion  filter +com.facebook.react.ReactExtension.Companion  	filterNot +com.facebook.react.ReactExtension.Companion  fromAutolinkingConfigJson +com.facebook.react.ReactExtension.Companion  getGradleDependenciesToApply +com.facebook.react.ReactExtension.Companion  java +com.facebook.react.ReactExtension.Companion  listOf +com.facebook.react.ReactExtension.Companion  mapOf +com.facebook.react.ReactExtension.Companion  
mutableListOf +com.facebook.react.ReactExtension.Companion  projectPathToLibraryName +com.facebook.react.ReactExtension.Companion  to +com.facebook.react.ReactExtension.Companion  AndroidComponentsExtension com.facebook.react.ReactPlugin  File com.facebook.react.ReactPlugin  +GenerateAutolinkingNewArchitecturesFileTask com.facebook.react.ReactPlugin  GenerateCodegenArtifactsTask com.facebook.react.ReactPlugin  GenerateCodegenSchemaTask com.facebook.react.ReactPlugin  GeneratePackageListTask com.facebook.react.ReactPlugin  	JsonUtils com.facebook.react.ReactPlugin  Jvm com.facebook.react.ReactPlugin  PrivateReactExtension com.facebook.react.ReactPlugin  ReactExtension com.facebook.react.ReactPlugin  Task com.facebook.react.ReactPlugin  apply com.facebook.react.ReactPlugin  checkJvmVersion com.facebook.react.ReactPlugin  configureAutolinking com.facebook.react.ReactPlugin  &configureBackwardCompatibilityReactMap com.facebook.react.ReactPlugin   configureBuildConfigFieldsForApp com.facebook.react.ReactPlugin  &configureBuildConfigFieldsForLibraries com.facebook.react.ReactPlugin  configureCodegen com.facebook.react.ReactPlugin  configureDependencies com.facebook.react.ReactPlugin  configureDevPorts com.facebook.react.ReactPlugin  configureJavaToolChains com.facebook.react.ReactPlugin  configureNamespaceForLibraries com.facebook.react.ReactPlugin  configureReactNativeNdk com.facebook.react.ReactPlugin  configureReactTasks com.facebook.react.ReactPlugin  configureRepositories com.facebook.react.ReactPlugin  configureResources com.facebook.react.ReactPlugin  	dependsOn com.facebook.react.ReactPlugin  exitProcess com.facebook.react.ReactPlugin  findPackageJsonFile com.facebook.react.ReactPlugin  fromPackageJson com.facebook.react.ReactPlugin  isNewArchEnabled com.facebook.react.ReactPlugin  
isNotBlank com.facebook.react.ReactPlugin  java com.facebook.react.ReactPlugin  let com.facebook.react.ReactPlugin  needsCodegenFromPackageJson com.facebook.react.ReactPlugin  readVersionAndGroupStrings com.facebook.react.ReactPlugin  substringAfterLast com.facebook.react.ReactPlugin  toIntOrNull com.facebook.react.ReactPlugin  
trimIndent com.facebook.react.ReactPlugin  DirectoryProperty com.facebook.react.internal  Inject com.facebook.react.internal  ListProperty com.facebook.react.internal  PrivateReactExtension com.facebook.react.internal  Project com.facebook.react.internal  String com.facebook.react.internal  java com.facebook.react.internal  listOf com.facebook.react.internal  String 1com.facebook.react.internal.PrivateReactExtension  
codegenDir 1com.facebook.react.internal.PrivateReactExtension  java 1com.facebook.react.internal.PrivateReactExtension  listOf 1com.facebook.react.internal.PrivateReactExtension  nodeExecutableAndArgs 1com.facebook.react.internal.PrivateReactExtension  objects 1com.facebook.react.internal.PrivateReactExtension  reactNativeDir 1com.facebook.react.internal.PrivateReactExtension  root 1com.facebook.react.internal.PrivateReactExtension  "ModelAutolinkingAndroidProjectJson com.facebook.react.model  ModelAutolinkingConfigJson com.facebook.react.model   ModelAutolinkingDependenciesJson com.facebook.react.model  /ModelAutolinkingDependenciesPlatformAndroidJson com.facebook.react.model  (ModelAutolinkingDependenciesPlatformJson com.facebook.react.model  ModelAutolinkingProjectJson com.facebook.react.model  ModelCodegenConfig com.facebook.react.model  ModelCodegenConfigAndroid com.facebook.react.model  ModelPackageJson com.facebook.react.model  packageName ;com.facebook.react.model.ModelAutolinkingAndroidProjectJson  dependencies 3com.facebook.react.model.ModelAutolinkingConfigJson  project 3com.facebook.react.model.ModelAutolinkingConfigJson  name 9com.facebook.react.model.ModelAutolinkingDependenciesJson  nameCleansed 9com.facebook.react.model.ModelAutolinkingDependenciesJson  	platforms 9com.facebook.react.model.ModelAutolinkingDependenciesJson  
buildTypes Hcom.facebook.react.model.ModelAutolinkingDependenciesPlatformAndroidJson  cmakeListsPath Hcom.facebook.react.model.ModelAutolinkingDependenciesPlatformAndroidJson  componentDescriptors Hcom.facebook.react.model.ModelAutolinkingDependenciesPlatformAndroidJson  cxxModuleCMakeListsModuleName Hcom.facebook.react.model.ModelAutolinkingDependenciesPlatformAndroidJson  cxxModuleCMakeListsPath Hcom.facebook.react.model.ModelAutolinkingDependenciesPlatformAndroidJson  cxxModuleHeaderName Hcom.facebook.react.model.ModelAutolinkingDependenciesPlatformAndroidJson  dependencyConfiguration Hcom.facebook.react.model.ModelAutolinkingDependenciesPlatformAndroidJson  isPureCxxDependency Hcom.facebook.react.model.ModelAutolinkingDependenciesPlatformAndroidJson  libraryName Hcom.facebook.react.model.ModelAutolinkingDependenciesPlatformAndroidJson  packageImportPath Hcom.facebook.react.model.ModelAutolinkingDependenciesPlatformAndroidJson  packageInstance Hcom.facebook.react.model.ModelAutolinkingDependenciesPlatformAndroidJson  android Acom.facebook.react.model.ModelAutolinkingDependenciesPlatformJson  android 4com.facebook.react.model.ModelAutolinkingProjectJson  android +com.facebook.react.model.ModelCodegenConfig  includesGeneratedCode +com.facebook.react.model.ModelCodegenConfig  	jsSrcsDir +com.facebook.react.model.ModelCodegenConfig  name +com.facebook.react.model.ModelCodegenConfig  javaPackageName 2com.facebook.react.model.ModelCodegenConfigAndroid  
codegenConfig )com.facebook.react.model.ModelPackageJson  Any com.facebook.react.tasks  Boolean com.facebook.react.tasks  BundleHermesCTask com.facebook.react.tasks  CMAKE_FILENAME com.facebook.react.tasks  CMAKE_TEMPLATE com.facebook.react.tasks  CODEGEN_LIB_PREFIX com.facebook.react.tasks  COMPONENT_DESCRIPTOR_FILENAME com.facebook.react.tasks  COMPONENT_INCLUDE_PATH com.facebook.react.tasks  CPP_FILENAME com.facebook.react.tasks  CPP_TEMPLATE com.facebook.react.tasks  ConfigurableFileTree com.facebook.react.tasks  DefaultTask com.facebook.react.tasks  	Directory com.facebook.react.tasks  DirectoryProperty com.facebook.react.tasks  Exec com.facebook.react.tasks  ExecOperations com.facebook.react.tasks  File com.facebook.react.tasks  FileTree com.facebook.react.tasks  GENERATED_FILENAME com.facebook.react.tasks  +GenerateAutolinkingNewArchitecturesFileTask com.facebook.react.tasks  GenerateCodegenArtifactsTask com.facebook.react.tasks  GenerateCodegenSchemaTask com.facebook.react.tasks  GeneratePackageListTask com.facebook.react.tasks  
H_FILENAME com.facebook.react.tasks  Inject com.facebook.react.tasks  Input com.facebook.react.tasks  	InputFile com.facebook.react.tasks  
InputFiles com.facebook.react.tasks  Internal com.facebook.react.tasks  	JsonUtils com.facebook.react.tasks  List com.facebook.react.tasks  ListProperty com.facebook.react.tasks  Map com.facebook.react.tasks  ModelAutolinkingConfigJson com.facebook.react.tasks  /ModelAutolinkingDependenciesPlatformAndroidJson com.facebook.react.tasks  Optional com.facebook.react.tasks  OutputDirectory com.facebook.react.tasks  
OutputFile com.facebook.react.tasks  Pair com.facebook.react.tasks  Property com.facebook.react.tasks  Provider com.facebook.react.tasks  Regex com.facebook.react.tasks  RegularFile com.facebook.react.tasks  RegularFileProperty com.facebook.react.tasks  String com.facebook.react.tasks  
TaskAction com.facebook.react.tasks  apply com.facebook.react.tasks  	associate com.facebook.react.tasks  
bundleCommand com.facebook.react.tasks  bundleConfig com.facebook.react.tasks  checkNotNull com.facebook.react.tasks  cliFile com.facebook.react.tasks  cliPath com.facebook.react.tasks  
component1 com.facebook.react.tasks  
component2 com.facebook.react.tasks  deleteRecursively com.facebook.react.tasks  detectOSAwareHermesCommand com.facebook.react.tasks  
devEnabled com.facebook.react.tasks  	emptyList com.facebook.react.tasks  	entryFile com.facebook.react.tasks  error com.facebook.react.tasks  extraPackagerArgs com.facebook.react.tasks  filter com.facebook.react.tasks  	filterNot com.facebook.react.tasks  fromAutolinkingConfigJson com.facebook.react.tasks  fromPackageJson com.facebook.react.tasks  generatedFileContentsTemplate com.facebook.react.tasks  	hTemplate com.facebook.react.tasks  interpolateDynamicValues com.facebook.react.tasks  
isNotEmpty com.facebook.react.tasks  joinToString com.facebook.react.tasks  
mapNotNull com.facebook.react.tasks  
minifyEnabled com.facebook.react.tasks  moveTo com.facebook.react.tasks  
mutableListOf com.facebook.react.tasks  nodeExecutableAndArgs com.facebook.react.tasks  
plusAssign com.facebook.react.tasks  replace com.facebook.react.tasks  requireNotNull com.facebook.react.tasks  resourcesDir com.facebook.react.tasks  sanitizeCmakeListsPath com.facebook.react.tasks  to com.facebook.react.tasks  toTypedArray com.facebook.react.tasks  
trimIndent com.facebook.react.tasks  windowsAwareCommandLine com.facebook.react.tasks  	writeText com.facebook.react.tasks  File *com.facebook.react.tasks.BundleHermesCTask  apply *com.facebook.react.tasks.BundleHermesCTask  bundleAssetName *com.facebook.react.tasks.BundleHermesCTask  
bundleCommand *com.facebook.react.tasks.BundleHermesCTask  bundleConfig *com.facebook.react.tasks.BundleHermesCTask  cliFile *com.facebook.react.tasks.BundleHermesCTask  cliPath *com.facebook.react.tasks.BundleHermesCTask  detectOSAwareHermesCommand *com.facebook.react.tasks.BundleHermesCTask  
devEnabled *com.facebook.react.tasks.BundleHermesCTask  	entryFile *com.facebook.react.tasks.BundleHermesCTask  execOperations *com.facebook.react.tasks.BundleHermesCTask  extraPackagerArgs *com.facebook.react.tasks.BundleHermesCTask  getBundleCommand *com.facebook.react.tasks.BundleHermesCTask  getComposeSourceMapsCommand *com.facebook.react.tasks.BundleHermesCTask  getHermescCommand *com.facebook.react.tasks.BundleHermesCTask  group *com.facebook.react.tasks.BundleHermesCTask  
hermesCommand *com.facebook.react.tasks.BundleHermesCTask  
hermesEnabled *com.facebook.react.tasks.BundleHermesCTask  hermesFlags *com.facebook.react.tasks.BundleHermesCTask  jsBundleDir *com.facebook.react.tasks.BundleHermesCTask  jsIntermediateSourceMapsDir *com.facebook.react.tasks.BundleHermesCTask  jsSourceMapsDir *com.facebook.react.tasks.BundleHermesCTask  
minifyEnabled *com.facebook.react.tasks.BundleHermesCTask  moveTo *com.facebook.react.tasks.BundleHermesCTask  
mutableListOf *com.facebook.react.tasks.BundleHermesCTask  nodeExecutableAndArgs *com.facebook.react.tasks.BundleHermesCTask  project *com.facebook.react.tasks.BundleHermesCTask  reactNativeDir *com.facebook.react.tasks.BundleHermesCTask  resolveCompilerSourceMap *com.facebook.react.tasks.BundleHermesCTask  resolveOutputSourceMap *com.facebook.react.tasks.BundleHermesCTask  resolvePackagerSourceMapFile *com.facebook.react.tasks.BundleHermesCTask  resourcesDir *com.facebook.react.tasks.BundleHermesCTask  root *com.facebook.react.tasks.BundleHermesCTask  
runCommand *com.facebook.react.tasks.BundleHermesCTask  toTypedArray *com.facebook.react.tasks.BundleHermesCTask  windowsAwareCommandLine *com.facebook.react.tasks.BundleHermesCTask  CMAKE_FILENAME Dcom.facebook.react.tasks.GenerateAutolinkingNewArchitecturesFileTask  CMAKE_TEMPLATE Dcom.facebook.react.tasks.GenerateAutolinkingNewArchitecturesFileTask  CODEGEN_LIB_PREFIX Dcom.facebook.react.tasks.GenerateAutolinkingNewArchitecturesFileTask  COMPONENT_DESCRIPTOR_FILENAME Dcom.facebook.react.tasks.GenerateAutolinkingNewArchitecturesFileTask  COMPONENT_INCLUDE_PATH Dcom.facebook.react.tasks.GenerateAutolinkingNewArchitecturesFileTask  CPP_FILENAME Dcom.facebook.react.tasks.GenerateAutolinkingNewArchitecturesFileTask  CPP_TEMPLATE Dcom.facebook.react.tasks.GenerateAutolinkingNewArchitecturesFileTask  	Companion Dcom.facebook.react.tasks.GenerateAutolinkingNewArchitecturesFileTask  DirectoryProperty Dcom.facebook.react.tasks.GenerateAutolinkingNewArchitecturesFileTask  File Dcom.facebook.react.tasks.GenerateAutolinkingNewArchitecturesFileTask  
H_FILENAME Dcom.facebook.react.tasks.GenerateAutolinkingNewArchitecturesFileTask  	InputFile Dcom.facebook.react.tasks.GenerateAutolinkingNewArchitecturesFileTask  	JsonUtils Dcom.facebook.react.tasks.GenerateAutolinkingNewArchitecturesFileTask  List Dcom.facebook.react.tasks.GenerateAutolinkingNewArchitecturesFileTask  ModelAutolinkingConfigJson Dcom.facebook.react.tasks.GenerateAutolinkingNewArchitecturesFileTask  /ModelAutolinkingDependenciesPlatformAndroidJson Dcom.facebook.react.tasks.GenerateAutolinkingNewArchitecturesFileTask  OutputDirectory Dcom.facebook.react.tasks.GenerateAutolinkingNewArchitecturesFileTask  RegularFileProperty Dcom.facebook.react.tasks.GenerateAutolinkingNewArchitecturesFileTask  String Dcom.facebook.react.tasks.GenerateAutolinkingNewArchitecturesFileTask  
TaskAction Dcom.facebook.react.tasks.GenerateAutolinkingNewArchitecturesFileTask  apply Dcom.facebook.react.tasks.GenerateAutolinkingNewArchitecturesFileTask  autolinkInputFile Dcom.facebook.react.tasks.GenerateAutolinkingNewArchitecturesFileTask  	emptyList Dcom.facebook.react.tasks.GenerateAutolinkingNewArchitecturesFileTask  filter Dcom.facebook.react.tasks.GenerateAutolinkingNewArchitecturesFileTask  filterAndroidPackages Dcom.facebook.react.tasks.GenerateAutolinkingNewArchitecturesFileTask  fromAutolinkingConfigJson Dcom.facebook.react.tasks.GenerateAutolinkingNewArchitecturesFileTask  generateCmakeFileContent Dcom.facebook.react.tasks.GenerateAutolinkingNewArchitecturesFileTask  generateCppFileContent Dcom.facebook.react.tasks.GenerateAutolinkingNewArchitecturesFileTask  generatedOutputDirectory Dcom.facebook.react.tasks.GenerateAutolinkingNewArchitecturesFileTask  group Dcom.facebook.react.tasks.GenerateAutolinkingNewArchitecturesFileTask  	hTemplate Dcom.facebook.react.tasks.GenerateAutolinkingNewArchitecturesFileTask  
isNotEmpty Dcom.facebook.react.tasks.GenerateAutolinkingNewArchitecturesFileTask  joinToString Dcom.facebook.react.tasks.GenerateAutolinkingNewArchitecturesFileTask  
mapNotNull Dcom.facebook.react.tasks.GenerateAutolinkingNewArchitecturesFileTask  
plusAssign Dcom.facebook.react.tasks.GenerateAutolinkingNewArchitecturesFileTask  replace Dcom.facebook.react.tasks.GenerateAutolinkingNewArchitecturesFileTask  sanitizeCmakeListsPath Dcom.facebook.react.tasks.GenerateAutolinkingNewArchitecturesFileTask  
trimIndent Dcom.facebook.react.tasks.GenerateAutolinkingNewArchitecturesFileTask  	writeText Dcom.facebook.react.tasks.GenerateAutolinkingNewArchitecturesFileTask  CMAKE_FILENAME Ncom.facebook.react.tasks.GenerateAutolinkingNewArchitecturesFileTask.Companion  CMAKE_TEMPLATE Ncom.facebook.react.tasks.GenerateAutolinkingNewArchitecturesFileTask.Companion  CODEGEN_LIB_PREFIX Ncom.facebook.react.tasks.GenerateAutolinkingNewArchitecturesFileTask.Companion  COMPONENT_DESCRIPTOR_FILENAME Ncom.facebook.react.tasks.GenerateAutolinkingNewArchitecturesFileTask.Companion  COMPONENT_INCLUDE_PATH Ncom.facebook.react.tasks.GenerateAutolinkingNewArchitecturesFileTask.Companion  CPP_FILENAME Ncom.facebook.react.tasks.GenerateAutolinkingNewArchitecturesFileTask.Companion  CPP_TEMPLATE Ncom.facebook.react.tasks.GenerateAutolinkingNewArchitecturesFileTask.Companion  File Ncom.facebook.react.tasks.GenerateAutolinkingNewArchitecturesFileTask.Companion  
H_FILENAME Ncom.facebook.react.tasks.GenerateAutolinkingNewArchitecturesFileTask.Companion  	JsonUtils Ncom.facebook.react.tasks.GenerateAutolinkingNewArchitecturesFileTask.Companion  apply Ncom.facebook.react.tasks.GenerateAutolinkingNewArchitecturesFileTask.Companion  	emptyList Ncom.facebook.react.tasks.GenerateAutolinkingNewArchitecturesFileTask.Companion  filter Ncom.facebook.react.tasks.GenerateAutolinkingNewArchitecturesFileTask.Companion  fromAutolinkingConfigJson Ncom.facebook.react.tasks.GenerateAutolinkingNewArchitecturesFileTask.Companion  	hTemplate Ncom.facebook.react.tasks.GenerateAutolinkingNewArchitecturesFileTask.Companion  
isNotEmpty Ncom.facebook.react.tasks.GenerateAutolinkingNewArchitecturesFileTask.Companion  joinToString Ncom.facebook.react.tasks.GenerateAutolinkingNewArchitecturesFileTask.Companion  
mapNotNull Ncom.facebook.react.tasks.GenerateAutolinkingNewArchitecturesFileTask.Companion  
plusAssign Ncom.facebook.react.tasks.GenerateAutolinkingNewArchitecturesFileTask.Companion  replace Ncom.facebook.react.tasks.GenerateAutolinkingNewArchitecturesFileTask.Companion  sanitizeCmakeListsPath Ncom.facebook.react.tasks.GenerateAutolinkingNewArchitecturesFileTask.Companion  
trimIndent Ncom.facebook.react.tasks.GenerateAutolinkingNewArchitecturesFileTask.Companion  	writeText Ncom.facebook.react.tasks.GenerateAutolinkingNewArchitecturesFileTask.Companion  File 5com.facebook.react.tasks.GenerateCodegenArtifactsTask  	JsonUtils 5com.facebook.react.tasks.GenerateCodegenArtifactsTask  cliPath 5com.facebook.react.tasks.GenerateCodegenArtifactsTask  codegenJavaPackageName 5com.facebook.react.tasks.GenerateCodegenArtifactsTask  commandLine 5com.facebook.react.tasks.GenerateCodegenArtifactsTask  fromPackageJson 5com.facebook.react.tasks.GenerateCodegenArtifactsTask  generatedSchemaFile 5com.facebook.react.tasks.GenerateCodegenArtifactsTask  generatedSrcDir 5com.facebook.react.tasks.GenerateCodegenArtifactsTask  libraryName 5com.facebook.react.tasks.GenerateCodegenArtifactsTask  nodeExecutableAndArgs 5com.facebook.react.tasks.GenerateCodegenArtifactsTask  nodeWorkingDir 5com.facebook.react.tasks.GenerateCodegenArtifactsTask  packageJsonFile 5com.facebook.react.tasks.GenerateCodegenArtifactsTask  reactNativeDir 5com.facebook.react.tasks.GenerateCodegenArtifactsTask  resolveTaskParameters 5com.facebook.react.tasks.GenerateCodegenArtifactsTask  setupCommandLine 5com.facebook.react.tasks.GenerateCodegenArtifactsTask  to 5com.facebook.react.tasks.GenerateCodegenArtifactsTask  toTypedArray 5com.facebook.react.tasks.GenerateCodegenArtifactsTask  windowsAwareCommandLine 5com.facebook.react.tasks.GenerateCodegenArtifactsTask  File 2com.facebook.react.tasks.GenerateCodegenSchemaTask  apply 2com.facebook.react.tasks.GenerateCodegenSchemaTask  cliPath 2com.facebook.react.tasks.GenerateCodegenSchemaTask  
codegenDir 2com.facebook.react.tasks.GenerateCodegenSchemaTask  commandLine 2com.facebook.react.tasks.GenerateCodegenSchemaTask  deleteRecursively 2com.facebook.react.tasks.GenerateCodegenSchemaTask  generatedSchemaFile 2com.facebook.react.tasks.GenerateCodegenSchemaTask  generatedSrcDir 2com.facebook.react.tasks.GenerateCodegenSchemaTask  jsInputFiles 2com.facebook.react.tasks.GenerateCodegenSchemaTask  	jsRootDir 2com.facebook.react.tasks.GenerateCodegenSchemaTask  nodeExecutableAndArgs 2com.facebook.react.tasks.GenerateCodegenSchemaTask  nodeWorkingDir 2com.facebook.react.tasks.GenerateCodegenSchemaTask  setupCommandLine 2com.facebook.react.tasks.GenerateCodegenSchemaTask  toTypedArray 2com.facebook.react.tasks.GenerateCodegenSchemaTask  windowsAwareCommandLine 2com.facebook.react.tasks.GenerateCodegenSchemaTask  
wipeOutputDir 2com.facebook.react.tasks.GenerateCodegenSchemaTask  	Companion 0com.facebook.react.tasks.GeneratePackageListTask  DirectoryProperty 0com.facebook.react.tasks.GeneratePackageListTask  File 0com.facebook.react.tasks.GeneratePackageListTask  GENERATED_FILENAME 0com.facebook.react.tasks.GeneratePackageListTask  	InputFile 0com.facebook.react.tasks.GeneratePackageListTask  	JsonUtils 0com.facebook.react.tasks.GeneratePackageListTask  Map 0com.facebook.react.tasks.GeneratePackageListTask  ModelAutolinkingConfigJson 0com.facebook.react.tasks.GeneratePackageListTask  /ModelAutolinkingDependenciesPlatformAndroidJson 0com.facebook.react.tasks.GeneratePackageListTask  OutputDirectory 0com.facebook.react.tasks.GeneratePackageListTask  Regex 0com.facebook.react.tasks.GeneratePackageListTask  RegularFileProperty 0com.facebook.react.tasks.GeneratePackageListTask  String 0com.facebook.react.tasks.GeneratePackageListTask  
TaskAction 0com.facebook.react.tasks.GeneratePackageListTask  apply 0com.facebook.react.tasks.GeneratePackageListTask  	associate 0com.facebook.react.tasks.GeneratePackageListTask  autolinkInputFile 0com.facebook.react.tasks.GeneratePackageListTask  checkNotNull 0com.facebook.react.tasks.GeneratePackageListTask  
component1 0com.facebook.react.tasks.GeneratePackageListTask  
component2 0com.facebook.react.tasks.GeneratePackageListTask  composeFileContent 0com.facebook.react.tasks.GeneratePackageListTask  composePackageImports 0com.facebook.react.tasks.GeneratePackageListTask  composePackageInstance 0com.facebook.react.tasks.GeneratePackageListTask  	emptyList 0com.facebook.react.tasks.GeneratePackageListTask  error 0com.facebook.react.tasks.GeneratePackageListTask  filter 0com.facebook.react.tasks.GeneratePackageListTask  filterAndroidPackages 0com.facebook.react.tasks.GeneratePackageListTask  	filterNot 0com.facebook.react.tasks.GeneratePackageListTask  fromAutolinkingConfigJson 0com.facebook.react.tasks.GeneratePackageListTask  generatedFileContentsTemplate 0com.facebook.react.tasks.GeneratePackageListTask  generatedOutputDirectory 0com.facebook.react.tasks.GeneratePackageListTask  group 0com.facebook.react.tasks.GeneratePackageListTask  interpolateDynamicValues 0com.facebook.react.tasks.GeneratePackageListTask  joinToString 0com.facebook.react.tasks.GeneratePackageListTask  replace 0com.facebook.react.tasks.GeneratePackageListTask  requireNotNull 0com.facebook.react.tasks.GeneratePackageListTask  to 0com.facebook.react.tasks.GeneratePackageListTask  
trimIndent 0com.facebook.react.tasks.GeneratePackageListTask  	writeText 0com.facebook.react.tasks.GeneratePackageListTask  File :com.facebook.react.tasks.GeneratePackageListTask.Companion  GENERATED_FILENAME :com.facebook.react.tasks.GeneratePackageListTask.Companion  	JsonUtils :com.facebook.react.tasks.GeneratePackageListTask.Companion  Regex :com.facebook.react.tasks.GeneratePackageListTask.Companion  apply :com.facebook.react.tasks.GeneratePackageListTask.Companion  	associate :com.facebook.react.tasks.GeneratePackageListTask.Companion  checkNotNull :com.facebook.react.tasks.GeneratePackageListTask.Companion  
component1 :com.facebook.react.tasks.GeneratePackageListTask.Companion  
component2 :com.facebook.react.tasks.GeneratePackageListTask.Companion  	emptyList :com.facebook.react.tasks.GeneratePackageListTask.Companion  error :com.facebook.react.tasks.GeneratePackageListTask.Companion  filter :com.facebook.react.tasks.GeneratePackageListTask.Companion  	filterNot :com.facebook.react.tasks.GeneratePackageListTask.Companion  fromAutolinkingConfigJson :com.facebook.react.tasks.GeneratePackageListTask.Companion  generatedFileContentsTemplate :com.facebook.react.tasks.GeneratePackageListTask.Companion  interpolateDynamicValues :com.facebook.react.tasks.GeneratePackageListTask.Companion  joinToString :com.facebook.react.tasks.GeneratePackageListTask.Companion  replace :com.facebook.react.tasks.GeneratePackageListTask.Companion  requireNotNull :com.facebook.react.tasks.GeneratePackageListTask.Companion  to :com.facebook.react.tasks.GeneratePackageListTask.Companion  
trimIndent :com.facebook.react.tasks.GeneratePackageListTask.Companion  	writeText :com.facebook.react.tasks.GeneratePackageListTask.Companion  BUILD_SCRIPT_PATH !com.facebook.react.tasks.internal  BuildCodegenCLITask !com.facebook.react.tasks.internal  ConfigurableFileCollection !com.facebook.react.tasks.internal  CustomExecTask !com.facebook.react.tasks.internal  DefaultTask !com.facebook.react.tasks.internal  DirectoryProperty !com.facebook.react.tasks.internal  DuplicatesStrategy !com.facebook.react.tasks.internal  Exec !com.facebook.react.tasks.internal  File !com.facebook.react.tasks.internal  FileOutputStream !com.facebook.react.tasks.internal  FileSystemOperations !com.facebook.react.tasks.internal  FileTree !com.facebook.react.tasks.internal  Inject !com.facebook.react.tasks.internal  Input !com.facebook.react.tasks.internal  InputDirectory !com.facebook.react.tasks.internal  
InputFiles !com.facebook.react.tasks.internal  Internal !com.facebook.react.tasks.internal  ListProperty !com.facebook.react.tasks.internal  Optional !com.facebook.react.tasks.internal  OutputDirectory !com.facebook.react.tasks.internal  
OutputFile !com.facebook.react.tasks.internal  OutputFiles !com.facebook.react.tasks.internal  PrefabPreprocessingEntry !com.facebook.react.tasks.internal  PrepareBoostTask !com.facebook.react.tasks.internal  PrepareGlogTask !com.facebook.react.tasks.internal  PreparePrefabHeadersTask !com.facebook.react.tasks.internal  Property !com.facebook.react.tasks.internal  RegularFile !com.facebook.react.tasks.internal  RegularFileProperty !com.facebook.react.tasks.internal  
ReplaceTokens !com.facebook.react.tasks.internal  String !com.facebook.react.tasks.internal  
TaskAction !com.facebook.react.tasks.internal  apply !com.facebook.react.tasks.internal  boostVersion !com.facebook.react.tasks.internal  forEach !com.facebook.react.tasks.internal  java !com.facebook.react.tasks.internal  mapOf !com.facebook.react.tasks.internal  removeSuffix !com.facebook.react.tasks.internal  to !com.facebook.react.tasks.internal  unixifyPath !com.facebook.react.tasks.internal  windowsAwareBashCommandLine !com.facebook.react.tasks.internal  BUILD_SCRIPT_PATH 5com.facebook.react.tasks.internal.BuildCodegenCLITask  DirectoryProperty 5com.facebook.react.tasks.internal.BuildCodegenCLITask  FileOutputStream 5com.facebook.react.tasks.internal.BuildCodegenCLITask  FileTree 5com.facebook.react.tasks.internal.BuildCodegenCLITask  
InputFiles 5com.facebook.react.tasks.internal.BuildCodegenCLITask  Internal 5com.facebook.react.tasks.internal.BuildCodegenCLITask  
OutputFile 5com.facebook.react.tasks.internal.BuildCodegenCLITask  OutputFiles 5com.facebook.react.tasks.internal.BuildCodegenCLITask  Property 5com.facebook.react.tasks.internal.BuildCodegenCLITask  RegularFileProperty 5com.facebook.react.tasks.internal.BuildCodegenCLITask  String 5com.facebook.react.tasks.internal.BuildCodegenCLITask  apply 5com.facebook.react.tasks.internal.BuildCodegenCLITask  bashWindowsHome 5com.facebook.react.tasks.internal.BuildCodegenCLITask  
codegenDir 5com.facebook.react.tasks.internal.BuildCodegenCLITask  commandLine 5com.facebook.react.tasks.internal.BuildCodegenCLITask  logFile 5com.facebook.react.tasks.internal.BuildCodegenCLITask  rootProjectName 5com.facebook.react.tasks.internal.BuildCodegenCLITask  standardOutput 5com.facebook.react.tasks.internal.BuildCodegenCLITask  unixifyPath 5com.facebook.react.tasks.internal.BuildCodegenCLITask  windowsAwareBashCommandLine 5com.facebook.react.tasks.internal.BuildCodegenCLITask  BUILD_SCRIPT_PATH ?com.facebook.react.tasks.internal.BuildCodegenCLITask.Companion  FileOutputStream ?com.facebook.react.tasks.internal.BuildCodegenCLITask.Companion  apply ?com.facebook.react.tasks.internal.BuildCodegenCLITask.Companion  unixifyPath ?com.facebook.react.tasks.internal.BuildCodegenCLITask.Companion  windowsAwareBashCommandLine ?com.facebook.react.tasks.internal.BuildCodegenCLITask.Companion  File 0com.facebook.react.tasks.internal.CustomExecTask  FileOutputStream 0com.facebook.react.tasks.internal.CustomExecTask  errorOutput 0com.facebook.react.tasks.internal.CustomExecTask  errorOutputFile 0com.facebook.react.tasks.internal.CustomExecTask  onlyIfProvidedPathDoesNotExists 0com.facebook.react.tasks.internal.CustomExecTask  standardOutput 0com.facebook.react.tasks.internal.CustomExecTask  standardOutputFile 0com.facebook.react.tasks.internal.CustomExecTask  File 2com.facebook.react.tasks.internal.PrepareBoostTask  apply 2com.facebook.react.tasks.internal.PrepareBoostTask  	boostPath 2com.facebook.react.tasks.internal.PrepareBoostTask  boostThirdPartyJniPath 2com.facebook.react.tasks.internal.PrepareBoostTask  boostVersion 2com.facebook.react.tasks.internal.PrepareBoostTask  fs 2com.facebook.react.tasks.internal.PrepareBoostTask  	outputDir 2com.facebook.react.tasks.internal.PrepareBoostTask  DuplicatesStrategy 1com.facebook.react.tasks.internal.PrepareGlogTask  File 1com.facebook.react.tasks.internal.PrepareGlogTask  
ReplaceTokens 1com.facebook.react.tasks.internal.PrepareGlogTask  apply 1com.facebook.react.tasks.internal.PrepareGlogTask  fs 1com.facebook.react.tasks.internal.PrepareGlogTask  glogPath 1com.facebook.react.tasks.internal.PrepareGlogTask  glogThirdPartyJniPath 1com.facebook.react.tasks.internal.PrepareGlogTask  glogVersion 1com.facebook.react.tasks.internal.PrepareGlogTask  java 1com.facebook.react.tasks.internal.PrepareGlogTask  mapOf 1com.facebook.react.tasks.internal.PrepareGlogTask  	outputDir 1com.facebook.react.tasks.internal.PrepareGlogTask  removeSuffix 1com.facebook.react.tasks.internal.PrepareGlogTask  to 1com.facebook.react.tasks.internal.PrepareGlogTask  File :com.facebook.react.tasks.internal.PreparePrefabHeadersTask  fs :com.facebook.react.tasks.internal.PreparePrefabHeadersTask  input :com.facebook.react.tasks.internal.PreparePrefabHeadersTask  	outputDir :com.facebook.react.tasks.internal.PreparePrefabHeadersTask  List 'com.facebook.react.tasks.internal.utils  Pair 'com.facebook.react.tasks.internal.utils  PrefabPreprocessingEntry 'com.facebook.react.tasks.internal.utils  Serializable 'com.facebook.react.tasks.internal.utils  String 'com.facebook.react.tasks.internal.utils  listOf 'com.facebook.react.tasks.internal.utils  
component1 @com.facebook.react.tasks.internal.utils.PrefabPreprocessingEntry  
component2 @com.facebook.react.tasks.internal.utils.PrefabPreprocessingEntry  listOf @com.facebook.react.tasks.internal.utils.PrefabPreprocessingEntry  Action com.facebook.react.utils  AgpConfiguratorUtils com.facebook.react.utils  AndroidComponentsExtension com.facebook.react.utils  Any com.facebook.react.utils  
AppliedPlugin com.facebook.react.utils  BackwardCompatUtils com.facebook.react.utils  Boolean com.facebook.react.utils  DEFAULT_DEV_SERVER_PORT com.facebook.react.utils  !DEFAULT_INTERNAL_PUBLISHING_GROUP com.facebook.react.utils  DependencyUtils com.facebook.react.utils  DirectoryProperty com.facebook.react.utils  DocumentBuilder com.facebook.react.utils  DocumentBuilderFactory com.facebook.react.utils  Element com.facebook.react.utils  	Exception com.facebook.react.utils  File com.facebook.react.utils  HERMESC_BUILT_FROM_SOURCE_DIR com.facebook.react.utils  HERMESC_IN_REACT_NATIVE_DIR com.facebook.react.utils  HERMES_ENABLED com.facebook.react.utils  HERMES_FALLBACK com.facebook.react.utils  INCLUDE_JITPACK_REPOSITORY com.facebook.react.utils  "INCLUDE_JITPACK_REPOSITORY_DEFAULT com.facebook.react.utils  'INTERNAL_DISABLE_JAVA_VERSION_ALIGNMENT com.facebook.react.utils  INTERNAL_PUBLISHING_GROUP com.facebook.react.utils  &INTERNAL_REACT_NATIVE_MAVEN_LOCAL_REPO com.facebook.react.utils  INTERNAL_USE_HERMES_NIGHTLY com.facebook.react.utils  INTERNAL_VERSION_NAME com.facebook.react.utils  JavaVersion com.facebook.react.utils  JdkConfiguratorUtils com.facebook.react.utils  	JsonUtils com.facebook.react.utils  JvmName com.facebook.react.utils  LibraryExtension com.facebook.react.utils  List com.facebook.react.utils  Map com.facebook.react.utils  MavenArtifactRepository com.facebook.react.utils  ModelPackageJson com.facebook.react.utils  NEW_ARCH_ENABLED com.facebook.react.utils  NdkConfiguratorUtils com.facebook.react.utils  Os com.facebook.react.utils  Pair com.facebook.react.utils  Project com.facebook.react.utils  ProjectUtils com.facebook.react.utils  
Properties com.facebook.react.utils  
PropertyUtils com.facebook.react.utils  REACT_NATIVE_ARCHITECTURES com.facebook.react.utils  ReactExtension com.facebook.react.utils  Runtime com.facebook.react.utils  SCOPED_HERMES_ENABLED com.facebook.react.utils  !SCOPED_INCLUDE_JITPACK_REPOSITORY com.facebook.react.utils  SCOPED_NEW_ARCH_ENABLED com.facebook.react.utils  !SCOPED_REACT_NATIVE_ARCHITECTURES com.facebook.react.utils  SCOPED_USE_THIRD_PARTY_JSC com.facebook.react.utils  String com.facebook.react.utils  Suppress com.facebook.react.utils  System com.facebook.react.utils  Triple com.facebook.react.utils  URI com.facebook.react.utils  USE_THIRD_PARTY_JSC com.facebook.react.utils  Unit com.facebook.react.utils  Variant com.facebook.react.utils  arrayOf com.facebook.react.utils  bufferedReader com.facebook.react.utils  contains com.facebook.react.utils  copyTo com.facebook.react.utils  deleteRecursively com.facebook.react.utils  
detectCliFile com.facebook.react.utils  detectEntryFile com.facebook.react.utils  detectOSAwareHermesCommand com.facebook.react.utils  detectedCliFile com.facebook.react.utils  detectedEntryFile com.facebook.react.utils  detectedHermesCommand com.facebook.react.utils  
emptyArray com.facebook.react.utils  error com.facebook.react.utils  filter com.facebook.react.utils  findPackageJsonFile com.facebook.react.utils  forEach com.facebook.react.utils  fromPackageJson com.facebook.react.utils  getBuiltHermescFile com.facebook.react.utils  
getHermesCBin com.facebook.react.utils  getHermesOSBin com.facebook.react.utils  getPackageNameFromManifest com.facebook.react.utils  getReactNativeArchitectures com.facebook.react.utils  inputStream com.facebook.react.utils  isBlank com.facebook.react.utils  isLinuxAmd64 com.facebook.react.utils  isMac com.facebook.react.utils  isNewArchEnabled com.facebook.react.utils  
isNotBlank com.facebook.react.utils  
isNotEmpty com.facebook.react.utils  
isNullOrBlank com.facebook.react.utils  
isNullOrEmpty com.facebook.react.utils  	isWindows com.facebook.react.utils  java com.facebook.react.utils  joinToString com.facebook.react.utils  let com.facebook.react.utils  listOf com.facebook.react.utils  lowercaseCompat com.facebook.react.utils  mapOf com.facebook.react.utils  moveTo com.facebook.react.utils  
mutableListOf com.facebook.react.utils  none com.facebook.react.utils  orEmpty com.facebook.react.utils  projectPathToLibraryName com.facebook.react.utils  readPackageJsonFile com.facebook.react.utils  readText com.facebook.react.utils  recreateDir com.facebook.react.utils  replace com.facebook.react.utils  split com.facebook.react.utils  
startsWith com.facebook.react.utils  takeIf com.facebook.react.utils  to com.facebook.react.utils  	toBoolean com.facebook.react.utils  toBooleanStrictOrNullCompat com.facebook.react.utils  toString com.facebook.react.utils  trim com.facebook.react.utils  
trimIndent com.facebook.react.utils  use com.facebook.react.utils  windowsAwareBashCommandLine com.facebook.react.utils  windowsAwareCommandLine com.facebook.react.utils  with com.facebook.react.utils  Action -com.facebook.react.utils.AgpConfiguratorUtils  AndroidComponentsExtension -com.facebook.react.utils.AgpConfiguratorUtils  DEFAULT_DEV_SERVER_PORT -com.facebook.react.utils.AgpConfiguratorUtils  LibraryExtension -com.facebook.react.utils.AgpConfiguratorUtils   configureBuildConfigFieldsForApp -com.facebook.react.utils.AgpConfiguratorUtils  &configureBuildConfigFieldsForLibraries -com.facebook.react.utils.AgpConfiguratorUtils  configureDevPorts -com.facebook.react.utils.AgpConfiguratorUtils  configureNamespaceForLibraries -com.facebook.react.utils.AgpConfiguratorUtils  getPackageNameFromManifest -com.facebook.react.utils.AgpConfiguratorUtils  isHermesEnabled -com.facebook.react.utils.AgpConfiguratorUtils  isNewArchEnabled -com.facebook.react.utils.AgpConfiguratorUtils  java -com.facebook.react.utils.AgpConfiguratorUtils  let -com.facebook.react.utils.AgpConfiguratorUtils  takeIf -com.facebook.react.utils.AgpConfiguratorUtils  &configureBackwardCompatibilityReactMap ,com.facebook.react.utils.BackwardCompatUtils  hasShownJSCRemovalMessage ,com.facebook.react.utils.BackwardCompatUtils  
isNotEmpty ,com.facebook.react.utils.BackwardCompatUtils  mapOf ,com.facebook.react.utils.BackwardCompatUtils  showJSCRemovalMessage ,com.facebook.react.utils.BackwardCompatUtils  
trimIndent ,com.facebook.react.utils.BackwardCompatUtils  !DEFAULT_INTERNAL_PUBLISHING_GROUP (com.facebook.react.utils.DependencyUtils  File (com.facebook.react.utils.DependencyUtils  INCLUDE_JITPACK_REPOSITORY (com.facebook.react.utils.DependencyUtils  "INCLUDE_JITPACK_REPOSITORY_DEFAULT (com.facebook.react.utils.DependencyUtils  INTERNAL_PUBLISHING_GROUP (com.facebook.react.utils.DependencyUtils  &INTERNAL_REACT_NATIVE_MAVEN_LOCAL_REPO (com.facebook.react.utils.DependencyUtils  INTERNAL_USE_HERMES_NIGHTLY (com.facebook.react.utils.DependencyUtils  INTERNAL_VERSION_NAME (com.facebook.react.utils.DependencyUtils  Pair (com.facebook.react.utils.DependencyUtils  
Properties (com.facebook.react.utils.DependencyUtils  !SCOPED_INCLUDE_JITPACK_REPOSITORY (com.facebook.react.utils.DependencyUtils  Triple (com.facebook.react.utils.DependencyUtils  URI (com.facebook.react.utils.DependencyUtils  configureDependencies (com.facebook.react.utils.DependencyUtils  configureRepositories (com.facebook.react.utils.DependencyUtils  contains (com.facebook.react.utils.DependencyUtils  getDependencySubstitutions (com.facebook.react.utils.DependencyUtils  inputStream (com.facebook.react.utils.DependencyUtils  isBlank (com.facebook.react.utils.DependencyUtils  mavenRepoFromURI (com.facebook.react.utils.DependencyUtils  mavenRepoFromUrl (com.facebook.react.utils.DependencyUtils  
mutableListOf (com.facebook.react.utils.DependencyUtils  orEmpty (com.facebook.react.utils.DependencyUtils  readVersionAndGroupStrings (com.facebook.react.utils.DependencyUtils  shouldAddJitPack (com.facebook.react.utils.DependencyUtils  
startsWith (com.facebook.react.utils.DependencyUtils  	toBoolean (com.facebook.react.utils.DependencyUtils  toString (com.facebook.react.utils.DependencyUtils  use (com.facebook.react.utils.DependencyUtils  with (com.facebook.react.utils.DependencyUtils  Action -com.facebook.react.utils.JdkConfiguratorUtils  AndroidComponentsExtension -com.facebook.react.utils.JdkConfiguratorUtils  'INTERNAL_DISABLE_JAVA_VERSION_ALIGNMENT -com.facebook.react.utils.JdkConfiguratorUtils  JavaVersion -com.facebook.react.utils.JdkConfiguratorUtils  configureJavaToolChains -com.facebook.react.utils.JdkConfiguratorUtils  java -com.facebook.react.utils.JdkConfiguratorUtils  kotlinExtension -com.facebook.react.utils.JdkConfiguratorUtils  fromAutolinkingConfigJson "com.facebook.react.utils.JsonUtils  fromPackageJson "com.facebook.react.utils.JsonUtils  capitalizeCompat 0com.facebook.react.utils.KotlinStdlibCompatUtils  lowercaseCompat 0com.facebook.react.utils.KotlinStdlibCompatUtils  toBooleanStrictOrNullCompat 0com.facebook.react.utils.KotlinStdlibCompatUtils  AndroidComponentsExtension -com.facebook.react.utils.NdkConfiguratorUtils  File -com.facebook.react.utils.NdkConfiguratorUtils  !configureJsEnginePackagingOptions -com.facebook.react.utils.NdkConfiguratorUtils   configureNewArchPackagingOptions -com.facebook.react.utils.NdkConfiguratorUtils  configureReactNativeNdk -com.facebook.react.utils.NdkConfiguratorUtils  getPackagingOptionsForVariant -com.facebook.react.utils.NdkConfiguratorUtils  getReactNativeArchitectures -com.facebook.react.utils.NdkConfiguratorUtils  isNewArchEnabled -com.facebook.react.utils.NdkConfiguratorUtils  
isNotEmpty -com.facebook.react.utils.NdkConfiguratorUtils  java -com.facebook.react.utils.NdkConfiguratorUtils  listOf -com.facebook.react.utils.NdkConfiguratorUtils  
mutableListOf -com.facebook.react.utils.NdkConfiguratorUtils  none -com.facebook.react.utils.NdkConfiguratorUtils  
startsWith -com.facebook.react.utils.NdkConfiguratorUtils  to -com.facebook.react.utils.NdkConfiguratorUtils  cliPath com.facebook.react.utils.Os  isLinuxAmd64 com.facebook.react.utils.Os  isMac com.facebook.react.utils.Os  	isWindows com.facebook.react.utils.Os  unixifyPath com.facebook.react.utils.Os  HERMES_ENABLED %com.facebook.react.utils.ProjectUtils  HERMES_FALLBACK %com.facebook.react.utils.ProjectUtils  NEW_ARCH_ENABLED %com.facebook.react.utils.ProjectUtils  REACT_NATIVE_ARCHITECTURES %com.facebook.react.utils.ProjectUtils  SCOPED_HERMES_ENABLED %com.facebook.react.utils.ProjectUtils  SCOPED_NEW_ARCH_ENABLED %com.facebook.react.utils.ProjectUtils  !SCOPED_REACT_NATIVE_ARCHITECTURES %com.facebook.react.utils.ProjectUtils  SCOPED_USE_THIRD_PARTY_JSC %com.facebook.react.utils.ProjectUtils  USE_THIRD_PARTY_JSC %com.facebook.react.utils.ProjectUtils  filter %com.facebook.react.utils.ProjectUtils  getReactNativeArchitectures %com.facebook.react.utils.ProjectUtils  isHermesEnabled %com.facebook.react.utils.ProjectUtils  isNewArchEnabled %com.facebook.react.utils.ProjectUtils  
isNotBlank %com.facebook.react.utils.ProjectUtils  lowercaseCompat %com.facebook.react.utils.ProjectUtils  
mutableListOf %com.facebook.react.utils.ProjectUtils  needsCodegenFromPackageJson %com.facebook.react.utils.ProjectUtils  readPackageJsonFile %com.facebook.react.utils.ProjectUtils  split %com.facebook.react.utils.ProjectUtils  	toBoolean %com.facebook.react.utils.ProjectUtils  toBooleanStrictOrNullCompat %com.facebook.react.utils.ProjectUtils  toString %com.facebook.react.utils.ProjectUtils  useThirdPartyJSC %com.facebook.react.utils.ProjectUtils  !DEFAULT_INTERNAL_PUBLISHING_GROUP &com.facebook.react.utils.PropertyUtils  HERMES_ENABLED &com.facebook.react.utils.PropertyUtils  INCLUDE_JITPACK_REPOSITORY &com.facebook.react.utils.PropertyUtils  "INCLUDE_JITPACK_REPOSITORY_DEFAULT &com.facebook.react.utils.PropertyUtils  'INTERNAL_DISABLE_JAVA_VERSION_ALIGNMENT &com.facebook.react.utils.PropertyUtils  INTERNAL_PUBLISHING_GROUP &com.facebook.react.utils.PropertyUtils  &INTERNAL_REACT_NATIVE_MAVEN_LOCAL_REPO &com.facebook.react.utils.PropertyUtils  INTERNAL_USE_HERMES_NIGHTLY &com.facebook.react.utils.PropertyUtils  INTERNAL_VERSION_NAME &com.facebook.react.utils.PropertyUtils  NEW_ARCH_ENABLED &com.facebook.react.utils.PropertyUtils  REACT_NATIVE_ARCHITECTURES &com.facebook.react.utils.PropertyUtils  SCOPED_HERMES_ENABLED &com.facebook.react.utils.PropertyUtils  !SCOPED_INCLUDE_JITPACK_REPOSITORY &com.facebook.react.utils.PropertyUtils  SCOPED_NEW_ARCH_ENABLED &com.facebook.react.utils.PropertyUtils  !SCOPED_REACT_NATIVE_ARCHITECTURES &com.facebook.react.utils.PropertyUtils  SCOPED_USE_THIRD_PARTY_JSC &com.facebook.react.utils.PropertyUtils  USE_THIRD_PARTY_JSC &com.facebook.react.utils.PropertyUtils  BufferedReader java.io  ByteArrayInputStream java.io  File java.io  FileInputStream java.io  FileOutputStream java.io  Serializable java.io  readText java.io.BufferedReader  File java.io.File  absolutePath java.io.File  apply java.io.File  boostVersion java.io.File  
canonicalPath java.io.File  cliPath java.io.File  copyTo java.io.File  
createNewFile java.io.File  delete java.io.File  deleteRecursively java.io.File  exists java.io.File  	hTemplate java.io.File  inputStream java.io.File  let java.io.File  mkdirs java.io.File  moveTo java.io.File  
parentFile java.io.File  renameTo java.io.File  
separatorChar java.io.File  takeIf java.io.File  toURI java.io.File  	writeText java.io.File  use java.io.FileInputStream  Class 	java.lang  	Exception 	java.lang  inputStream java.lang.Process  exec java.lang.Runtime  
getRuntime java.lang.Runtime  getenv java.lang.System  URI java.net  create java.net.URI  Any 	java.util  !DEFAULT_INTERNAL_PUBLISHING_GROUP 	java.util  File 	java.util  INCLUDE_JITPACK_REPOSITORY 	java.util  "INCLUDE_JITPACK_REPOSITORY_DEFAULT 	java.util  INTERNAL_PUBLISHING_GROUP 	java.util  &INTERNAL_REACT_NATIVE_MAVEN_LOCAL_REPO 	java.util  INTERNAL_USE_HERMES_NIGHTLY 	java.util  INTERNAL_VERSION_NAME 	java.util  List 	java.util  Map 	java.util  MavenArtifactRepository 	java.util  Pair 	java.util  Project 	java.util  
Properties 	java.util  !SCOPED_INCLUDE_JITPACK_REPOSITORY 	java.util  String 	java.util  Suppress 	java.util  Triple 	java.util  URI 	java.util  Unit 	java.util  contains 	java.util  forEach 	java.util  inputStream 	java.util  isBlank 	java.util  
isNotEmpty 	java.util  mapOf 	java.util  
mutableListOf 	java.util  orEmpty 	java.util  
startsWith 	java.util  	toBoolean 	java.util  toString 	java.util  
trimIndent 	java.util  use 	java.util  with 	java.util  get java.util.Properties  load java.util.Properties  Inject javax.inject  DocumentBuilder javax.xml.parsers  DocumentBuilderFactory javax.xml.parsers  parse !javax.xml.parsers.DocumentBuilder  newDocumentBuilder (javax.xml.parsers.DocumentBuilderFactory  newInstance (javax.xml.parsers.DocumentBuilderFactory  Array kotlin  Boolean kotlin  CharSequence kotlin  	Function0 kotlin  	Function1 kotlin  Nothing kotlin  Pair kotlin  String kotlin  Suppress kotlin  Triple kotlin  apply kotlin  arrayOf kotlin  checkNotNull kotlin  
emptyArray kotlin  error kotlin  let kotlin  requireNotNull kotlin  takeIf kotlin  to kotlin  toString kotlin  use kotlin  with kotlin  equals 
kotlin.Any  toString 
kotlin.Any  	Companion kotlin.Boolean  not kotlin.Boolean  toString kotlin.Boolean  invoke kotlin.Function1  	compareTo 
kotlin.Int  
component1 kotlin.Pair  
component2 kotlin.Pair  first kotlin.Pair  second kotlin.Pair  	Companion 
kotlin.String  capitalizeCompat 
kotlin.String  contains 
kotlin.String  equals 
kotlin.String  isBlank 
kotlin.String  
isNotBlank 
kotlin.String  
isNotEmpty 
kotlin.String  
isNullOrBlank 
kotlin.String  
isNullOrEmpty 
kotlin.String  let 
kotlin.String  lowercaseCompat 
kotlin.String  orEmpty 
kotlin.String  plus 
kotlin.String  
plusAssign 
kotlin.String  replace 
kotlin.String  split 
kotlin.String  
startsWith 
kotlin.String  to 
kotlin.String  	toBoolean 
kotlin.String  toBooleanStrictOrNullCompat 
kotlin.String  toIntOrNull 
kotlin.String  trim 
kotlin.String  
trimIndent 
kotlin.String  
component1 
kotlin.Triple  
component2 
kotlin.Triple  
component3 
kotlin.Triple  
Collection kotlin.collections  List kotlin.collections  Map kotlin.collections  MutableCollection kotlin.collections  MutableList kotlin.collections  
MutableSet kotlin.collections  Set kotlin.collections  any kotlin.collections  	associate kotlin.collections  
component1 kotlin.collections  
component2 kotlin.collections  contains kotlin.collections  	emptyList kotlin.collections  filter kotlin.collections  	filterNot kotlin.collections  forEach kotlin.collections  
isNotEmpty kotlin.collections  
isNullOrEmpty kotlin.collections  joinToString kotlin.collections  listOf kotlin.collections  
mapNotNull kotlin.collections  mapOf kotlin.collections  
mutableListOf kotlin.collections  none kotlin.collections  orEmpty kotlin.collections  
plusAssign kotlin.collections  toString kotlin.collections  toTypedArray kotlin.collections  filter kotlin.collections.Collection  
mapNotNull kotlin.collections.Collection  	associate kotlin.collections.List  filter kotlin.collections.List  	filterNot kotlin.collections.List  isEmpty kotlin.collections.List  
isNotEmpty kotlin.collections.List  joinToString kotlin.collections.List  Entry kotlin.collections.Map  entries kotlin.collections.Map  get kotlin.collections.Map  isEmpty kotlin.collections.Map  
isNotEmpty kotlin.collections.Map  values kotlin.collections.Map  
component1 kotlin.collections.Map.Entry  
component2 kotlin.collections.Map.Entry  add $kotlin.collections.MutableCollection  add kotlin.collections.MutableList  addAll kotlin.collections.MutableList  apply kotlin.collections.MutableList  
bundleCommand kotlin.collections.MutableList  bundleConfig kotlin.collections.MutableList  cliFile kotlin.collections.MutableList  cliPath kotlin.collections.MutableList  contains kotlin.collections.MutableList  
devEnabled kotlin.collections.MutableList  	entryFile kotlin.collections.MutableList  extraPackagerArgs kotlin.collections.MutableList  
minifyEnabled kotlin.collections.MutableList  nodeExecutableAndArgs kotlin.collections.MutableList  none kotlin.collections.MutableList  resourcesDir kotlin.collections.MutableList  to kotlin.collections.MutableList  get kotlin.collections.MutableMap  addAll kotlin.collections.MutableSet  joinToString kotlin.collections.Set  bufferedReader 	kotlin.io  copyTo 	kotlin.io  deleteRecursively 	kotlin.io  inputStream 	kotlin.io  readText 	kotlin.io  
startsWith 	kotlin.io  use 	kotlin.io  	writeText 	kotlin.io  JvmName 
kotlin.jvm  java 
kotlin.jvm  contains 
kotlin.ranges  
KProperty1 kotlin.reflect  java kotlin.reflect.KClass  Sequence kotlin.sequences  any kotlin.sequences  	associate kotlin.sequences  contains kotlin.sequences  filter kotlin.sequences  	filterNot kotlin.sequences  forEach kotlin.sequences  joinToString kotlin.sequences  
mapNotNull kotlin.sequences  none kotlin.sequences  orEmpty kotlin.sequences  exitProcess 
kotlin.system  MatchResult kotlin.text  Regex kotlin.text  any kotlin.text  	associate kotlin.text  contains kotlin.text  equals kotlin.text  filter kotlin.text  	filterNot kotlin.text  forEach kotlin.text  isBlank kotlin.text  
isNotBlank kotlin.text  
isNotEmpty kotlin.text  
isNullOrBlank kotlin.text  
isNullOrEmpty kotlin.text  
mapNotNull kotlin.text  none kotlin.text  orEmpty kotlin.text  removeSuffix kotlin.text  replace kotlin.text  split kotlin.text  
startsWith kotlin.text  substringAfterLast kotlin.text  	toBoolean kotlin.text  toIntOrNull kotlin.text  toString kotlin.text  trim kotlin.text  
trimIndent kotlin.text  Destructured kotlin.text.MatchResult  destructured kotlin.text.MatchResult  
component1 $kotlin.text.MatchResult.Destructured  
component2 $kotlin.text.MatchResult.Destructured  
component3 $kotlin.text.MatchResult.Destructured  
ReplaceTokens org.apache.tools.ant.filters  Action org.gradle.api  DefaultTask org.gradle.api  JavaVersion org.gradle.api  NamedDomainObjectContainer org.gradle.api  Plugin org.gradle.api  Project org.gradle.api  Task org.gradle.api  <SAM-CONSTRUCTOR> org.gradle.api.Action  BUILD_SCRIPT_PATH org.gradle.api.DefaultTask  CMAKE_FILENAME org.gradle.api.DefaultTask  CMAKE_TEMPLATE org.gradle.api.DefaultTask  CODEGEN_LIB_PREFIX org.gradle.api.DefaultTask  COMPONENT_DESCRIPTOR_FILENAME org.gradle.api.DefaultTask  COMPONENT_INCLUDE_PATH org.gradle.api.DefaultTask  CPP_FILENAME org.gradle.api.DefaultTask  CPP_TEMPLATE org.gradle.api.DefaultTask  DuplicatesStrategy org.gradle.api.DefaultTask  File org.gradle.api.DefaultTask  FileOutputStream org.gradle.api.DefaultTask  GENERATED_FILENAME org.gradle.api.DefaultTask  
H_FILENAME org.gradle.api.DefaultTask  	JsonUtils org.gradle.api.DefaultTask  Regex org.gradle.api.DefaultTask  RegularFile org.gradle.api.DefaultTask  
ReplaceTokens org.gradle.api.DefaultTask  String org.gradle.api.DefaultTask  apply org.gradle.api.DefaultTask  	associate org.gradle.api.DefaultTask  boostVersion org.gradle.api.DefaultTask  
bundleCommand org.gradle.api.DefaultTask  bundleConfig org.gradle.api.DefaultTask  checkNotNull org.gradle.api.DefaultTask  cliFile org.gradle.api.DefaultTask  cliPath org.gradle.api.DefaultTask  
component1 org.gradle.api.DefaultTask  
component2 org.gradle.api.DefaultTask  deleteRecursively org.gradle.api.DefaultTask  	dependsOn org.gradle.api.DefaultTask  detectOSAwareHermesCommand org.gradle.api.DefaultTask  
devEnabled org.gradle.api.DefaultTask  	emptyList org.gradle.api.DefaultTask  	entryFile org.gradle.api.DefaultTask  error org.gradle.api.DefaultTask  extraPackagerArgs org.gradle.api.DefaultTask  filter org.gradle.api.DefaultTask  	filterNot org.gradle.api.DefaultTask  fromAutolinkingConfigJson org.gradle.api.DefaultTask  fromPackageJson org.gradle.api.DefaultTask  generatedFileContentsTemplate org.gradle.api.DefaultTask  group org.gradle.api.DefaultTask  	hTemplate org.gradle.api.DefaultTask  interpolateDynamicValues org.gradle.api.DefaultTask  
isNotEmpty org.gradle.api.DefaultTask  java org.gradle.api.DefaultTask  joinToString org.gradle.api.DefaultTask  
mapNotNull org.gradle.api.DefaultTask  mapOf org.gradle.api.DefaultTask  
minifyEnabled org.gradle.api.DefaultTask  moveTo org.gradle.api.DefaultTask  
mutableListOf org.gradle.api.DefaultTask  nodeExecutableAndArgs org.gradle.api.DefaultTask  onlyIf org.gradle.api.DefaultTask  
plusAssign org.gradle.api.DefaultTask  project org.gradle.api.DefaultTask  removeSuffix org.gradle.api.DefaultTask  replace org.gradle.api.DefaultTask  requireNotNull org.gradle.api.DefaultTask  resourcesDir org.gradle.api.DefaultTask  sanitizeCmakeListsPath org.gradle.api.DefaultTask  to org.gradle.api.DefaultTask  toTypedArray org.gradle.api.DefaultTask  
trimIndent org.gradle.api.DefaultTask  unixifyPath org.gradle.api.DefaultTask  windowsAwareBashCommandLine org.gradle.api.DefaultTask  windowsAwareCommandLine org.gradle.api.DefaultTask  	writeText org.gradle.api.DefaultTask  
VERSION_17 org.gradle.api.JavaVersion  majorVersion org.gradle.api.JavaVersion  	getByName )org.gradle.api.NamedDomainObjectContainer  BundleHermesCTask org.gradle.api.Project  File org.gradle.api.Project  HERMES_ENABLED org.gradle.api.Project  HERMES_FALLBACK org.gradle.api.Project  INCLUDE_JITPACK_REPOSITORY org.gradle.api.Project  "INCLUDE_JITPACK_REPOSITORY_DEFAULT org.gradle.api.Project  NEW_ARCH_ENABLED org.gradle.api.Project  REACT_NATIVE_ARCHITECTURES org.gradle.api.Project  SCOPED_HERMES_ENABLED org.gradle.api.Project  !SCOPED_INCLUDE_JITPACK_REPOSITORY org.gradle.api.Project  SCOPED_NEW_ARCH_ENABLED org.gradle.api.Project  !SCOPED_REACT_NATIVE_ARCHITECTURES org.gradle.api.Project  SCOPED_USE_THIRD_PARTY_JSC org.gradle.api.Project  System org.gradle.api.Project  URI org.gradle.api.Project  USE_THIRD_PARTY_JSC org.gradle.api.Project  
afterEvaluate org.gradle.api.Project  allprojects org.gradle.api.Project  any org.gradle.api.Project  capitalizeCompat org.gradle.api.Project  configurations org.gradle.api.Project  !configureJsEnginePackagingOptions org.gradle.api.Project   configureNewArchPackagingOptions org.gradle.api.Project  configureReactTasks org.gradle.api.Project  dependencies org.gradle.api.Project  detectedCliFile org.gradle.api.Project  detectedEntryFile org.gradle.api.Project  equals org.gradle.api.Project  evaluationDependsOn org.gradle.api.Project  
extensions org.gradle.api.Project  file org.gradle.api.Project  fileTree org.gradle.api.Project  filter org.gradle.api.Project  findProperty org.gradle.api.Project  getReactNativeArchitectures org.gradle.api.Project  hasProperty org.gradle.api.Project  isHermesEnabled org.gradle.api.Project  isNewArchEnabled org.gradle.api.Project  
isNotBlank org.gradle.api.Project  
isNotEmpty org.gradle.api.Project  java org.gradle.api.Project  layout org.gradle.api.Project  logger org.gradle.api.Project  lowercaseCompat org.gradle.api.Project  
mutableListOf org.gradle.api.Project  name org.gradle.api.Project  needsCodegenFromPackageJson org.gradle.api.Project  objects org.gradle.api.Project  path org.gradle.api.Project  
pluginManager org.gradle.api.Project  project org.gradle.api.Project  
properties org.gradle.api.Project  property org.gradle.api.Project  readPackageJsonFile org.gradle.api.Project  repositories org.gradle.api.Project  rootProject org.gradle.api.Project  showJSCRemovalMessage org.gradle.api.Project  split org.gradle.api.Project  subprojects org.gradle.api.Project  tasks org.gradle.api.Project  	toBoolean org.gradle.api.Project  toBooleanStrictOrNullCompat org.gradle.api.Project  toString org.gradle.api.Project  useThirdPartyJSC org.gradle.api.Project  
Dependency org.gradle.api.artifacts  resolutionStrategy &org.gradle.api.artifacts.Configuration  all /org.gradle.api.artifacts.ConfigurationContainer  module 0org.gradle.api.artifacts.DependencySubstitutions  
substitute 0org.gradle.api.artifacts.DependencySubstitutions  because =org.gradle.api.artifacts.DependencySubstitutions.Substitution  using =org.gradle.api.artifacts.DependencySubstitutions.Substitution  dependencySubstitution +org.gradle.api.artifacts.ResolutionStrategy  force +org.gradle.api.artifacts.ResolutionStrategy  add .org.gradle.api.artifacts.dsl.DependencyHandler  project .org.gradle.api.artifacts.dsl.DependencyHandler  google .org.gradle.api.artifacts.dsl.RepositoryHandler  maven .org.gradle.api.artifacts.dsl.RepositoryHandler  mavenCentral .org.gradle.api.artifacts.dsl.RepositoryHandler  MavenArtifactRepository %org.gradle.api.artifacts.repositories  content 8org.gradle.api.artifacts.repositories.ArtifactRepository  content =org.gradle.api.artifacts.repositories.MavenArtifactRepository  url =org.gradle.api.artifacts.repositories.MavenArtifactRepository  excludeGroup Aorg.gradle.api.artifacts.repositories.RepositoryContentDescriptor  ConfigurableFileCollection org.gradle.api.file  ConfigurableFileTree org.gradle.api.file  	Directory org.gradle.api.file  DirectoryProperty org.gradle.api.file  DuplicatesStrategy org.gradle.api.file  FileSystemOperations org.gradle.api.file  FileTree org.gradle.api.file  RegularFile org.gradle.api.file  RegularFileProperty org.gradle.api.file  filter %org.gradle.api.file.ContentFilterable  duplicatesStrategy org.gradle.api.file.CopySpec  eachFile org.gradle.api.file.CopySpec  exclude org.gradle.api.file.CopySpec  
filesMatching org.gradle.api.file.CopySpec  from org.gradle.api.file.CopySpec  include org.gradle.api.file.CopySpec  includeEmptyDirs org.gradle.api.file.CopySpec  into org.gradle.api.file.CopySpec  asFile org.gradle.api.file.Directory  dir org.gradle.api.file.Directory  asFile %org.gradle.api.file.DirectoryProperty  
convention %org.gradle.api.file.DirectoryProperty  dir %org.gradle.api.file.DirectoryProperty  file %org.gradle.api.file.DirectoryProperty  get %org.gradle.api.file.DirectoryProperty  set %org.gradle.api.file.DirectoryProperty  INCLUDE &org.gradle.api.file.DuplicatesStrategy  name #org.gradle.api.file.FileCopyDetails  path #org.gradle.api.file.FileCopyDetails  asFile .org.gradle.api.file.FileSystemLocationProperty  copy (org.gradle.api.file.FileSystemOperations  buildDirectory !org.gradle.api.file.ProjectLayout  projectDirectory !org.gradle.api.file.ProjectLayout  asFile org.gradle.api.file.RegularFile  asFile 'org.gradle.api.file.RegularFileProperty  
convention 'org.gradle.api.file.RegularFileProperty  get 'org.gradle.api.file.RegularFileProperty  	isPresent 'org.gradle.api.file.RegularFileProperty  orNull 'org.gradle.api.file.RegularFileProperty  set 'org.gradle.api.file.RegularFileProperty  BUILD_SCRIPT_PATH $org.gradle.api.internal.AbstractTask  CMAKE_FILENAME $org.gradle.api.internal.AbstractTask  CMAKE_TEMPLATE $org.gradle.api.internal.AbstractTask  CODEGEN_LIB_PREFIX $org.gradle.api.internal.AbstractTask  COMPONENT_DESCRIPTOR_FILENAME $org.gradle.api.internal.AbstractTask  COMPONENT_INCLUDE_PATH $org.gradle.api.internal.AbstractTask  CPP_FILENAME $org.gradle.api.internal.AbstractTask  CPP_TEMPLATE $org.gradle.api.internal.AbstractTask  DuplicatesStrategy $org.gradle.api.internal.AbstractTask  File $org.gradle.api.internal.AbstractTask  FileOutputStream $org.gradle.api.internal.AbstractTask  GENERATED_FILENAME $org.gradle.api.internal.AbstractTask  
H_FILENAME $org.gradle.api.internal.AbstractTask  	JsonUtils $org.gradle.api.internal.AbstractTask  Regex $org.gradle.api.internal.AbstractTask  RegularFile $org.gradle.api.internal.AbstractTask  
ReplaceTokens $org.gradle.api.internal.AbstractTask  String $org.gradle.api.internal.AbstractTask  apply $org.gradle.api.internal.AbstractTask  	associate $org.gradle.api.internal.AbstractTask  boostVersion $org.gradle.api.internal.AbstractTask  
bundleCommand $org.gradle.api.internal.AbstractTask  bundleConfig $org.gradle.api.internal.AbstractTask  checkNotNull $org.gradle.api.internal.AbstractTask  cliFile $org.gradle.api.internal.AbstractTask  cliPath $org.gradle.api.internal.AbstractTask  
component1 $org.gradle.api.internal.AbstractTask  
component2 $org.gradle.api.internal.AbstractTask  deleteRecursively $org.gradle.api.internal.AbstractTask  detectOSAwareHermesCommand $org.gradle.api.internal.AbstractTask  
devEnabled $org.gradle.api.internal.AbstractTask  	emptyList $org.gradle.api.internal.AbstractTask  	entryFile $org.gradle.api.internal.AbstractTask  error $org.gradle.api.internal.AbstractTask  extraPackagerArgs $org.gradle.api.internal.AbstractTask  filter $org.gradle.api.internal.AbstractTask  	filterNot $org.gradle.api.internal.AbstractTask  fromAutolinkingConfigJson $org.gradle.api.internal.AbstractTask  fromPackageJson $org.gradle.api.internal.AbstractTask  generatedFileContentsTemplate $org.gradle.api.internal.AbstractTask  group $org.gradle.api.internal.AbstractTask  	hTemplate $org.gradle.api.internal.AbstractTask  interpolateDynamicValues $org.gradle.api.internal.AbstractTask  
isNotEmpty $org.gradle.api.internal.AbstractTask  java $org.gradle.api.internal.AbstractTask  joinToString $org.gradle.api.internal.AbstractTask  
mapNotNull $org.gradle.api.internal.AbstractTask  mapOf $org.gradle.api.internal.AbstractTask  
minifyEnabled $org.gradle.api.internal.AbstractTask  moveTo $org.gradle.api.internal.AbstractTask  
mutableListOf $org.gradle.api.internal.AbstractTask  nodeExecutableAndArgs $org.gradle.api.internal.AbstractTask  
plusAssign $org.gradle.api.internal.AbstractTask  project $org.gradle.api.internal.AbstractTask  removeSuffix $org.gradle.api.internal.AbstractTask  replace $org.gradle.api.internal.AbstractTask  requireNotNull $org.gradle.api.internal.AbstractTask  resourcesDir $org.gradle.api.internal.AbstractTask  sanitizeCmakeListsPath $org.gradle.api.internal.AbstractTask  to $org.gradle.api.internal.AbstractTask  toTypedArray $org.gradle.api.internal.AbstractTask  
trimIndent $org.gradle.api.internal.AbstractTask  unixifyPath $org.gradle.api.internal.AbstractTask  windowsAwareBashCommandLine $org.gradle.api.internal.AbstractTask  windowsAwareCommandLine $org.gradle.api.internal.AbstractTask  	writeText $org.gradle.api.internal.AbstractTask  BUILD_SCRIPT_PATH &org.gradle.api.internal.ConventionTask  File &org.gradle.api.internal.ConventionTask  FileOutputStream &org.gradle.api.internal.ConventionTask  	JsonUtils &org.gradle.api.internal.ConventionTask  apply &org.gradle.api.internal.ConventionTask  cliPath &org.gradle.api.internal.ConventionTask  deleteRecursively &org.gradle.api.internal.ConventionTask  fromPackageJson &org.gradle.api.internal.ConventionTask  to &org.gradle.api.internal.ConventionTask  toTypedArray &org.gradle.api.internal.ConventionTask  unixifyPath &org.gradle.api.internal.ConventionTask  windowsAwareBashCommandLine &org.gradle.api.internal.ConventionTask  windowsAwareCommandLine &org.gradle.api.internal.ConventionTask  directoryProperty "org.gradle.api.model.ObjectFactory  fileProperty "org.gradle.api.model.ObjectFactory  listProperty "org.gradle.api.model.ObjectFactory  property "org.gradle.api.model.ObjectFactory  
AppliedPlugin org.gradle.api.plugins  create )org.gradle.api.plugins.ExtensionContainer  extraProperties )org.gradle.api.plugins.ExtensionContainer  
findByType )org.gradle.api.plugins.ExtensionContainer  	getByType )org.gradle.api.plugins.ExtensionContainer  get /org.gradle.api.plugins.ExtraPropertiesExtension  has /org.gradle.api.plugins.ExtraPropertiesExtension  set /org.gradle.api.plugins.ExtraPropertiesExtension  
pluginManager "org.gradle.api.plugins.PluginAware  
withPlugin $org.gradle.api.plugins.PluginManager  ListProperty org.gradle.api.provider  Property org.gradle.api.provider  Provider org.gradle.api.provider  SetProperty org.gradle.api.provider  
convention $org.gradle.api.provider.ListProperty  get $org.gradle.api.provider.ListProperty  set $org.gradle.api.provider.ListProperty  
convention  org.gradle.api.provider.Property  get  org.gradle.api.provider.Property  	isPresent  org.gradle.api.provider.Property  orNull  org.gradle.api.provider.Property  set  org.gradle.api.provider.Property  get  org.gradle.api.provider.Provider  	isPresent  org.gradle.api.provider.Provider  orNull  org.gradle.api.provider.Provider  addAll #org.gradle.api.provider.SetProperty  Spec org.gradle.api.specs  <SAM-CONSTRUCTOR> org.gradle.api.specs.Spec  Any org.gradle.api.tasks  BUILD_SCRIPT_PATH org.gradle.api.tasks  Boolean org.gradle.api.tasks  ConfigurableFileCollection org.gradle.api.tasks  ConfigurableFileTree org.gradle.api.tasks  DefaultTask org.gradle.api.tasks  DirectoryProperty org.gradle.api.tasks  DuplicatesStrategy org.gradle.api.tasks  Exec org.gradle.api.tasks  ExecOperations org.gradle.api.tasks  File org.gradle.api.tasks  FileOutputStream org.gradle.api.tasks  FileSystemOperations org.gradle.api.tasks  FileTree org.gradle.api.tasks  Inject org.gradle.api.tasks  Input org.gradle.api.tasks  InputDirectory org.gradle.api.tasks  	InputFile org.gradle.api.tasks  
InputFiles org.gradle.api.tasks  Internal org.gradle.api.tasks  List org.gradle.api.tasks  ListProperty org.gradle.api.tasks  Optional org.gradle.api.tasks  OutputDirectory org.gradle.api.tasks  
OutputFile org.gradle.api.tasks  OutputFiles org.gradle.api.tasks  Property org.gradle.api.tasks  Provider org.gradle.api.tasks  RegularFile org.gradle.api.tasks  RegularFileProperty org.gradle.api.tasks  
ReplaceTokens org.gradle.api.tasks  String org.gradle.api.tasks  
TaskAction org.gradle.api.tasks  TaskProvider org.gradle.api.tasks  apply org.gradle.api.tasks  boostVersion org.gradle.api.tasks  
bundleCommand org.gradle.api.tasks  bundleConfig org.gradle.api.tasks  cliFile org.gradle.api.tasks  cliPath org.gradle.api.tasks  deleteRecursively org.gradle.api.tasks  detectOSAwareHermesCommand org.gradle.api.tasks  
devEnabled org.gradle.api.tasks  	entryFile org.gradle.api.tasks  extraPackagerArgs org.gradle.api.tasks  java org.gradle.api.tasks  mapOf org.gradle.api.tasks  
minifyEnabled org.gradle.api.tasks  moveTo org.gradle.api.tasks  
mutableListOf org.gradle.api.tasks  nodeExecutableAndArgs org.gradle.api.tasks  removeSuffix org.gradle.api.tasks  resourcesDir org.gradle.api.tasks  to org.gradle.api.tasks  toTypedArray org.gradle.api.tasks  unixifyPath org.gradle.api.tasks  windowsAwareBashCommandLine org.gradle.api.tasks  windowsAwareCommandLine org.gradle.api.tasks  BUILD_SCRIPT_PATH %org.gradle.api.tasks.AbstractExecTask  File %org.gradle.api.tasks.AbstractExecTask  FileOutputStream %org.gradle.api.tasks.AbstractExecTask  	JsonUtils %org.gradle.api.tasks.AbstractExecTask  apply %org.gradle.api.tasks.AbstractExecTask  cliPath %org.gradle.api.tasks.AbstractExecTask  deleteRecursively %org.gradle.api.tasks.AbstractExecTask  fromPackageJson %org.gradle.api.tasks.AbstractExecTask  to %org.gradle.api.tasks.AbstractExecTask  toTypedArray %org.gradle.api.tasks.AbstractExecTask  unixifyPath %org.gradle.api.tasks.AbstractExecTask  windowsAwareBashCommandLine %org.gradle.api.tasks.AbstractExecTask  windowsAwareCommandLine %org.gradle.api.tasks.AbstractExecTask  BUILD_SCRIPT_PATH org.gradle.api.tasks.Exec  File org.gradle.api.tasks.Exec  FileOutputStream org.gradle.api.tasks.Exec  	JsonUtils org.gradle.api.tasks.Exec  apply org.gradle.api.tasks.Exec  cliPath org.gradle.api.tasks.Exec  commandLine org.gradle.api.tasks.Exec  deleteRecursively org.gradle.api.tasks.Exec  errorOutput org.gradle.api.tasks.Exec  exec org.gradle.api.tasks.Exec  fromPackageJson org.gradle.api.tasks.Exec  standardOutput org.gradle.api.tasks.Exec  to org.gradle.api.tasks.Exec  toTypedArray org.gradle.api.tasks.Exec  unixifyPath org.gradle.api.tasks.Exec  windowsAwareBashCommandLine org.gradle.api.tasks.Exec  windowsAwareCommandLine org.gradle.api.tasks.Exec  named "org.gradle.api.tasks.TaskContainer  register "org.gradle.api.tasks.TaskContainer  exclude +org.gradle.api.tasks.util.PatternFilterable  include +org.gradle.api.tasks.util.PatternFilterable  Jvm org.gradle.internal.jvm  current org.gradle.internal.jvm.Jvm  javaVersion org.gradle.internal.jvm.Jvm  ExecOperations org.gradle.process  exec !org.gradle.process.ExecOperations  commandLine org.gradle.process.ExecSpec  
workingDir %org.gradle.process.ProcessForkOptions  KotlinProjectExtension org.jetbrains.kotlin.gradle.dsl  kotlinExtension org.jetbrains.kotlin.gradle.dsl  jvmToolchain 6org.jetbrains.kotlin.gradle.dsl.KotlinProjectExtension  jvmToolchain 7org.jetbrains.kotlin.gradle.dsl.KotlinTopLevelExtension  error org.slf4j.Logger  warn org.slf4j.Logger  Element org.w3c.dom  getElementsByTagName org.w3c.dom.Document  getAttribute org.w3c.dom.Element  item org.w3c.dom.NodeList                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            