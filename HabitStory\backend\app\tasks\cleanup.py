"""
Background tasks for cleanup and maintenance.
"""

import asyncio
import logging
from datetime import datetime, timedelta
from sqlalchemy.ext.asyncio import AsyncSession

from ..database import get_db_session
from ..services.auth import auth_service
from ..models.auth import LoginAttempt
from ..config import settings

logger = logging.getLogger(__name__)


async def cleanup_expired_tokens():
    """Clean up expired refresh tokens."""
    try:
        async with get_db_session() as db:
            await auth_service.cleanup_expired_tokens(db)
        logger.info("Token cleanup completed successfully")
    except Exception as e:
        logger.error(f"Token cleanup failed: {e}")


async def cleanup_old_login_attempts():
    """Clean up old login attempts (keep only last 30 days)."""
    try:
        async with get_db_session() as db:
            cutoff_date = datetime.utcnow() - timedelta(days=30)
            
            # Delete old login attempts
            result = await db.execute(
                LoginAttempt.__table__.delete().where(
                    LoginAttempt.attempted_at < cutoff_date
                )
            )
            await db.commit()
            
            deleted_count = result.rowcount
            logger.info(f"Cleaned up {deleted_count} old login attempts")
            
    except Exception as e:
        logger.error(f"Login attempts cleanup failed: {e}")


async def run_periodic_cleanup():
    """Run periodic cleanup tasks."""
    logger.info("Starting periodic cleanup tasks")
    
    while True:
        try:
            # Run cleanup tasks
            await cleanup_expired_tokens()
            await cleanup_old_login_attempts()
            
            # Wait 1 hour before next cleanup
            await asyncio.sleep(3600)
            
        except Exception as e:
            logger.error(f"Periodic cleanup error: {e}")
            # Wait 5 minutes before retrying
            await asyncio.sleep(300)


def start_background_tasks():
    """Start background cleanup tasks."""
    if settings.environment == "production":
        # Only run in production to avoid interference during development
        asyncio.create_task(run_periodic_cleanup())
        logger.info("Background cleanup tasks started")
    else:
        logger.info("Background tasks disabled in development mode")
