{"version": 3, "file": "TokenEmitter.js", "sourceRoot": "", "sources": ["../src/TokenEmitter.ts"], "names": [], "mappings": "AAAA,OAAO,EAA0B,QAAQ,EAAE,MAAM,mBAAmB,CAAC;AAErE,OAAO,gBAAgB,MAAM,oBAAoB,CAAC;AAElD,OAAO,EAAE,qBAAqB,EAAE,MAAM,yBAAyB,CAAC;AAShE,iCAAiC;AACjC,MAAM,iBAAiB,GAAG,mBAAmB,CAAC;AAE9C;;;;;;;;;;;;;;;;;;;;;;;;;GAyBG;AACH,MAAM,UAAU,oBAAoB,CAAC,QAA2B;IAC9D,qBAAqB,EAAE,CAAC;IACxB,OAAO,gBAAgB,CAAC,WAAW,CAAC,iBAAiB,EAAE,CAAC,EAAE,eAAe,EAAE,EAAE,EAAE,CAC7E,QAAQ,CAAC,EAAE,IAAI,EAAE,eAAe,EAAE,IAAI,EAAE,QAAQ,CAAC,EAAE,EAAE,CAAC,CACvD,CAAC;AACJ,CAAC;AAED;;;;;;GAMG;AACH,MAAM,UAAU,2BAA2B,CAAC,YAA+B;IACzE,OAAO,CAAC,IAAI,CACV,oFAAoF,CACrF,CAAC;IACF,YAAY,CAAC,MAAM,EAAE,CAAC;AACxB,CAAC", "sourcesContent": ["import { type EventSubscription, Platform } from 'expo-modules-core';\n\nimport PushTokenManager from './PushTokenManager';\nimport { DevicePushToken } from './Tokens.types';\nimport { warnOfExpoGoPushUsage } from './warnOfExpoGoPushUsage';\n\n/**\n * A function accepting a device push token ([`DevicePushToken`](#devicepushtoken)) as an argument.\n * > **Note:** You should not call `getDevicePushTokenAsync` inside this function, as it triggers the listener and may lead to an infinite loop.\n * @header fetch\n */\nexport type PushTokenListener = (token: DevicePushToken) => void;\n\n// Web uses SyntheticEventEmitter\nconst newTokenEventName = 'onDevicePushToken';\n\n/**\n * In rare situations, a push token may be changed by the push notification service while the app is running.\n * When a token is rolled, the old one becomes invalid and sending notifications to it will fail.\n * A push token listener will let you handle this situation gracefully by registering the new token with your backend right away.\n * @param listener A function accepting a push token as an argument, it will be called whenever the push token changes.\n * @return An [`EventSubscription`](#eventsubscription) object represents the subscription of the provided listener.\n * @header fetch\n * @example Registering a push token listener using a React hook.\n * ```jsx\n * import React from 'react';\n * import * as Notifications from 'expo-notifications';\n *\n * import { registerDevicePushTokenAsync } from '../api';\n *\n * export default function App() {\n *   React.useEffect(() => {\n *     const subscription = Notifications.addPushTokenListener(registerDevicePushTokenAsync);\n *     return () => subscription.remove();\n *   }, []);\n *\n *   return (\n *     // Your app content\n *   );\n * }\n * ```\n */\nexport function addPushTokenListener(listener: PushTokenListener): EventSubscription {\n  warnOfExpoGoPushUsage();\n  return PushTokenManager.addListener(newTokenEventName, ({ devicePushToken }) =>\n    listener({ data: devicePushToken, type: Platform.OS })\n  );\n}\n\n/**\n * @deprecated call `remove()` on the subscription object instead.\n *\n * Removes a push token subscription returned by an `addPushTokenListener` call.\n * @param subscription A subscription returned by `addPushTokenListener` method.\n * @header fetch\n */\nexport function removePushTokenSubscription(subscription: EventSubscription) {\n  console.warn(\n    '`removePushTokenSubscription` is deprecated. Call `subscription.remove()` instead.'\n  );\n  subscription.remove();\n}\n"]}