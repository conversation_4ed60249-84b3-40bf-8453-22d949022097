# 🚀 Guía de Ejecución de HabitStory

## 📋 Requisitos Previos

### Para Backend:
- Python 3.11+
- pip (gestor de paquetes de Python)

### Para Frontend Móvil:
- Node.js 18+
- npm o yarn
- Expo CLI (`npm install -g @expo/cli`)
- Expo Go app en tu móvil (disponible en App Store/Google Play)

### Alternativa con Docker:
- Docker Desktop
- Docker Compose

---

## 🐳 Opción 1: Ejecución con Docker (Recomendado)

### 1. Abrir terminal en el directorio del proyecto
```bash
cd HabitStory
```

### 2. Construir y ejecutar con Docker Compose
```bash
docker-compose up --build
```

### 3. Verificar que los servicios estén corriendo
- **Backend API**: http://localhost:8000
- **Documentación API**: http://localhost:8000/docs
- **Health Check**: http://localhost:8000/health

---

## 🔧 Opción 2: Ejecución Manual

### Backend (Terminal 1)

1. **Navegar al directorio del backend**
```bash
cd HabitStory/backend
```

2. **<PERSON>rear entorno virtual (recomendado)**
```bash
python -m venv venv
# Windows:
venv\Scripts\activate
# macOS/Linux:
source venv/bin/activate
```

3. **Instalar dependencias**
```bash
pip install -r requirements.txt
```

4. **Ejecutar migraciones de base de datos**
```bash
alembic upgrade head
```

5. **Iniciar el servidor**
```bash
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
```

### Frontend Móvil (Terminal 2)

1. **Navegar al directorio principal**
```bash
cd HabitStory
```

2. **Instalar dependencias**
```bash
npm install
```

3. **Iniciar Expo**
```bash
npx expo start
```

4. **Conectar desde tu móvil**
- Instala "Expo Go" en tu móvil
- Escanea el código QR que aparece en la terminal
- O abre la URL que aparece en tu navegador móvil

---

## 📱 Cómo Probar desde el Móvil

### 1. **Verificar Backend**
Abre en tu navegador móvil:
- `http://[TU_IP]:8000/health` - Debe mostrar `{"status": "healthy"}`
- `http://[TU_IP]:8000/docs` - Documentación interactiva de la API

### 2. **Abrir la App Móvil**
- Escanea el QR de Expo con la app "Expo Go"
- La app debería cargar automáticamente

### 3. **Probar Funcionalidades**

#### ✅ **Escribir una Entrada de Journal**
1. Toca el botón "+" para nueva entrada
2. Escribe algo como:
   ```
   Hoy fue un gran día! Corrí 5km por la mañana y me sentí increíble. 
   Medité 15 minutos y mi estado de ánimo está excelente. 
   Dormí 8 horas y tengo mucha energía.
   ```
3. Toca "Guardar"
4. Deberías recibir feedback inmediato de la IA

#### ✅ **Generar Reporte Semanal**
1. Después de tener varias entradas
2. Ve a la sección "Reportes"
3. Toca "Generar Reporte Semanal"
4. Espera a que se procese (2-5 segundos)
5. Verás un reporte HTML hermoso con:
   - Tu historia personalizada de la semana
   - Gráficos de tus métricas
   - Recomendaciones específicas
   - Preguntas de reflexión

#### ✅ **Dar Feedback**
1. En cualquier reporte, toca "Dar Feedback"
2. Califica del 1-5 y deja comentarios
3. Esto mejora las futuras recomendaciones

---

## 🔍 URLs de Prueba

### Backend API Endpoints:
```
GET  http://localhost:8000/health
GET  http://localhost:8000/docs
POST http://localhost:8000/api/v1/entries/parse
POST http://localhost:8000/api/v1/reports/weekly
POST http://localhost:8000/api/v1/feedback
```

### Ejemplo de Prueba Manual de API:
```bash
# Probar health check
curl http://localhost:8000/health

# Probar parsing de entrada
curl -X POST "http://localhost:8000/api/v1/entries/parse" \
  -H "Content-Type: application/json" \
  -d '{
    "text": "Hoy corrí 5km y medité 15 minutos. Me siento genial!",
    "user_id": "test_user",
    "user_traits": {"tone": "informal", "style": "optimistic"}
  }'
```

---

## 🐛 Solución de Problemas

### Backend no inicia:
1. **Verificar Python**: `python --version` (debe ser 3.11+)
2. **Verificar dependencias**: `pip list`
3. **Reinstalar**: `pip install -r requirements.txt --force-reinstall`
4. **Verificar puerto**: Asegúrate de que el puerto 8000 esté libre

### Frontend no inicia:
1. **Verificar Node.js**: `node --version` (debe ser 18+)
2. **Limpiar cache**: `npm cache clean --force`
3. **Reinstalar**: `rm -rf node_modules && npm install`
4. **Verificar Expo**: `npx expo --version`

### No se conecta desde móvil:
1. **Verificar red**: Móvil y PC deben estar en la misma red WiFi
2. **Verificar firewall**: Permitir conexiones en puertos 8000 y 8081
3. **Usar IP específica**: Reemplaza `localhost` con la IP de tu PC

### Errores de API:
1. **Verificar API Key**: Asegúrate de que la Gemini API key esté configurada
2. **Verificar logs**: Revisa la consola del backend para errores
3. **Verificar conectividad**: Prueba conexión a internet

---

## 📊 Datos de Prueba Sugeridos

### Entradas de Journal para Probar:
```
Día 1: "Excelente inicio de semana! Corrí 5km, medité 20 minutos y desayuné saludable. Energía nivel 9/10."

Día 2: "Día productivo en el trabajo. Hice yoga 30 minutos y cociné una cena nutritiva. Durmí 7 horas."

Día 3: "Día difícil, solo dormí 5 horas. Pero mantuve mi rutina de ejercicio con 30 min de caminata."

Día 4: "¡Recuperado! 8 horas de sueño, ejercicio intenso 45 min, y excelente estado de ánimo."

Día 5: "Fin de semana relajante. Medité 25 minutos, leí un libro y pasé tiempo con familia."
```

---

## 🎯 Funcionalidades a Probar

### ✅ **Parsing Inteligente**
- Escribe entradas variadas y observa cómo la IA extrae hábitos y métricas

### ✅ **Personalización**
- Nota cómo el tono de las respuestas se adapta a tu estilo de escritura

### ✅ **Análisis de Patrones**
- Después de varias entradas, genera un reporte para ver correlaciones

### ✅ **Visualizaciones**
- Los reportes incluyen gráficos hermosos de tus tendencias

### ✅ **Recomendaciones**
- Recibe consejos específicos basados en tus datos reales

---

## 🚀 ¡Listo para Probar!

Una vez que tengas todo ejecutándose:

1. **Backend corriendo en**: http://localhost:8000
2. **App móvil**: Escaneando QR de Expo
3. **Documentación**: http://localhost:8000/docs

¡Disfruta probando tu aplicación de crecimiento personal con IA! 🌱✨
