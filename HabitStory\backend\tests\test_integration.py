"""
Integration tests for the complete HabitStory backend system.
Tests the full pipeline from entry parsing to report generation.
"""

import pytest
import asyncio
from httpx import AsyncClient
from unittest.mock import patch, AsyncMock
import json

from app.main import app


class TestFullPipelineIntegration:
    """Test the complete pipeline integration."""
    
    @pytest.mark.asyncio
    async def test_complete_weekly_report_generation(self, client: AsyncClient):
        """Test the complete weekly report generation flow."""
        
        # Sample journal entries for a week
        sample_entries = [
            {
                "text": "Great start to the week! Ran 5k this morning and felt amazing. Had a healthy breakfast and meditated for 15 minutes. Work was productive and I'm feeling optimistic about the week ahead.",
                "date": "2024-01-15T08:00:00Z",
                "user_id": "test_user_integration"
            },
            {
                "text": "Busy day at work but managed to stick to my habits. Did a 30-minute yoga session and cooked a healthy dinner. Feeling grateful for the small wins.",
                "date": "2024-01-16T19:00:00Z", 
                "user_id": "test_user_integration"
            },
            {
                "text": "Struggled with sleep last night, only got 5 hours. Feeling tired but pushed through with a light workout. Need to prioritize sleep better.",
                "date": "2024-01-17T10:00:00Z",
                "user_id": "test_user_integration"
            },
            {
                "text": "Much better day! Got 8 hours of sleep and had energy for a proper workout. Mood is significantly better. Sleep really makes a difference.",
                "date": "2024-01-18T09:00:00Z",
                "user_id": "test_user_integration"
            },
            {
                "text": "Productive Friday! Completed all my weekly goals and celebrated with friends. Feeling accomplished and ready for the weekend.",
                "date": "2024-01-19T18:00:00Z",
                "user_id": "test_user_integration"
            }
        ]
        
        user_traits = {
            "tone": "informal",
            "style": "optimistic",
            "traits": {
                "health_conscious": True,
                "goal_oriented": True,
                "social": True
            }
        }
        
        # Mock Gemini responses for all modules
        with patch('app.services.gemini_client.gemini_client.call_gemini') as mock_gemini:
            # Set up different responses for different modules
            mock_responses = [
                # Parsing responses
                {
                    "response": json.dumps({
                        "name": "extract_entry_data",
                        "args": {
                            "habits": ["running", "meditation", "healthy_eating"],
                            "qualitative_habits": [
                                {"name": "morning_run", "category": "health", "completed": True, "confidence": 0.9}
                            ],
                            "metrics": {"exercise_minutes": 45, "mood_score": 8, "sleep_hours": 7},
                            "reflection": "Great start to the week with positive energy",
                            "user_traits": user_traits
                        }
                    }),
                    "type": "function_call"
                },
                # Storytelling response
                {
                    "response": "This week has been an incredible journey of self-discovery and growth! You started strong with that amazing 5k run, showing your commitment to health and wellness. Even when sleep became a challenge mid-week, you demonstrated resilience and self-awareness. The way you bounced back with better sleep and energy shows you're learning what works for your body. Ending the week by celebrating your accomplishments with friends perfectly captures your balanced approach to life. You're building sustainable habits while staying connected to what matters most!",
                    "type": "text"
                },
                # Recommendations response
                {
                    "response": json.dumps({
                        "name": "generate_recommendations", 
                        "args": {
                            "recommendations": [
                                {
                                    "category": "wellness",
                                    "title": "Master Your Sleep Game",
                                    "description": "You've seen firsthand how sleep affects everything else - let's make great sleep your superpower!",
                                    "priority": "high",
                                    "actionable_steps": [
                                        "Set a consistent bedtime routine",
                                        "Create a wind-down ritual 30 minutes before bed",
                                        "Track your sleep patterns for one week"
                                    ],
                                    "reasoning": "Your data shows sleep directly impacts mood and energy",
                                    "difficulty": "moderate",
                                    "timeframe": "this week"
                                }
                            ]
                        }
                    }),
                    "type": "function_call"
                },
                # Questions response
                {
                    "response": json.dumps({
                        "name": "generate_questions",
                        "args": {
                            "questions": [
                                {
                                    "category": "reflection",
                                    "question": "What specific factors help you get your best night's sleep?",
                                    "context": "Based on your sleep-mood connection",
                                    "purpose": "Identify optimal sleep conditions",
                                    "priority": "high",
                                    "follow_up_prompts": ["What disrupts your sleep?", "How do you feel after great sleep?"]
                                }
                            ]
                        }
                    }),
                    "type": "function_call"
                },
                # Validation response
                {
                    "response": "[]",  # No inconsistencies found
                    "type": "text"
                },
                # Report builder response
                {
                    "response": """<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Your Weekly Journey</title>
    <style>
        body { font-family: system-ui, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .header { text-align: center; margin-bottom: 30px; }
        .story { background: #f8fafc; padding: 20px; border-radius: 8px; margin: 20px 0; }
        .recommendations { margin: 20px 0; }
        .recommendation { border: 1px solid #e5e7eb; padding: 15px; margin: 10px 0; border-radius: 6px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Your Weekly Journey</h1>
        <p>January 15-19, 2024</p>
    </div>
    <div class="story">
        <h2>Your Story This Week</h2>
        <p>This week has been an incredible journey of self-discovery and growth!</p>
    </div>
    <div class="recommendations">
        <h2>Recommendations for Growth</h2>
        <div class="recommendation">
            <h3>Master Your Sleep Game</h3>
            <p>You've seen firsthand how sleep affects everything else - let's make great sleep your superpower!</p>
        </div>
    </div>
</body>
</html>""",
                    "type": "text"
                }
            ]
            
            # Cycle through responses for different calls
            mock_gemini.side_effect = mock_responses * 10  # Repeat as needed
            
            # Test the complete weekly report generation
            request_data = {
                "user_id": "test_user_integration",
                "entries": sample_entries,
                "week_start": "2024-01-15",
                "week_end": "2024-01-19",
                "user_traits": user_traits
            }
            
            response = await client.post("/api/v1/reports/weekly", json=request_data)
            
            # Assertions
            assert response.status_code == 200
            data = response.json()
            
            assert data["success"] is True
            assert "html_content" in data
            assert "generation_time" in data
            assert "metadata" in data
            
            # Verify HTML content
            html_content = data["html_content"]
            assert "<!DOCTYPE html>" in html_content
            assert "Your Weekly Journey" in html_content
            assert "incredible journey" in html_content
            
            # Verify metadata
            metadata = data["metadata"]
            assert "modules_completed" in metadata
            assert metadata["entries_processed"] == 5
    
    @pytest.mark.asyncio
    async def test_entry_parsing_endpoint(self, client: AsyncClient):
        """Test the entry parsing endpoint."""
        
        with patch('app.services.gemini_client.gemini_client.call_gemini') as mock_gemini:
            mock_gemini.return_value = {
                "response": json.dumps({
                    "name": "extract_entry_data",
                    "args": {
                        "habits": ["exercise", "meditation"],
                        "qualitative_habits": [
                            {"name": "morning_workout", "category": "health", "completed": True, "confidence": 0.9}
                        ],
                        "metrics": {"mood_score": 8, "energy_level": 9},
                        "reflection": "Great day with positive energy",
                        "user_traits": {"tone": "informal", "style": "optimistic"}
                    }
                }),
                "type": "function_call"
            }
            
            # Mock token check
            with patch('app.services.gemini_client.gemini_client.check_token_limit') as mock_tokens:
                mock_tokens.return_value = {
                    "limit": 10000,
                    "used": 100,
                    "remaining": 9900,
                    "percentage_used": 1.0
                }
                
                request_data = {
                    "text": "Had an amazing workout this morning! Feeling energized and ready for the day. Meditated for 20 minutes and my mood is great.",
                    "user_id": "test_user",
                    "user_traits": {"tone": "informal", "style": "optimistic"}
                }
                
                response = await client.post("/api/v1/entries/parse", json=request_data)
                
                assert response.status_code == 200
                data = response.json()
                
                assert data["success"] is True
                assert "parsed_data" in data
                assert "daily_feedback" in data
                
                # Check parsed data
                parsed_data = data["parsed_data"]
                assert "habits" in parsed_data
                assert "metrics" in parsed_data
                assert "reflection" in parsed_data
                
                # Check daily feedback
                feedback = data["daily_feedback"]
                assert "insights" in feedback
                assert "encouragement" in feedback
                assert "reflection_prompt" in feedback
    
    @pytest.mark.asyncio
    async def test_token_usage_endpoint(self, client: AsyncClient):
        """Test the token usage endpoint."""
        
        with patch('app.services.gemini_client.gemini_client.check_token_limit') as mock_tokens:
            mock_tokens.return_value = {
                "limit": 10000,
                "used": 2500,
                "remaining": 7500,
                "percentage_used": 25.0
            }
            
            response = await client.get("/api/v1/entries/test_user/token-usage")
            
            assert response.status_code == 200
            data = response.json()
            
            assert data["user_id"] == "test_user"
            assert data["current_usage"] == 2500
            assert data["monthly_limit"] == 10000
            assert data["remaining"] == 7500
            assert data["usage_percentage"] == 25.0
    
    @pytest.mark.asyncio
    async def test_health_check_endpoints(self, client: AsyncClient):
        """Test health check endpoints."""
        
        # Test main health endpoint
        response = await client.get("/health")
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
        
        # Test entries health endpoint
        with patch('app.services.gemini_client.gemini_client.call_gemini') as mock_gemini:
            mock_gemini.return_value = {
                "response": "OK",
                "type": "text"
            }
            
            response = await client.get("/api/v1/entries/health")
            assert response.status_code == 200
            data = response.json()
            assert data["status"] in ["healthy", "degraded"]
            assert "gemini_api" in data
            assert "timestamp" in data
    
    @pytest.mark.asyncio
    async def test_error_handling(self, client: AsyncClient):
        """Test error handling in API endpoints."""
        
        # Test with empty entry text
        response = await client.post("/api/v1/entries/parse", json={
            "text": "",
            "user_id": "test_user"
        })
        assert response.status_code == 400
        
        # Test with missing required fields
        response = await client.post("/api/v1/reports/weekly", json={
            "user_id": "test_user"
            # Missing entries
        })
        assert response.status_code == 400
        
        # Test token limit exceeded
        with patch('app.services.gemini_client.gemini_client.check_token_limit') as mock_tokens:
            mock_tokens.return_value = {
                "limit": 10000,
                "used": 10000,
                "remaining": 0,
                "percentage_used": 100.0
            }
            
            response = await client.post("/api/v1/entries/parse", json={
                "text": "Test entry",
                "user_id": "test_user"
            })
            assert response.status_code == 429  # Too Many Requests


class TestAPIDocumentation:
    """Test API documentation endpoints."""
    
    @pytest.mark.asyncio
    async def test_openapi_docs(self, client: AsyncClient):
        """Test that OpenAPI documentation is accessible."""
        
        # Test Swagger UI
        response = await client.get("/docs")
        assert response.status_code == 200
        
        # Test ReDoc
        response = await client.get("/redoc")
        assert response.status_code == 200
        
        # Test OpenAPI JSON
        response = await client.get("/openapi.json")
        assert response.status_code == 200
        data = response.json()
        assert "openapi" in data
        assert "info" in data
        assert data["info"]["title"] == "HabitStory API"


class TestCORSAndMiddleware:
    """Test CORS and middleware configuration."""
    
    @pytest.mark.asyncio
    async def test_cors_headers(self, client: AsyncClient):
        """Test CORS headers are properly set."""
        
        response = await client.options("/api/v1/entries/health")
        assert response.status_code == 200
        
        # Check CORS headers would be present in a real request
        # (Note: httpx test client doesn't fully simulate CORS)
        response = await client.get("/health")
        assert response.status_code == 200
