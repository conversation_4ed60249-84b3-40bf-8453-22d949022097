{"version": 3, "file": "NotificationPermissions.types.js", "sourceRoot": "", "sources": ["../src/NotificationPermissions.types.ts"], "names": [], "mappings": "AAEA,MAAM,CAAN,IAAY,aAIX;AAJD,WAAY,aAAa;IACvB,iDAAQ,CAAA;IACR,qDAAU,CAAA;IACV,mDAAS,CAAA;AACX,CAAC,EAJW,aAAa,KAAb,aAAa,QAIxB;AAED,MAAM,CAAN,IAAY,iBAIX;AAJD,WAAY,iBAAiB;IAC3B,2DAAS,CAAA;IACT,6DAAU,CAAA;IACV,qFAAsB,CAAA;AACxB,CAAC,EAJW,iBAAiB,KAAjB,iBAAiB,QAI5B;AAED,MAAM,CAAN,IAAY,sBAMX;AAND,WAAY,sBAAsB;IAChC,uFAAkB,CAAA;IAClB,uEAAU,CAAA;IACV,+EAAc,CAAA;IACd,iFAAe,CAAA;IACf,6EAAa,CAAA;AACf,CAAC,EANW,sBAAsB,KAAtB,sBAAsB,QAMjC", "sourcesContent": ["import { PermissionResponse } from 'expo-modules-core';\n\nexport enum IosAlertStyle {\n  NONE = 0,\n  BANNER = 1,\n  ALERT = 2,\n}\n\nexport enum IosAllowsPreviews {\n  NEVER = 0,\n  ALWAYS = 1,\n  WHEN_AUTHENTICATED = 2,\n}\n\nexport enum IosAuthorizationStatus {\n  NOT_DETERMINED = 0,\n  DENIED = 1,\n  AUTHORIZED = 2,\n  PROVISIONAL = 3,\n  EPHEMERAL = 4,\n}\n\n// @docsMissing\nexport interface NotificationPermissionsStatus extends PermissionResponse {\n  android?: {\n    importance: number;\n    interruptionFilter?: number;\n  };\n  ios?: {\n    status: IosAuthorizationStatus;\n    allowsDisplayInNotificationCenter: boolean | null;\n    allowsDisplayOnLockScreen: boolean | null;\n    allowsDisplayInCarPlay: boolean | null;\n    allowsAlert: boolean | null;\n    allowsBadge: boolean | null;\n    allowsSound: boolean | null;\n    allowsCriticalAlerts?: boolean | null;\n    alertStyle: IosAlertStyle;\n    allowsPreviews?: IosAllowsPreviews;\n    providesAppNotificationSettings?: boolean;\n    allowsAnnouncements?: boolean | null;\n  };\n}\n\n/**\n * Available configuration for permission request on iOS platform.\n * See Apple documentation for [`UNAuthorizationOptions`](https://developer.apple.com/documentation/usernotifications/unauthorizationoptions) to learn more.\n */\nexport interface IosNotificationPermissionsRequest {\n  /**\n   * The ability to display alerts.\n   */\n  allowAlert?: boolean;\n  /**\n   * The ability to update the app’s badge.\n   */\n  allowBadge?: boolean;\n  /**\n   * The ability to play sounds.\n   */\n  allowSound?: boolean;\n  /**\n   * The ability to display notifications in a CarPlay environment.\n   */\n  allowDisplayInCarPlay?: boolean;\n  /**\n   * The ability to play sounds for critical alerts.\n   */\n  allowCriticalAlerts?: boolean;\n  /**\n   * An option indicating the system should display a button for in-app notification settings.\n   */\n  provideAppNotificationSettings?: boolean;\n  /**\n   * The ability to post noninterrupting notifications provisionally to the Notification Center.\n   */\n  allowProvisional?: boolean;\n}\n\nexport type NativeNotificationPermissionsRequest = IosNotificationPermissionsRequest | object;\n\n/**\n * An interface representing the permissions request scope configuration.\n * Each option corresponds to a different native platform authorization option.\n */\nexport interface NotificationPermissionsRequest {\n  /**\n   * Available configuration for permission request on iOS platform.\n   */\n  ios?: IosNotificationPermissionsRequest;\n  /**\n   * On Android, all available permissions are granted by default, and if a user declines any permission, an app cannot prompt the user to change.\n   */\n  android?: object;\n}\n"]}