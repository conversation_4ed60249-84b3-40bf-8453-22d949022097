# 🚀 HabitStory - Inicio Rápido para Expo Go

## 📱 Pasos para Probar en tu Móvil

### 1. 📲 Instalar Expo Go en tu móvil
- **Android**: [Google Play Store](https://play.google.com/store/apps/details?id=host.exp.exponent)
- **iOS**: [App Store](https://apps.apple.com/app/expo-go/id982107779)

### 2. 🖥️ Abrir Terminal/PowerShell en tu PC
Navega al directorio del proyecto:
```bash
cd c:\_CARPETEr\__Projects\__Personal\AppTracker\MVP2\HabitStory
```

### 3. 📦 Instalar dependencias (solo la primera vez)
```bash
npm install
```

### 4. 🚀 Iniciar Expo
```bash
npm start
```
O alternativamente:
```bash
npx expo start
```

### 5. 📱 Conectar desde tu móvil
Cuando Expo se inicie, verás:
- Un **código QR** en la terminal
- Una **URL** como `exp://192.168.x.x:8081`

**Para conectar:**
- **Android**: Abre Expo Go → Escanea el código QR
- **iOS**: Abre la cámara → Escanea el código QR → Toca "Abrir en Expo Go"

---

## 🌐 Backend API (Opcional para pruebas completas)

Si quieres probar todas las funcionalidades con IA, también inicia el backend:

### Terminal 2 (Backend):
```bash
cd c:\_CARPETEr\__Projects\__Personal\AppTracker\MVP2\HabitStory\backend
pip install -r requirements.txt
python -m uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
```

**Verificar backend:**
- Abre en navegador: `http://localhost:8000/health`
- Documentación: `http://localhost:8000/docs`

---

## 🎯 Qué Probar en la App

### ✅ **Funcionalidades Básicas**
1. **Nueva Entrada**: Toca "+" y escribe una entrada de journal
2. **Ver Entradas**: Navega por tus entradas guardadas
3. **Configuración**: Ajusta preferencias de usuario

### ✅ **Funcionalidades con IA** (requiere backend)
1. **Parsing Inteligente**: Escribe entradas y recibe feedback de IA
2. **Reportes Semanales**: Genera reportes con análisis y visualizaciones
3. **Recomendaciones**: Recibe consejos personalizados

### 📝 **Ejemplos de Entradas para Probar**
```
"Hoy fue increíble! Corrí 5km por la mañana y me sentí súper energizado. 
Medité 15 minutos y mi estado de ánimo está en las nubes. 
Dormí 8 horas completas anoche."

"Día productivo en el trabajo. Completé 3 tareas importantes. 
Hice yoga 30 minutos después del trabajo y cociné una cena saludable. 
Nivel de estrés: 3/10."

"Día más tranquilo. Solo caminé 20 minutos pero fue relajante. 
Leí un capítulo de mi libro favorito y pasé tiempo de calidad con familia. 
Gratitud nivel máximo."
```

---

## 🔧 Solución de Problemas

### ❌ **"No se puede conectar"**
- Asegúrate de que PC y móvil estén en la misma red WiFi
- Verifica que el firewall no bloquee el puerto 8081
- Intenta usar la URL directa en lugar del QR

### ❌ **"Expo no inicia"**
```bash
# Limpiar cache
npx expo start --clear

# O reinstalar dependencias
rm -rf node_modules
npm install
npm start
```

### ❌ **"Backend no responde"**
- Verifica que esté corriendo en puerto 8000
- Revisa la configuración de la API key de Gemini
- Comprueba los logs en la terminal del backend

---

## 📱 URLs de Acceso Rápido

Una vez que todo esté corriendo:

### **Desde tu PC:**
- Backend Health: http://localhost:8000/health
- API Docs: http://localhost:8000/docs
- Expo DevTools: http://localhost:8081

### **Desde tu móvil:**
- Escanea el QR de Expo Go
- O usa la URL que aparece en la terminal

---

## 🎉 ¡Listo para Probar!

1. **Instala Expo Go** en tu móvil
2. **Ejecuta `npm start`** en la terminal
3. **Escanea el QR** con Expo Go
4. **¡Disfruta probando HabitStory!** 🌱

### 💡 Consejos:
- Mantén ambos dispositivos en la misma red WiFi
- Si hay problemas, reinicia Expo con `npx expo start --clear`
- Para funcionalidades completas de IA, asegúrate de que el backend esté corriendo

---

**¿Necesitas ayuda?** Revisa los logs en la terminal o consulta la documentación completa en `EXECUTION_GUIDE.md`
