@echo off
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                        🌱 HabitStory 🌱                        ║
echo ║              Iniciando para Expo Go...                      ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 📦 Instalando dependencias...
call npm install

echo.
echo 🚀 Iniciando Expo...
echo.
echo 📱 Instrucciones:
echo    1. <PERSON><PERSON><PERSON> "Expo Go" en tu móvil
echo    2. Escanea el código QR que aparecerá
echo    3. ¡Disfruta probando HabitStory!
echo.
echo 🛑 Para detener: Presiona Ctrl+C
echo.

call npm start

pause
