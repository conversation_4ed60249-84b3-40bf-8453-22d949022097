// Test script to verify journal functions work correctly
const { initDatabase, insertEntry, insertHabitTracking, upsertTraits } = require('./src/lib/db');

async function testJournalFunctions() {
  console.log('🧪 Testing Journal Functions...\n');

  try {
    // Initialize database
    console.log('1. Initializing database...');
    await initDatabase();
    console.log('✅ Database initialized successfully\n');

    // Test entry insertion
    console.log('2. Testing entry insertion...');
    const entryId = await insertEntry(
      '2025-01-03',
      'Test journal entry',
      ['exercise', 'reading'],
      { sleep_hours: 8, exercise_minutes: 30 },
      'Test reflection'
    );
    console.log(`✅ Entry inserted with ID: ${entryId}\n`);

    // Test habit tracking
    console.log('3. Testing habit tracking...');
    const habitId = await insertHabitTracking(
      'exercised',
      'health',
      '2025-01-03',
      true,
      0.9
    );
    console.log(`✅ Habit tracked with ID: ${habitId}\n`);

    // Test user traits
    console.log('4. Testing user traits...');
    await upsertTraits(
      'informal',
      'conversational',
      { optimistic: true, detail_oriented: true }
    );
    console.log('✅ User traits updated successfully\n');

    console.log('🎉 All tests passed! The journal functions are working correctly.');

  } catch (error) {
    console.error('❌ Test failed:', error);
    console.error('Error details:', error.message);
  }
}

// Run the test
testJournalFunctions();
