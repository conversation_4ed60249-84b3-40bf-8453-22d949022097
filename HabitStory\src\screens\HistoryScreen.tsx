import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Alert,
  RefreshControl,
  StyleSheet,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useEntries } from '../hooks/useEntries';
import { Entry } from '../lib/db';


const HistoryScreen: React.FC = () => {
  const { entries, isLoading, error, refreshEntries, removeEntry } = useEntries();
  const [selectedEntry, setSelectedEntry] = useState<Entry | null>(null);

  useEffect(() => {
    refreshEntries();
  }, []);

  const handleDeleteEntry = (entry: Entry) => {
    Alert.alert(
      'Delete Entry',
      `Are you sure you want to delete the entry from ${entry.date}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              await removeEntry(entry.id);
              if (selectedEntry?.id === entry.id) {
                setSelectedEntry(null);
              }
            } catch (error) {
              Alert.alert('Error', 'Failed to delete entry');
            }
          },
        },
      ]
    );
  };

  const renderEntryCard = (entry: Entry) => {
    const habits = JSON.parse(entry.habits || '[]');
    const metrics = JSON.parse(entry.metrics || '{}');
    const date = new Date(entry.date);
    const isSelected = selectedEntry?.id === entry.id;

    return (
      <TouchableOpacity
        key={entry.id}
        style={[
          styles.entryCard,
          isSelected ? styles.selectedCard : null
        ]}
        onPress={() => setSelectedEntry(isSelected ? null : entry)}
      >
        <View style={styles.cardHeader}>
          <Text style={styles.dateText}>
            {date.toLocaleDateString('en-US', {
              weekday: 'short',
              month: 'short',
              day: 'numeric'
            })}
          </Text>
          <TouchableOpacity
            onPress={() => handleDeleteEntry(entry)}
            style={styles.deleteButton}
          >
            <Text style={styles.deleteText}>Delete</Text>
          </TouchableOpacity>
        </View>

        <Text
          style={styles.entryText}
          numberOfLines={isSelected ? undefined : 3}
        >
          {entry.text}
        </Text>

        {isSelected && (
          <View style={styles.expandedSection}>
            {habits.length > 0 && (
              <View style={styles.section}>
                <Text style={styles.sectionTitle}>Habits:</Text>
                <View style={styles.tagContainer}>
                  {habits.map((habit: string, index: number) => (
                    <View key={index} style={styles.habitTag}>
                      <Text style={styles.habitText}>{habit}</Text>
                    </View>
                  ))}
                </View>
              </View>
            )}

            {Object.keys(metrics).length > 0 && (
              <View style={styles.section}>
                <Text style={styles.sectionTitle}>Metrics:</Text>
                <View style={styles.tagContainer}>
                  {Object.entries(metrics).map(([key, value]) => (
                    <View key={key} style={styles.metricTag}>
                      <Text style={styles.metricText}>
                        {key.replace('_', ' ')}: {value}
                      </Text>
                    </View>
                  ))}
                </View>
              </View>
            )}

            {entry.reflection && (
              <View style={styles.section}>
                <Text style={styles.sectionTitle}>Reflection:</Text>
                <Text style={styles.reflectionText}>"{entry.reflection}"</Text>
              </View>
            )}
          </View>
        )}
      </TouchableOpacity>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Journal History</Text>
        <Text style={styles.subtitle}>
          {entries.length} {entries.length === 1 ? 'entry' : 'entries'} recorded
        </Text>
      </View>

      {error && (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error}</Text>
        </View>
      )}

      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl refreshing={isLoading} onRefresh={refreshEntries} />
        }
      >
        {entries.length === 0 ? (
          <View style={styles.emptyState}>
            <Text style={styles.emptyIcon}>📚</Text>
            <Text style={styles.emptyTitle}>No entries yet</Text>
            <Text style={styles.emptyText}>
              Start journaling to see your entries here
            </Text>
          </View>
        ) : (
          entries.map((entry) => renderEntryCard(entry))
        )}

        <View style={styles.bottomPadding} />
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    backgroundColor: '#4F46E5',
    padding: 20,
    paddingTop: 40,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.9)',
  },
  errorContainer: {
    margin: 16,
    padding: 12,
    backgroundColor: '#fee2e2',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#fecaca',
  },
  errorText: {
    color: '#dc2626',
  },
  scrollView: {
    flex: 1,
    padding: 16,
  },
  entryCard: {
    backgroundColor: 'white',
    padding: 16,
    marginBottom: 12,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#e5e7eb',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  selectedCard: {
    borderColor: '#3b82f6',
    borderWidth: 2,
    backgroundColor: '#f0f9ff',
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  dateText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#374151',
  },
  deleteButton: {
    padding: 8,
  },
  deleteText: {
    color: '#ef4444',
    fontWeight: '500',
  },
  entryText: {
    fontSize: 16,
    color: '#6b7280',
    lineHeight: 24,
  },
  expandedSection: {
    borderTopWidth: 1,
    borderTopColor: '#e5e7eb',
    paddingTop: 12,
    marginTop: 12,
  },
  section: {
    marginBottom: 12,
  },
  sectionTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 8,
  },
  tagContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  habitTag: {
    backgroundColor: '#dcfce7',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    marginRight: 8,
    marginBottom: 4,
  },
  habitText: {
    color: '#166534',
    fontSize: 12,
    fontWeight: '500',
  },
  metricTag: {
    backgroundColor: '#dbeafe',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    marginRight: 8,
    marginBottom: 4,
  },
  metricText: {
    color: '#1e40af',
    fontSize: 12,
    fontWeight: '500',
  },
  reflectionText: {
    fontSize: 14,
    color: '#6b7280',
    fontStyle: 'italic',
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 80,
  },
  emptyIcon: {
    fontSize: 64,
    marginBottom: 16,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 8,
  },
  emptyText: {
    fontSize: 16,
    color: '#6b7280',
    textAlign: 'center',
  },
  bottomPadding: {
    height: 80,
  },
});

export default HistoryScreen;
