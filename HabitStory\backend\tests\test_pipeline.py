"""
Tests for the complete 9-module pipeline.
"""

import pytest
from unittest.mock import AsyncMock, patch, MagicMock
from datetime import datetime

from app.services.pipeline import weekly_report_pipeline, WeeklyReportPipeline
from tests.conftest import assert_valid_html, assert_valid_json_structure


class TestWeeklyReportPipeline:
    """Test the complete weekly report pipeline."""
    
    @pytest.mark.asyncio
    async def test_generate_weekly_report_success(
        self, 
        sample_weekly_entries, 
        sample_user_traits,
        mock_gemini_client
    ):
        """Test successful weekly report generation."""
        
        # Mock all service calls
        with patch('app.services.pipeline.parsing_service') as mock_parsing, \
             patch('app.services.pipeline.metrics_service') as mock_metrics, \
             patch('app.services.pipeline.correlations_service') as mock_correlations, \
             patch('app.services.pipeline.storytelling_service') as mock_storytelling, \
             patch('app.services.pipeline.recommendations_service') as mock_recommendations, \
             patch('app.services.pipeline.validation_service') as mock_validation, \
             patch('app.services.pipeline.questions_service') as mock_questions, \
             patch('app.services.pipeline.visualization_service') as mock_visualization, \
             patch('app.services.pipeline.report_builder_service') as mock_report_builder:
            
            # Configure mocks
            mock_parsing.parse_entry = AsyncMock(return_value={
                "habits": ["test_habit"],
                "metrics": {"mood_score": 8},
                "reflection": "Test reflection"
            })
            
            mock_metrics.calculate_weekly_stats.return_value = {
                "period": {"total_days": 5},
                "quantitative_metrics": {"mood_score": {"mean": 7.5}},
                "summary": {"overall_trend": "improving"}
            }
            
            mock_correlations.find_correlations.return_value = {
                "insights": [{"insight": "Test insight", "strength": "moderate"}]
            }
            
            mock_storytelling.generate_weekly_story = AsyncMock(
                return_value="This was an amazing week of growth..."
            )
            
            mock_recommendations.generate_weekly_recommendations = AsyncMock(
                return_value=[{"title": "Test recommendation", "priority": "high"}]
            )
            
            mock_validation.validate_weekly_data = AsyncMock(return_value={
                "data_quality_score": 0.9,
                "outliers": [],
                "inconsistencies": []
            })
            
            mock_questions.generate_weekly_questions = AsyncMock(
                return_value=[{"question": "Test question?", "category": "reflection"}]
            )
            
            mock_visualization.generate_weekly_visualizations.return_value = {
                "weekly_summary": "<svg>test chart</svg>"
            }
            
            mock_report_builder.build_weekly_report = AsyncMock(
                return_value="<!DOCTYPE html><html><body><h1>Test Report</h1></body></html>"
            )
            
            # Execute pipeline
            result = await weekly_report_pipeline.generate_weekly_report(
                user_id="test_user",
                entries=sample_weekly_entries,
                user_traits=sample_user_traits
            )
            
            # Assertions
            assert result["user_id"] == "test_user"
            assert len(result["modules_executed"]) == 9
            assert result["final_report"] is not None
            assert_valid_html(result["final_report"])
            assert "metadata" in result
            assert result["metadata"]["modules_completed"] == 9
            assert result["metadata"]["entries_processed"] == len(sample_weekly_entries)
    
    @pytest.mark.asyncio
    async def test_generate_weekly_report_with_errors(
        self, 
        sample_weekly_entries, 
        sample_user_traits
    ):
        """Test pipeline behavior when modules fail."""
        
        with patch('app.services.pipeline.metrics_service') as mock_metrics:
            # Make metrics service fail
            mock_metrics.calculate_weekly_stats.side_effect = Exception("Metrics calculation failed")
            
            result = await weekly_report_pipeline.generate_weekly_report(
                user_id="test_user",
                entries=sample_weekly_entries,
                user_traits=sample_user_traits
            )
            
            # Should still return a result with error information
            assert "errors" in result
            assert len(result["errors"]) > 0
            assert "Metrics calculation failed" in str(result["errors"])
            
            # Should have a fallback report
            assert result["final_report"] is not None
    
    @pytest.mark.asyncio
    async def test_generate_daily_feedback(
        self, 
        sample_journal_entry, 
        sample_user_traits,
        mock_gemini_client
    ):
        """Test daily feedback generation."""
        
        with patch('app.services.pipeline.parsing_service') as mock_parsing:
            mock_parsing.parse_entry = AsyncMock(return_value={
                "habits": ["exercise", "meditation"],
                "qualitative_habits": [
                    {"name": "morning run", "completed": True, "confidence": 0.9}
                ],
                "metrics": {"mood_score": 8, "energy_level": 9},
                "reflection": "Great day with good energy",
                "user_traits": sample_user_traits
            })
            
            result = await weekly_report_pipeline.generate_daily_feedback(
                user_id="test_user",
                entry=sample_journal_entry,
                user_traits=sample_user_traits
            )
            
            # Assertions
            assert "insights" in result
            assert "encouragement" in result
            assert "reflection_prompt" in result
            assert "parsed_data" in result
            
            # Check that insights are generated
            assert isinstance(result["insights"], list)
            assert isinstance(result["encouragement"], str)
            assert len(result["encouragement"]) > 0
    
    @pytest.mark.asyncio
    async def test_ensure_entries_parsed(self, sample_user_traits):
        """Test entry parsing logic."""
        
        # Mix of parsed and unparsed entries
        entries = [
            {
                "text": "Unparsed entry",
                "date": "2024-01-15",
                "user_id": "test_user"
            },
            {
                "text": "Already parsed entry",
                "date": "2024-01-16", 
                "user_id": "test_user",
                "habits": ["existing_habit"],
                "metrics": {"mood_score": 7},
                "reflection": "Already parsed"
            }
        ]
        
        with patch('app.services.pipeline.parsing_service') as mock_parsing:
            mock_parsing.parse_entry = AsyncMock(return_value={
                "habits": ["new_habit"],
                "metrics": {"mood_score": 8},
                "reflection": "Newly parsed"
            })
            
            pipeline = WeeklyReportPipeline()
            result = await pipeline._ensure_entries_parsed(
                "test_user", entries, sample_user_traits
            )
            
            # Should have 2 entries, one newly parsed, one already parsed
            assert len(result) == 2
            assert result[0]["habits"] == ["new_habit"]  # Newly parsed
            assert result[1]["habits"] == ["existing_habit"]  # Already parsed
            
            # Parsing service should only be called once
            mock_parsing.parse_entry.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_pipeline_with_empty_entries(self, sample_user_traits):
        """Test pipeline behavior with no entries."""
        
        result = await weekly_report_pipeline.generate_weekly_report(
            user_id="test_user",
            entries=[],
            user_traits=sample_user_traits
        )
        
        # Should return error
        assert "errors" in result
        assert len(result["errors"]) > 0
        assert "No journal entries provided" in str(result["errors"])
    
    @pytest.mark.asyncio
    async def test_pipeline_with_default_traits(self, sample_weekly_entries):
        """Test pipeline with default user traits."""
        
        with patch('app.services.pipeline.metrics_service') as mock_metrics, \
             patch('app.services.pipeline.report_builder_service') as mock_report_builder:
            
            mock_metrics.calculate_weekly_stats.return_value = {
                "period": {"total_days": 5},
                "summary": {"overall_trend": "stable"}
            }
            
            mock_report_builder.build_weekly_report = AsyncMock(
                return_value="<!DOCTYPE html><html><body>Test</body></html>"
            )
            
            result = await weekly_report_pipeline.generate_weekly_report(
                user_id="test_user",
                entries=sample_weekly_entries,
                user_traits=None  # Should use defaults
            )
            
            # Should complete successfully with default traits
            assert result["user_id"] == "test_user"
            assert result["final_report"] is not None
    
    def test_get_default_user_traits(self):
        """Test default user traits generation."""
        pipeline = WeeklyReportPipeline()
        traits = pipeline._get_default_user_traits()
        
        assert_valid_json_structure(traits, ["tone", "style", "traits", "trait_evidence"])
        assert traits["tone"] == "informal"
        assert traits["style"] == "conversational"
        assert isinstance(traits["traits"], dict)
        assert isinstance(traits["trait_evidence"], dict)
    
    @pytest.mark.asyncio
    async def test_generate_fallback_report(self, sample_weekly_entries, sample_user_traits):
        """Test fallback report generation."""
        
        with patch('app.services.pipeline.report_builder_service') as mock_report_builder:
            mock_report_builder._generate_fallback_report = AsyncMock(
                return_value="<!DOCTYPE html><html><body>Fallback Report</body></html>"
            )
            
            pipeline = WeeklyReportPipeline()
            result = await pipeline._generate_fallback_report(
                "test_user", 
                sample_weekly_entries, 
                sample_user_traits, 
                "Test error"
            )
            
            assert_valid_html(result)
            assert "Fallback Report" in result or len(sample_weekly_entries) > 0


class TestPipelineIntegration:
    """Integration tests for pipeline components."""
    
    @pytest.mark.asyncio
    async def test_module_execution_order(self, sample_weekly_entries, sample_user_traits):
        """Test that modules execute in the correct order."""
        
        execution_order = []
        
        def track_execution(module_name):
            def decorator(func):
                async def wrapper(*args, **kwargs):
                    execution_order.append(module_name)
                    return await func(*args, **kwargs) if asyncio.iscoroutinefunction(func) else func(*args, **kwargs)
                return wrapper
            return decorator
        
        with patch('app.services.pipeline.parsing_service') as mock_parsing, \
             patch('app.services.pipeline.metrics_service') as mock_metrics, \
             patch('app.services.pipeline.correlations_service') as mock_correlations, \
             patch('app.services.pipeline.validation_service') as mock_validation, \
             patch('app.services.pipeline.visualization_service') as mock_visualization, \
             patch('app.services.pipeline.storytelling_service') as mock_storytelling, \
             patch('app.services.pipeline.recommendations_service') as mock_recommendations, \
             patch('app.services.pipeline.questions_service') as mock_questions, \
             patch('app.services.pipeline.report_builder_service') as mock_report_builder:
            
            # Apply tracking decorators
            mock_parsing.parse_entry = track_execution("parsing")(AsyncMock(return_value={}))
            mock_metrics.calculate_weekly_stats = track_execution("metrics")(MagicMock(return_value={"summary": {}}))
            mock_correlations.find_correlations = track_execution("correlations")(MagicMock(return_value={"insights": []}))
            mock_validation.validate_weekly_data = track_execution("validation")(AsyncMock(return_value={"data_quality_score": 0.8}))
            mock_visualization.generate_weekly_visualizations = track_execution("visualization")(MagicMock(return_value={}))
            mock_storytelling.generate_weekly_story = track_execution("storytelling")(AsyncMock(return_value="Story"))
            mock_recommendations.generate_weekly_recommendations = track_execution("recommendations")(AsyncMock(return_value=[]))
            mock_questions.generate_weekly_questions = track_execution("questions")(AsyncMock(return_value=[]))
            mock_report_builder.build_weekly_report = track_execution("report_builder")(AsyncMock(return_value="<html></html>"))
            
            await weekly_report_pipeline.generate_weekly_report(
                user_id="test_user",
                entries=sample_weekly_entries,
                user_traits=sample_user_traits
            )
            
            # Verify execution order
            expected_order = [
                "metrics", "correlations", "validation", "visualization",
                "storytelling", "recommendations", "questions", "report_builder"
            ]
            
            # Check that all expected modules were executed
            for module in expected_order:
                assert module in execution_order, f"Module {module} was not executed"
    
    @pytest.mark.asyncio
    async def test_data_flow_between_modules(self, sample_weekly_entries, sample_user_traits):
        """Test that data flows correctly between modules."""
        
        captured_data = {}
        
        with patch('app.services.pipeline.metrics_service') as mock_metrics, \
             patch('app.services.pipeline.correlations_service') as mock_correlations, \
             patch('app.services.pipeline.storytelling_service') as mock_storytelling:
            
            # Capture data passed between modules
            def capture_metrics_call(entries):
                captured_data["metrics_input"] = entries
                return {"period": {"total_days": len(entries)}, "summary": {}}
            
            def capture_correlations_call(entries):
                captured_data["correlations_input"] = entries
                return {"insights": []}
            
            async def capture_storytelling_call(user_id, entries, stats, correlations, traits, feedback=None):
                captured_data["storytelling_inputs"] = {
                    "entries": entries,
                    "stats": stats,
                    "correlations": correlations,
                    "traits": traits
                }
                return "Generated story"
            
            mock_metrics.calculate_weekly_stats.side_effect = capture_metrics_call
            mock_correlations.find_correlations.side_effect = capture_correlations_call
            mock_storytelling.generate_weekly_story.side_effect = capture_storytelling_call
            
            # Mock other services to avoid errors
            with patch('app.services.pipeline.validation_service') as mock_validation, \
                 patch('app.services.pipeline.visualization_service') as mock_visualization, \
                 patch('app.services.pipeline.recommendations_service') as mock_recommendations, \
                 patch('app.services.pipeline.questions_service') as mock_questions, \
                 patch('app.services.pipeline.report_builder_service') as mock_report_builder:
                
                mock_validation.validate_weekly_data = AsyncMock(return_value={"data_quality_score": 0.8})
                mock_visualization.generate_weekly_visualizations.return_value = {}
                mock_recommendations.generate_weekly_recommendations = AsyncMock(return_value=[])
                mock_questions.generate_weekly_questions = AsyncMock(return_value=[])
                mock_report_builder.build_weekly_report = AsyncMock(return_value="<html></html>")
                
                await weekly_report_pipeline.generate_weekly_report(
                    user_id="test_user",
                    entries=sample_weekly_entries,
                    user_traits=sample_user_traits
                )
            
            # Verify data flow
            assert "metrics_input" in captured_data
            assert "correlations_input" in captured_data
            assert "storytelling_inputs" in captured_data
            
            # Check that the same entries were passed to metrics and correlations
            assert len(captured_data["metrics_input"]) == len(sample_weekly_entries)
            assert len(captured_data["correlations_input"]) == len(sample_weekly_entries)
            
            # Check that storytelling received outputs from previous modules
            storytelling_inputs = captured_data["storytelling_inputs"]
            assert "stats" in storytelling_inputs
            assert "correlations" in storytelling_inputs
            assert storytelling_inputs["stats"]["period"]["total_days"] == len(sample_weekly_entries)
