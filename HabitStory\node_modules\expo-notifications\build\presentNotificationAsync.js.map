{"version": 3, "file": "presentNotificationAsync.js", "sourceRoot": "", "sources": ["../src/presentNotificationAsync.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,IAAI,EAAE,MAAM,mBAAmB,CAAC;AAIzC;;;;;;;;;;GAUG;AACH,MAAM,CAAC,OAAO,CAAC,KAAK,UAAU,wBAAwB,CACpD,OAAiC,EACjC,aAAqB,IAAI,CAAC,EAAE,EAAE;IAE9B,MAAM,IAAI,KAAK,CACb,qLAAqL,CACtL,CAAC;AACJ,CAAC", "sourcesContent": ["import { uuid } from 'expo-modules-core';\n\nimport { NotificationContentInput } from './Notifications.types';\n\n/**\n * @hidden\n *\n * Schedules a notification for immediate trigger.\n * @param content An object representing the notification content.\n * @param identifier\n * @return It returns a Promise resolving with the notification's identifier once the notification is successfully scheduled for immediate display.\n * @header schedule\n * @deprecated This method has been deprecated in favor of using an explicit `NotificationHandler` and the [`scheduleNotificationAsync`](#schedulenotificationasyncrequest) method.\n * More information can be found in our [FYI document](https://expo.fyi/presenting-notifications-deprecated).\n */\nexport default async function presentNotificationAsync(\n  content: NotificationContentInput,\n  identifier: string = uuid.v4()\n): Promise<string> {\n  throw new Error(\n    '`presentNotificationAsync` has been removed. Use `scheduleNotificationAsync` + an explicit notification handler. Read more at https://expo.fyi/presenting-notifications-deprecated.'\n  );\n}\n"]}