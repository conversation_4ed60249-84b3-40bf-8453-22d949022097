{"version": 3, "file": "setNotificationChannelAsync.js", "sourceRoot": "", "sources": ["../src/setNotificationChannelAsync.ts"], "names": [], "mappings": "AAEA;;;;;;;;;;;;;;;;;;;;GAoBG;AACH,MAAM,CAAC,OAAO,CAAC,KAAK,UAAU,2BAA2B,CACvD,SAAiB,EACjB,OAAiC;IAEjC,OAAO,CAAC,KAAK,CAAC,6DAA6D,CAAC,CAAC;IAC7E,OAAO,IAAI,CAAC;AACd,CAAC", "sourcesContent": ["import { NotificationChannel, NotificationChannelInput } from './NotificationChannelManager.types';\n\n/**\n * Assigns the channel configuration to a channel of a specified name (creating it if need be).\n * This method lets you assign given notification channel to a notification channel group.\n *\n * > **Note:** After a channel has been created, you can modify only its name and description. This limitation is imposed by the Android OS.\n *\n * > **Note:** For some settings to be applied on all Android versions, it may be necessary to duplicate the configuration across both\n * > a single notification and its respective notification channel.\n *\n * For example, for a notification to play a custom sound on Android versions **below** 8.0,\n * the custom notification sound has to be set on the notification (through the [`NotificationContentInput`](#notificationcontentinput)),\n * and for the custom sound to play on Android versions **above** 8.0, the relevant notification channel must have the custom sound configured\n * (through the [`NotificationChannelInput`](#notificationchannelinput)). For more information,\n * see [Set custom notification sounds on Android](#set-custom-notification-sounds).\n * @param channelId The channel identifier.\n * @param channel Object representing the channel's configuration.\n * @return A Promise which resolving to the object (of type [`NotificationChannel`](#notificationchannel)) describing the modified channel\n * or to `null` if the platform does not support notification channels.\n * @platform android\n * @header channels\n */\nexport default async function setNotificationChannelAsync(\n  channelId: string,\n  channel: NotificationChannelInput\n): Promise<NotificationChannel | null> {\n  console.debug('Notification channels feature is only supported on Android.');\n  return null;\n}\n"]}