{"version": 3, "file": "registerTaskAsync.js", "sourceRoot": "", "sources": ["../src/registerTaskAsync.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,mBAAmB,EAAE,MAAM,mBAAmB,CAAC;AAExD,OAAO,iCAAiC,MAAM,qCAAqC,CAAC;AAEpF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAqCG;AACH,MAAM,CAAC,OAAO,CAAC,KAAK,UAAU,iBAAiB,CAAC,QAAgB;IAC9D,IAAI,CAAC,iCAAiC,CAAC,iBAAiB,EAAE,CAAC;QACzD,MAAM,IAAI,mBAAmB,CAAC,eAAe,EAAE,mBAAmB,CAAC,CAAC;IACtE,CAAC;IAED,OAAO,MAAM,iCAAiC,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;AAC7E,CAAC", "sourcesContent": ["import { UnavailabilityError } from 'expo-modules-core';\n\nimport BackgroundNotificationTasksModule from './BackgroundNotificationTasksModule';\n\n/**\n * Call `registerTaskAsync` to set a callback (task) that runs when a notification is received while the app is in foreground, background, or terminated.\n * Only on Android, the task also runs in response to a notification action press when the app is backgrounded or terminated.\n * When the app is terminated, only a [Headless Background Notification](/push-notifications/what-you-need-to-know/#headless-background-notifications) triggers the task execution.\n * However, the OS may decide not to deliver the notification to your app in some cases (e.g. when the device is in Doze mode on Android, or when you send too many notifications - Apple recommends to not [\"send more than two or three per hour\"](https://developer.apple.com/documentation/usernotifications/pushing-background-updates-to-your-app#overview)).\n *\n * Under the hood, this function is run using `expo-task-manager`. You **must** define the task first, with [`TaskManager.defineTask`](./task-manager#taskmanagerdefinetasktaskname-taskexecutor) and register it with `registerTaskAsync`.\n *\n * Make sure you define and register the task in the module scope of a JS module which is required early by your app (e.g. in the `index.js` file).\n * `expo-task-manager` loads your app's JS bundle in the background and executes the task, as well as any side effects which may happen as a consequence of requiring any JS modules.\n *\n * The callback function you define with `TaskManager.defineTask` receives an object with the following fields:\n * - `data`: The remote payload delivered by either FCM (Android) or APNs (iOS). See [`NotificationTaskPayload`](#notificationtaskpayload) for details.\n * - `error`: The error (if any) that occurred during execution of the task.\n * - `executionInfo`: JSON object of additional info related to the task, including the `taskName`.\n * @param taskName The string you passed to `TaskManager.defineTask` as the `taskName` parameter.\n *\n * @example\n * ```ts\n * import * as TaskManager from 'expo-task-manager';\n * import * as Notifications from 'expo-notifications';\n *\n * const BACKGROUND_NOTIFICATION_TASK = 'BACKGROUND-NOTIFICATION-TASK';\n *\n * defineTask<Notifications.NotificationTaskPayload>(BACKGROUND_NOTIFICATION_TASK, ({ data, error, executionInfo }) => {\n *   console.log('Received a notification task payload!');\n *   const isNotificationResponse = 'actionIdentifier' in taskPayload;\n *   if (isNotificationResponse) {\n *     // Do something with the notification response from user\n *   } else {\n *     // Do something with the data from notification that was received\n *   }\n * });\n *\n * Notifications.registerTaskAsync(BACKGROUND_NOTIFICATION_TASK);\n * ```\n * @header inBackground\n */\nexport default async function registerTaskAsync(taskName: string): Promise<null> {\n  if (!BackgroundNotificationTasksModule.registerTaskAsync) {\n    throw new UnavailabilityError('Notifications', 'registerTaskAsync');\n  }\n\n  return await BackgroundNotificationTasksModule.registerTaskAsync(taskName);\n}\n"]}