import * as SQLite from 'expo-sqlite';

// Database types
export interface Entry {
  id: number;
  date: string;
  text: string;
  habits: string; // JSON string array
  metrics: string; // JSON object
  reflection: string;
  created_at: string;
  updated_at: string;
}

export interface Report {
  id: number;
  week_start: string;
  week_end: string;
  html_content: string;
  feedback_rating: number | null;
  feedback_text: string | null;
  created_at: string;
  updated_at: string;
}

export interface UserTraits {
  id: number;
  tone: string;
  style: string;
  traits: string; // JSON string
  trait_evidence: string; // JSON string with evidence for each trait
  updated_at: string;
}

export interface Feedback {
  id: number;
  report_id: number;
  rating: number; // 1 for thumbs up, -1 for thumbs down
  text: string | null;
  created_at: string;
}

export interface HabitTracking {
  id: number;
  habit_name: string;
  habit_category: string; // 'health', 'productivity', 'wellness', 'social', 'personal'
  date: string;
  completed: boolean;
  confidence_score: number; // 0-1, how confident AI is about detection
  created_at: string;
}

// Database instance
let db: SQLite.SQLiteDatabase | null = null;

// Initialize database
export const initDatabase = async (): Promise<void> => {
  try {
    db = await SQLite.openDatabaseAsync('habitstory.db');
    
    // Create tables
    await createTables();
    console.log('Database initialized successfully');
  } catch (error) {
    console.error('Error initializing database:', error);
    throw error;
  }
};

// Create all tables
const createTables = async (): Promise<void> => {
  if (!db) throw new Error('Database not initialized');

  // Entries table
  await db.execAsync(`
    CREATE TABLE IF NOT EXISTS entries (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      date TEXT NOT NULL UNIQUE,
      text TEXT NOT NULL,
      habits TEXT NOT NULL,
      metrics TEXT NOT NULL,
      reflection TEXT NOT NULL,
      created_at TEXT DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT DEFAULT CURRENT_TIMESTAMP
    );
  `);

  // Reports table
  await db.execAsync(`
    CREATE TABLE IF NOT EXISTS reports (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      week_start TEXT NOT NULL,
      week_end TEXT NOT NULL,
      html_content TEXT NOT NULL,
      feedback_rating INTEGER,
      feedback_text TEXT,
      created_at TEXT DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT DEFAULT CURRENT_TIMESTAMP
    );
  `);

  // User traits table
  await db.execAsync(`
    CREATE TABLE IF NOT EXISTS user_traits (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      tone TEXT NOT NULL DEFAULT 'informal',
      style TEXT NOT NULL DEFAULT 'conversational',
      traits TEXT NOT NULL DEFAULT '{}',
      trait_evidence TEXT NOT NULL DEFAULT '{}',
      updated_at TEXT DEFAULT CURRENT_TIMESTAMP
    );
  `);

  // Feedback table
  await db.execAsync(`
    CREATE TABLE IF NOT EXISTS feedback (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      report_id INTEGER NOT NULL,
      rating INTEGER NOT NULL,
      text TEXT,
      created_at TEXT DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (report_id) REFERENCES reports (id)
    );
  `);

  // Habit tracking table
  await db.execAsync(`
    CREATE TABLE IF NOT EXISTS habit_tracking (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      habit_name TEXT NOT NULL,
      habit_category TEXT NOT NULL DEFAULT 'personal',
      date TEXT NOT NULL,
      completed INTEGER NOT NULL DEFAULT 1,
      confidence_score REAL NOT NULL DEFAULT 1.0,
      created_at TEXT DEFAULT CURRENT_TIMESTAMP,
      UNIQUE(habit_name, date)
    );
  `);

  // Add trait_evidence column if it doesn't exist (migration)
  try {
    await db.execAsync(`ALTER TABLE user_traits ADD COLUMN trait_evidence TEXT NOT NULL DEFAULT '{}'`);
  } catch (error) {
    // Column already exists, ignore error
  }

  // Insert default user traits if not exists
  const traitsCount = await db.getFirstAsync('SELECT COUNT(*) as count FROM user_traits');
  if ((traitsCount as any)?.count === 0) {
    await db.runAsync(`
      INSERT INTO user_traits (tone, style, traits, trait_evidence)
      VALUES ('informal', 'conversational', '{"optimistic": true, "detail_oriented": false}', '{"optimistic": ["positive language used"], "detail_oriented": ["brief descriptions"]}')
    `);
  }
};

// Entry operations
export const insertEntry = async (
  date: string,
  text: string,
  habits: string[],
  metrics: Record<string, any>,
  reflection: string
): Promise<number> => {
  if (!db) throw new Error('Database not initialized');

  const result = await db.runAsync(
    `INSERT INTO entries (date, text, habits, metrics, reflection, updated_at)
     VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP)`,
    [date, text, JSON.stringify(habits), JSON.stringify(metrics), reflection]
  );

  return result.lastInsertRowId;
};

export const upsertEntry = async (
  date: string,
  text: string,
  habits: string[],
  metrics: Record<string, any>,
  reflection: string
): Promise<number> => {
  if (!db) throw new Error('Database not initialized');

  const result = await db.runAsync(
    `INSERT OR REPLACE INTO entries (date, text, habits, metrics, reflection, updated_at)
     VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP)`,
    [date, text, JSON.stringify(habits), JSON.stringify(metrics), reflection]
  );

  return result.lastInsertRowId;
};

export const getEntry = async (date: string): Promise<Entry | null> => {
  if (!db) throw new Error('Database not initialized');

  const entry = await db.getFirstAsync(
    'SELECT * FROM entries WHERE date = ?',
    [date]
  ) as Entry | null;

  return entry;
};

export const getWeekEntries = async (startDate: string, endDate: string): Promise<Entry[]> => {
  if (!db) throw new Error('Database not initialized');

  const entries = await db.getAllAsync(
    'SELECT * FROM entries WHERE date >= ? AND date <= ? ORDER BY date ASC',
    [startDate, endDate]
  ) as Entry[];

  return entries;
};

export const getAllEntries = async (): Promise<Entry[]> => {
  if (!db) throw new Error('Database not initialized');

  const entries = await db.getAllAsync(
    'SELECT * FROM entries ORDER BY date DESC'
  ) as Entry[];

  return entries;
};

export const updateEntry = async (
  id: number,
  text: string,
  habits: string[],
  metrics: Record<string, any>,
  reflection: string
): Promise<void> => {
  if (!db) throw new Error('Database not initialized');

  await db.runAsync(
    `UPDATE entries 
     SET text = ?, habits = ?, metrics = ?, reflection = ?, updated_at = CURRENT_TIMESTAMP 
     WHERE id = ?`,
    [text, JSON.stringify(habits), JSON.stringify(metrics), reflection, id]
  );
};

export const deleteEntry = async (id: number): Promise<void> => {
  if (!db) throw new Error('Database not initialized');

  await db.runAsync('DELETE FROM entries WHERE id = ?', [id]);
};

// Report operations
export const insertReport = async (
  weekStart: string,
  weekEnd: string,
  htmlContent: string
): Promise<number> => {
  if (!db) throw new Error('Database not initialized');

  const result = await db.runAsync(
    `INSERT INTO reports (week_start, week_end, html_content, updated_at) 
     VALUES (?, ?, ?, CURRENT_TIMESTAMP)`,
    [weekStart, weekEnd, htmlContent]
  );

  return result.lastInsertRowId;
};

export const getLatestReport = async (): Promise<Report | null> => {
  if (!db) throw new Error('Database not initialized');

  const report = await db.getFirstAsync(
    'SELECT * FROM reports ORDER BY created_at DESC LIMIT 1'
  ) as Report | null;

  return report;
};

export const getAllReports = async (): Promise<Report[]> => {
  if (!db) throw new Error('Database not initialized');

  const reports = await db.getAllAsync(
    'SELECT * FROM reports ORDER BY created_at DESC'
  ) as Report[];

  return reports;
};

export const updateReportFeedback = async (
  reportId: number,
  rating: number,
  text: string | null
): Promise<void> => {
  if (!db) throw new Error('Database not initialized');

  await db.runAsync(
    `UPDATE reports 
     SET feedback_rating = ?, feedback_text = ?, updated_at = CURRENT_TIMESTAMP 
     WHERE id = ?`,
    [rating, text, reportId]
  );
};

// User traits operations
export const getUserTraits = async (): Promise<UserTraits | null> => {
  if (!db) throw new Error('Database not initialized');

  const traits = await db.getFirstAsync(
    'SELECT * FROM user_traits ORDER BY updated_at DESC LIMIT 1'
  ) as UserTraits | null;

  return traits;
};

export const upsertTraits = async (
  tone: string,
  style: string,
  traits: Record<string, any>,
  traitEvidence?: Record<string, string[]>
): Promise<void> => {
  if (!db) throw new Error('Database not initialized');

  const existingTraits = await getUserTraits();
  const evidenceToStore = traitEvidence || {};

  if (existingTraits) {
    await db.runAsync(
      `UPDATE user_traits
       SET tone = ?, style = ?, traits = ?, trait_evidence = ?, updated_at = CURRENT_TIMESTAMP
       WHERE id = ?`,
      [tone, style, JSON.stringify(traits), JSON.stringify(evidenceToStore), existingTraits.id]
    );
  } else {
    await db.runAsync(
      `INSERT INTO user_traits (tone, style, traits, trait_evidence, updated_at)
       VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP)`,
      [tone, style, JSON.stringify(traits), JSON.stringify(evidenceToStore)]
    );
  }
};

// Feedback operations
export const insertFeedback = async (
  reportId: number,
  rating: number,
  text: string | null
): Promise<number> => {
  if (!db) throw new Error('Database not initialized');

  const result = await db.runAsync(
    `INSERT INTO feedback (report_id, rating, text) 
     VALUES (?, ?, ?)`,
    [reportId, rating, text]
  );

  // Also update the report with this feedback
  await updateReportFeedback(reportId, rating, text);

  return result.lastInsertRowId;
};

export const getFeedbackForReport = async (reportId: number): Promise<Feedback[]> => {
  if (!db) throw new Error('Database not initialized');

  const feedback = await db.getAllAsync(
    'SELECT * FROM feedback WHERE report_id = ? ORDER BY created_at DESC',
    [reportId]
  ) as Feedback[];

  return feedback;
};

// Utility functions
export const getWeekDateRange = (date: Date): { start: string; end: string } => {
  const startOfWeek = new Date(date);
  const day = startOfWeek.getDay();
  const diff = startOfWeek.getDate() - day; // Sunday as start of week
  startOfWeek.setDate(diff);
  
  const endOfWeek = new Date(startOfWeek);
  endOfWeek.setDate(startOfWeek.getDate() + 6);
  
  return {
    start: startOfWeek.toISOString().split('T')[0],
    end: endOfWeek.toISOString().split('T')[0]
  };
};

export const formatDate = (date: Date): string => {
  return date.toISOString().split('T')[0];
};

// Habit tracking operations
export const insertHabitTracking = async (
  habitName: string,
  habitCategory: string,
  date: string,
  completed: boolean = true,
  confidenceScore: number = 1.0
): Promise<number> => {
  if (!db) throw new Error('Database not initialized');

  const result = await db.runAsync(
    `INSERT OR REPLACE INTO habit_tracking (habit_name, habit_category, date, completed, confidence_score)
     VALUES (?, ?, ?, ?, ?)`,
    [habitName, habitCategory, date, completed ? 1 : 0, confidenceScore]
  );

  return result.lastInsertRowId;
};

export const getHabitsForDate = async (date: string): Promise<HabitTracking[]> => {
  if (!db) throw new Error('Database not initialized');

  const habits = await db.getAllAsync(
    'SELECT * FROM habit_tracking WHERE date = ? ORDER BY habit_category, habit_name',
    [date]
  ) as HabitTracking[];

  return habits;
};

export const getHabitHistory = async (habitName: string, days: number = 30): Promise<HabitTracking[]> => {
  if (!db) throw new Error('Database not initialized');

  const habits = await db.getAllAsync(
    `SELECT * FROM habit_tracking
     WHERE habit_name = ? AND date >= date('now', '-${days} days')
     ORDER BY date DESC`,
    [habitName]
  ) as HabitTracking[];

  return habits;
};

export const getHabitStreak = async (habitName: string): Promise<number> => {
  if (!db) throw new Error('Database not initialized');

  const habits = await db.getAllAsync(
    `SELECT date FROM habit_tracking
     WHERE habit_name = ? AND completed = 1
     ORDER BY date DESC`,
    [habitName]
  ) as { date: string }[];

  if (habits.length === 0) return 0;

  let streak = 0;
  const today = new Date();

  for (let i = 0; i < habits.length; i++) {
    const habitDate = new Date(habits[i].date);
    const expectedDate = new Date(today);
    expectedDate.setDate(today.getDate() - i);

    if (habitDate.toDateString() === expectedDate.toDateString()) {
      streak++;
    } else {
      break;
    }
  }

  return streak;
};

export const getAllUniqueHabits = async (): Promise<{ habit_name: string; habit_category: string; count: number }[]> => {
  if (!db) throw new Error('Database not initialized');

  const habits = await db.getAllAsync(
    `SELECT habit_name, habit_category, COUNT(*) as count
     FROM habit_tracking
     GROUP BY habit_name, habit_category
     ORDER BY count DESC, habit_name`
  ) as { habit_name: string; habit_category: string; count: number }[];

  return habits;
};

// Database cleanup and reset (for development)
export const resetDatabase = async (): Promise<void> => {
  if (!db) throw new Error('Database not initialized');

  await db.execAsync('DROP TABLE IF EXISTS entries');
  await db.execAsync('DROP TABLE IF EXISTS reports');
  await db.execAsync('DROP TABLE IF EXISTS user_traits');
  await db.execAsync('DROP TABLE IF EXISTS feedback');
  await db.execAsync('DROP TABLE IF EXISTS habit_tracking');

  await createTables();
  console.log('Database reset successfully');
};
