{"version": 3, "file": "NotificationPermissions.types.d.ts", "sourceRoot": "", "sources": ["../src/NotificationPermissions.types.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,kBAAkB,EAAE,MAAM,mBAAmB,CAAC;AAEvD,oBAAY,aAAa;IACvB,IAAI,IAAI;IACR,MAAM,IAAI;IACV,KAAK,IAAI;CACV;AAED,oBAAY,iBAAiB;IAC3B,KAAK,IAAI;IACT,MAAM,IAAI;IACV,kBAAkB,IAAI;CACvB;AAED,oBAAY,sBAAsB;IAChC,cAAc,IAAI;IAClB,MAAM,IAAI;IACV,UAAU,IAAI;IACd,WAAW,IAAI;IACf,SAAS,IAAI;CACd;AAGD,MAAM,WAAW,6BAA8B,SAAQ,kBAAkB;IACvE,OAAO,CAAC,EAAE;QACR,UAAU,EAAE,MAAM,CAAC;QACnB,kBAAkB,CAAC,EAAE,MAAM,CAAC;KAC7B,CAAC;IACF,GAAG,CAAC,EAAE;QACJ,MAAM,EAAE,sBAAsB,CAAC;QAC/B,iCAAiC,EAAE,OAAO,GAAG,IAAI,CAAC;QAClD,yBAAyB,EAAE,OAAO,GAAG,IAAI,CAAC;QAC1C,sBAAsB,EAAE,OAAO,GAAG,IAAI,CAAC;QACvC,WAAW,EAAE,OAAO,GAAG,IAAI,CAAC;QAC5B,WAAW,EAAE,OAAO,GAAG,IAAI,CAAC;QAC5B,WAAW,EAAE,OAAO,GAAG,IAAI,CAAC;QAC5B,oBAAoB,CAAC,EAAE,OAAO,GAAG,IAAI,CAAC;QACtC,UAAU,EAAE,aAAa,CAAC;QAC1B,cAAc,CAAC,EAAE,iBAAiB,CAAC;QACnC,+BAA+B,CAAC,EAAE,OAAO,CAAC;QAC1C,mBAAmB,CAAC,EAAE,OAAO,GAAG,IAAI,CAAC;KACtC,CAAC;CACH;AAED;;;GAGG;AACH,MAAM,WAAW,iCAAiC;IAChD;;OAEG;IACH,UAAU,CAAC,EAAE,OAAO,CAAC;IACrB;;OAEG;IACH,UAAU,CAAC,EAAE,OAAO,CAAC;IACrB;;OAEG;IACH,UAAU,CAAC,EAAE,OAAO,CAAC;IACrB;;OAEG;IACH,qBAAqB,CAAC,EAAE,OAAO,CAAC;IAChC;;OAEG;IACH,mBAAmB,CAAC,EAAE,OAAO,CAAC;IAC9B;;OAEG;IACH,8BAA8B,CAAC,EAAE,OAAO,CAAC;IACzC;;OAEG;IACH,gBAAgB,CAAC,EAAE,OAAO,CAAC;CAC5B;AAED,MAAM,MAAM,oCAAoC,GAAG,iCAAiC,GAAG,MAAM,CAAC;AAE9F;;;GAGG;AACH,MAAM,WAAW,8BAA8B;IAC7C;;OAEG;IACH,GAAG,CAAC,EAAE,iCAAiC,CAAC;IACxC;;OAEG;IACH,OAAO,CAAC,EAAE,MAAM,CAAC;CAClB"}