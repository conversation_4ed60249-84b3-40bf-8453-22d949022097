import axios from 'axios';
import * as SecureStore from 'expo-secure-store';
import { WEEKLY_SUMMARY_PROMPT, PromptVariables } from './prompts';

// Gemini API configuration
const GEMINI_API_URL = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro:generateContent';
const API_KEY_STORAGE_KEY = 'gemini_api_key';

// Types for Gemini responses
export interface QualitativeHabit {
  name: string;
  category: 'health' | 'productivity' | 'wellness' | 'social' | 'personal';
  completed: boolean;
  confidence: number; // 0-1
}

export interface ParsedEntry {
  habits: string[]; // Keep for backward compatibility
  qualitative_habits: QualitativeHabit[];
  metrics: {
    sleep_hours?: number;
    water_liters?: number;
    exercise_minutes?: number;
    mood_score?: number;
    stress_level?: number;
    productivity_score?: number;
    social_interactions?: number;
    screen_time_hours?: number;
    meditation_minutes?: number;
    steps?: number;
    [key: string]: any;
  };
  reflection: string;
  user_traits: {
    tone: string;
    style: string;
    traits: Record<string, any>;
    trait_evidence: Record<string, string[]>;
  };
}

export interface WeeklySummary {
  html_content: string;
}

// API Key management
export const setApiKey = async (apiKey: string): Promise<void> => {
  try {
    await SecureStore.setItemAsync(API_KEY_STORAGE_KEY, apiKey);
  } catch (error) {
    console.error('Error storing API key:', error);
    throw new Error('Failed to store API key securely');
  }
};

export const getApiKey = async (): Promise<string | null> => {
  try {
    // Always use the centralized API key from environment
    const envKey = process.env.GEMINI_API_KEY;
    if (envKey) {
      console.log('Using centralized API key from environment:', envKey.slice(0, 8) + '...');
      return envKey;
    }

    console.log('No centralized API key found in environment');
    return null;
  } catch (error) {
    console.error('Error retrieving API key:', error);
    return null;
  }
};

export const removeApiKey = async (): Promise<void> => {
  try {
    await SecureStore.deleteItemAsync(API_KEY_STORAGE_KEY);
  } catch (error) {
    console.error('Error removing API key:', error);
  }
};

export const hasApiKey = async (): Promise<boolean> => {
  const apiKey = await getApiKey();
  const hasKey = apiKey !== null && apiKey.trim().length > 0;
  console.log('Centralized API key available:', hasKey);
  return hasKey;
};

// Gemini API calls
const makeGeminiRequest = async (
  prompt: string,
  temperature: number = 0.7
): Promise<string> => {
  const apiKey = await getApiKey();
  
  if (!apiKey) {
    throw new Error('Gemini API service is temporarily unavailable. Please try again later.');
  }

  try {
    const response = await axios.post(
      `${GEMINI_API_URL}?key=${apiKey}`,
      {
        contents: [{
          parts: [{
            text: prompt
          }]
        }],
        generationConfig: {
          temperature: temperature,
          topK: 40,
          topP: 0.95,
          maxOutputTokens: 2048,
        }
      },
      {
        headers: {
          'Content-Type': 'application/json',
        },
        timeout: 30000, // 30 seconds timeout
      }
    );

    if (response.data?.candidates?.[0]?.content?.parts?.[0]?.text) {
      return response.data.candidates[0].content.parts[0].text.trim();
    } else {
      throw new Error('Invalid response format from Gemini API');
    }
  } catch (error) {
    console.error('Gemini API Error:', error);
    
    if (axios.isAxiosError(error)) {
      if (error.response?.status === 400) {
        throw new Error('Invalid request. Please check your input.');
      } else if (error.response?.status === 403) {
        throw new Error('Invalid API key. Please check your Gemini API key.');
      } else if (error.response?.status === 429) {
        throw new Error('Rate limit exceeded. Please try again later.');
      } else if (error.response?.status === 500) {
        throw new Error('Gemini service is temporarily unavailable. Please try again later.');
      } else if (error.code === 'ECONNABORTED') {
        throw new Error('Request timeout. Please check your internet connection and try again.');
      }
    }
    
    throw new Error('Failed to communicate with Gemini API. Please try again.');
  }
};

// Parse daily entry with Gemini
export const parseEntry = async (
  entryText: string,
  previousTraits?: any
): Promise<ParsedEntry> => {
  const prompt = `You are an AI assistant that analyzes daily journal entries to extract structured data.

Analyze the following journal entry and extract:

1. **habits**: Array of habit-related activities mentioned (e.g., ["morning run", "meditation", "reading"]) - KEEP FOR COMPATIBILITY
2. **qualitative_habits**: Array of qualitative habits with detailed tracking:
   Each habit should be an object with:
   - name: Clear, consistent habit name (e.g., "ate healthy", "slept well", "exercised", "meditated", "read", "socialized")
   - category: One of "health", "productivity", "wellness", "social", "personal"
   - completed: true if the habit was done/achieved, false if mentioned as not done
   - confidence: 0-1 score of how confident you are about this detection

   Examples of qualitative habits to detect:
   - Health: "ate healthy", "drank water", "took vitamins", "avoided junk food", "cooked at home"
   - Productivity: "worked focused", "completed tasks", "organized space", "planned day", "avoided distractions"
   - Wellness: "slept well", "felt energized", "managed stress", "practiced gratitude", "relaxed"
   - Social: "connected with friends", "called family", "helped someone", "attended social event"
   - Personal: "read", "learned something", "practiced hobby", "reflected", "journaled"

3. **metrics**: Object with quantifiable data when mentioned:
   - sleep_hours: Hours of sleep (number)
   - water_liters: Water consumption in liters (number)
   - exercise_minutes: Exercise duration in minutes (number)
   - mood_score: Mood on scale 1-10 (number)
   - stress_level: Stress on scale 1-10 (number)
   - productivity_score: Productivity on scale 1-10 (number)
   - social_interactions: Number of meaningful social interactions (number)
   - screen_time_hours: Screen time in hours (number)
   - meditation_minutes: Meditation time in minutes (number)
   - steps: Number of steps taken (number)
   - Any other quantifiable metrics mentioned
4. **reflection**: A brief 1-2 sentence summary of the main insight or feeling from the entry
5. **user_traits**: Object analyzing the user's communication style:
   - tone: "formal", "informal", "casual", "professional"
   - style: "descriptive", "concise", "emotional", "analytical", "conversational", "poetic"
   - traits: Object with personality traits like {"optimistic": true, "detail_oriented": false, "introspective": true}
   - trait_evidence: Object with evidence for each trait, e.g. {"optimistic": ["used positive language", "focused on good outcomes"], "detail_oriented": ["provided specific times", "mentioned exact quantities"]}

Previous user traits for reference: ${previousTraits ? JSON.stringify(previousTraits) : 'None'}

Journal Entry:
"${entryText}"

IMPORTANT: Respond with ONLY a valid JSON object. Do not include any explanatory text, markdown formatting, or code blocks. Just the raw JSON object in this exact format:

{
  "habits": ["habit1", "habit2"],
  "qualitative_habits": [
    {
      "name": "ate healthy",
      "category": "health",
      "completed": true,
      "confidence": 0.9
    },
    {
      "name": "slept well",
      "category": "wellness",
      "completed": false,
      "confidence": 0.8
    }
  ],
  "metrics": {"sleep_hours": 8, "mood_score": 7},
  "reflection": "Brief insight from the entry",
  "user_traits": {
    "tone": "informal",
    "style": "conversational",
    "traits": {"optimistic": true, "detail_oriented": false},
    "trait_evidence": {"optimistic": ["used positive language", "focused on achievements"], "detail_oriented": ["mentioned specific times", "provided exact quantities"]}
  }
}`;

  try {
    const response = await makeGeminiRequest(prompt, 0.3);
    console.log('Raw Gemini response:', response);

    // Clean and extract JSON from the response
    let jsonString = response.trim();

    // Remove markdown code blocks if present
    jsonString = jsonString.replace(/```json\s*/g, '').replace(/```\s*$/g, '');

    // Try to find JSON object in the response
    const jsonMatch = jsonString.match(/\{[\s\S]*\}/);
    if (jsonMatch) {
      jsonString = jsonMatch[0];
    }

    // Fix common JSON issues
    // Remove trailing commas before closing brackets/braces
    jsonString = jsonString.replace(/,(\s*[}\]])/g, '$1');
    // Remove extra commas
    jsonString = jsonString.replace(/,,+/g, ',');

    console.log('Cleaned JSON string:', jsonString);

    // Try to parse the JSON response
    const parsed = JSON.parse(jsonString);
    
    // Validate the response structure
    if (!parsed.habits || !Array.isArray(parsed.habits)) {
      parsed.habits = [];
    }
    if (!parsed.qualitative_habits || !Array.isArray(parsed.qualitative_habits)) {
      parsed.qualitative_habits = [];
    }
    // Validate each qualitative habit
    parsed.qualitative_habits = parsed.qualitative_habits.filter((habit: any) => {
      return habit &&
             typeof habit.name === 'string' &&
             typeof habit.category === 'string' &&
             typeof habit.completed === 'boolean' &&
             typeof habit.confidence === 'number';
    });
    if (!parsed.metrics || typeof parsed.metrics !== 'object') {
      parsed.metrics = {};
    }
    if (!parsed.reflection || typeof parsed.reflection !== 'string') {
      parsed.reflection = 'No specific reflection extracted.';
    }
    if (!parsed.user_traits) {
      parsed.user_traits = {
        tone: 'informal',
        style: 'conversational',
        traits: {}
      };
    }

    return parsed as ParsedEntry;
  } catch (error) {
    console.error('Error parsing entry:', error);

    if (error instanceof SyntaxError) {
      console.error('JSON parsing failed. This might be due to invalid JSON format from Gemini API.');
    }

    // Return a fallback response if parsing fails
    return {
      habits: [],
      qualitative_habits: [],
      metrics: {},
      reflection: 'Unable to analyze entry automatically. Please try again.',
      user_traits: {
        tone: 'informal',
        style: 'conversational',
        traits: {},
        trait_evidence: {}
      }
    };
  }
};

// Generate weekly summary with Gemini using structured prompt
export const generateWeeklySummary = async (
  weekEntries: any[],
  userTraits: any,
  previousFeedback?: string,
  userName?: string
): Promise<WeeklySummary> => {
  // Prepare variables for the structured prompt
  const promptVariables: PromptVariables = {
    weekEntries,
    userTraits,
    previousFeedback,
    userName
  };

  // Use the structured WEEKLY_SUMMARY_PROMPT
  const prompt = WEEKLY_SUMMARY_PROMPT(promptVariables);

  try {
    const htmlContent = await makeGeminiRequest(prompt, 0.7);
    
    return {
      html_content: htmlContent
    };
  } catch (error) {
    console.error('Error generating weekly summary:', error);
    
    // Return a fallback HTML if generation fails
    const fallbackHtml = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Weekly Summary</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #f8fafc; }
        .container { max-width: 600px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; }
        h1 { color: #0ea5e9; text-align: center; }
        .error { color: #ef4444; text-align: center; padding: 20px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Weekly Summary</h1>
        <div class="error">
            <p>Unable to generate personalized summary at this time.</p>
            <p>Please check your internet connection and try again.</p>
        </div>
    </div>
</body>
</html>`;
    
    return {
      html_content: fallbackHtml
    };
  }
};

// Test API connection
export const testApiConnection = async (): Promise<boolean> => {
  try {
    const response = await makeGeminiRequest('Hello, please respond with just the word "success" to test the connection.', 0);
    return response.toLowerCase().includes('success');
  } catch (error) {
    console.error('API connection test failed:', error);
    return false;
  }
};
