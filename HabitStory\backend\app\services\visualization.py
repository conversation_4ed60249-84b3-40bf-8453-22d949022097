"""
Module 8: Visualization Service
Generates SVG charts using Python/Plotly for data visualization.
"""

import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import pandas as pd
import numpy as np
import json
import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)


class VisualizationService:
    """Service for generating data visualizations as SVG."""
    
    def __init__(self):
        # Color palette for consistent styling
        self.colors = {
            'primary': '#3B82F6',      # Blue
            'secondary': '#10B981',     # Green
            'accent': '#F59E0B',        # Amber
            'warning': '#EF4444',       # Red
            'info': '#8B5CF6',          # Purple
            'neutral': '#6B7280',       # Gray
            'success': '#059669',       # Emerald
            'background': '#F9FAFB'     # Light gray
        }
        
        # Chart configuration
        self.chart_config = {
            'width': 800,
            'height': 400,
            'font_family': 'Inter, system-ui, sans-serif',
            'font_size': 12,
            'margin': dict(l=60, r=60, t=80, b=60)
        }
    
    def generate_weekly_visualizations(
        self,
        entries: List[Dict[str, Any]],
        stats: Dict[str, Any],
        correlations: Dict[str, Any]
    ) -> Dict[str, str]:
        """
        Generate comprehensive weekly visualizations.
        
        Args:
            entries: List of journal entries
            stats: Weekly statistics
            correlations: Correlation analysis results
            
        Returns:
            Dict containing SVG strings for different chart types
        """
        try:
            visualizations = {}
            
            # Convert entries to DataFrame
            df = self._entries_to_dataframe(entries)
            
            if not df.empty:
                # Metrics trend chart
                visualizations['metrics_trends'] = self._create_metrics_trends_chart(df, stats)
                
                # Habits performance chart
                visualizations['habits_performance'] = self._create_habits_performance_chart(stats)
                
                # Correlation heatmap
                if correlations.get('metric_correlations'):
                    visualizations['correlations_heatmap'] = self._create_correlations_heatmap(correlations)
                
                # Weekly summary dashboard
                visualizations['weekly_summary'] = self._create_weekly_summary_chart(stats)
                
                # Mood and wellness trends
                visualizations['wellness_trends'] = self._create_wellness_trends_chart(df)
            
            # Always include a summary stats chart even with limited data
            if 'weekly_summary' not in visualizations:
                visualizations['weekly_summary'] = self._create_basic_summary_chart(stats)
            
            return visualizations
            
        except Exception as e:
            logger.error(f"Error generating visualizations: {e}")
            return self._get_fallback_visualizations()
    
    def generate_habit_visualization(
        self,
        habit_name: str,
        habit_data: Dict[str, Any],
        entries: List[Dict[str, Any]]
    ) -> str:
        """
        Generate visualization for a specific habit.
        
        Args:
            habit_name: Name of the habit
            habit_data: Habit statistics
            entries: Journal entries for context
            
        Returns:
            SVG string of the habit visualization
        """
        try:
            # Extract habit performance over time
            habit_timeline = self._extract_habit_timeline(habit_name, entries)
            
            if not habit_timeline:
                return self._create_basic_habit_chart(habit_name, habit_data)
            
            # Create timeline chart
            fig = go.Figure()
            
            dates = [item['date'] for item in habit_timeline]
            values = [item['value'] for item in habit_timeline]
            
            # Add line chart
            fig.add_trace(go.Scatter(
                x=dates,
                y=values,
                mode='lines+markers',
                name=habit_name.title(),
                line=dict(color=self.colors['primary'], width=3),
                marker=dict(size=8, color=self.colors['primary'])
            ))
            
            # Update layout
            fig.update_layout(
                title=f"{habit_name.title()} Progress Over Time",
                xaxis_title="Date",
                yaxis_title="Performance",
                **self._get_base_layout()
            )
            
            return fig.to_image(format="svg", **self.chart_config).decode('utf-8')
            
        except Exception as e:
            logger.error(f"Error generating habit visualization: {e}")
            return self._create_basic_habit_chart(habit_name, habit_data)
    
    def generate_correlation_visualization(
        self,
        correlation: Dict[str, Any],
        entries: List[Dict[str, Any]]
    ) -> str:
        """
        Generate visualization for a specific correlation.
        
        Args:
            correlation: Correlation data
            entries: Journal entries for data points
            
        Returns:
            SVG string of the correlation visualization
        """
        try:
            df = self._entries_to_dataframe(entries)
            
            if df.empty:
                return self._create_basic_correlation_chart(correlation)
            
            # Extract the two variables
            if correlation.get('type') == 'metric_correlation':
                var1 = correlation.get('metric1')
                var2 = correlation.get('metric2')
            elif correlation.get('type') == 'habit_metric_correlation':
                var1 = correlation.get('habit')
                var2 = correlation.get('metric')
            else:
                return self._create_basic_correlation_chart(correlation)
            
            if var1 not in df.columns or var2 not in df.columns:
                return self._create_basic_correlation_chart(correlation)
            
            # Create scatter plot
            fig = go.Figure()
            
            x_data = df[var1].dropna()
            y_data = df[var2].dropna()
            
            # Find common indices
            common_idx = x_data.index.intersection(y_data.index)
            if len(common_idx) < 3:
                return self._create_basic_correlation_chart(correlation)
            
            x_values = x_data.loc[common_idx]
            y_values = y_data.loc[common_idx]
            
            # Add scatter plot
            fig.add_trace(go.Scatter(
                x=x_values,
                y=y_values,
                mode='markers',
                name='Data Points',
                marker=dict(
                    size=10,
                    color=self.colors['primary'],
                    opacity=0.7
                )
            ))
            
            # Add trend line
            z = np.polyfit(x_values, y_values, 1)
            p = np.poly1d(z)
            x_trend = np.linspace(x_values.min(), x_values.max(), 100)
            y_trend = p(x_trend)
            
            fig.add_trace(go.Scatter(
                x=x_trend,
                y=y_trend,
                mode='lines',
                name='Trend',
                line=dict(color=self.colors['accent'], width=2, dash='dash')
            ))
            
            # Update layout
            correlation_value = correlation.get('correlation', correlation.get('pearson_correlation', 0))
            fig.update_layout(
                title=f"Correlation: {var1.replace('_', ' ').title()} vs {var2.replace('_', ' ').title()}<br><sub>r = {correlation_value:.3f}</sub>",
                xaxis_title=var1.replace('_', ' ').title(),
                yaxis_title=var2.replace('_', ' ').title(),
                **self._get_base_layout()
            )
            
            return fig.to_image(format="svg", **self.chart_config).decode('utf-8')
            
        except Exception as e:
            logger.error(f"Error generating correlation visualization: {e}")
            return self._create_basic_correlation_chart(correlation)
    
    def _entries_to_dataframe(self, entries: List[Dict[str, Any]]) -> pd.DataFrame:
        """Convert entries to DataFrame for visualization."""
        data = []
        
        for entry in entries:
            try:
                # Parse metrics
                metrics = entry.get("metrics", {})
                if isinstance(metrics, str):
                    metrics = json.loads(metrics)
                
                # Create row
                row = {
                    "date": pd.to_datetime(entry.get("date", entry.get("created_at"))),
                    "entry_length": len(entry.get("text", "")),
                    **{k: v for k, v in metrics.items() if isinstance(v, (int, float))}
                }
                data.append(row)
                
            except Exception as e:
                logger.warning(f"Error processing entry for visualization: {e}")
                continue
        
        if not data:
            return pd.DataFrame()
        
        df = pd.DataFrame(data)
        df.set_index("date", inplace=True)
        df.sort_index(inplace=True)
        
        return df
    
    def _create_metrics_trends_chart(self, df: pd.DataFrame, stats: Dict[str, Any]) -> str:
        """Create metrics trends chart."""
        try:
            # Select top metrics to display
            quant_metrics = stats.get('quantitative_metrics', {})
            top_metrics = list(quant_metrics.keys())[:4]  # Top 4 metrics
            
            if not top_metrics:
                return self._create_basic_summary_chart(stats)
            
            # Create subplots
            fig = make_subplots(
                rows=2, cols=2,
                subplot_titles=[metric.replace('_', ' ').title() for metric in top_metrics],
                vertical_spacing=0.12,
                horizontal_spacing=0.1
            )
            
            colors = [self.colors['primary'], self.colors['secondary'], 
                     self.colors['accent'], self.colors['info']]
            
            for i, metric in enumerate(top_metrics):
                if metric in df.columns:
                    row = (i // 2) + 1
                    col = (i % 2) + 1
                    
                    series = df[metric].dropna()
                    if len(series) > 0:
                        fig.add_trace(
                            go.Scatter(
                                x=series.index,
                                y=series.values,
                                mode='lines+markers',
                                name=metric.replace('_', ' ').title(),
                                line=dict(color=colors[i], width=2),
                                marker=dict(size=6),
                                showlegend=False
                            ),
                            row=row, col=col
                        )
            
            fig.update_layout(
                title="Weekly Metrics Trends",
                **self._get_base_layout()
            )
            
            return fig.to_image(format="svg", **self.chart_config).decode('utf-8')
            
        except Exception as e:
            logger.error(f"Error creating metrics trends chart: {e}")
            return self._create_basic_summary_chart(stats)
    
    def _create_habits_performance_chart(self, stats: Dict[str, Any]) -> str:
        """Create habits performance chart."""
        try:
            habit_metrics = stats.get('habit_metrics', {})
            
            if not habit_metrics:
                return self._create_no_data_chart("No habit data available")
            
            # Extract habit names and completion rates
            habits = []
            completion_rates = []
            
            for habit, data in habit_metrics.items():
                if isinstance(data, dict) and 'completion_rate' in data:
                    habits.append(habit.replace('_', ' ').title())
                    completion_rates.append(data['completion_rate'] * 100)
            
            if not habits:
                return self._create_no_data_chart("No habit completion data")
            
            # Create horizontal bar chart
            fig = go.Figure()
            
            # Color bars based on performance
            colors = []
            for rate in completion_rates:
                if rate >= 80:
                    colors.append(self.colors['success'])
                elif rate >= 60:
                    colors.append(self.colors['secondary'])
                elif rate >= 40:
                    colors.append(self.colors['accent'])
                else:
                    colors.append(self.colors['warning'])
            
            fig.add_trace(go.Bar(
                y=habits,
                x=completion_rates,
                orientation='h',
                marker=dict(color=colors),
                text=[f"{rate:.0f}%" for rate in completion_rates],
                textposition='inside'
            ))
            
            fig.update_layout(
                title="Habit Completion Rates",
                xaxis_title="Completion Rate (%)",
                yaxis_title="Habits",
                **self._get_base_layout()
            )
            
            return fig.to_image(format="svg", **self.chart_config).decode('utf-8')
            
        except Exception as e:
            logger.error(f"Error creating habits performance chart: {e}")
            return self._create_no_data_chart("Error generating habits chart")
    
    def _create_correlations_heatmap(self, correlations: Dict[str, Any]) -> str:
        """Create correlations heatmap."""
        try:
            metric_corrs = correlations.get('metric_correlations', [])
            
            if not metric_corrs:
                return self._create_no_data_chart("No correlations found")
            
            # Extract correlation matrix data
            metrics = set()
            for corr in metric_corrs:
                metrics.add(corr.get('metric1', ''))
                metrics.add(corr.get('metric2', ''))
            
            metrics = list(metrics)
            if len(metrics) < 2:
                return self._create_no_data_chart("Insufficient correlation data")
            
            # Create correlation matrix
            matrix = np.zeros((len(metrics), len(metrics)))
            np.fill_diagonal(matrix, 1.0)  # Perfect correlation with self
            
            for corr in metric_corrs:
                metric1 = corr.get('metric1', '')
                metric2 = corr.get('metric2', '')
                corr_value = corr.get('pearson_correlation', 0)
                
                if metric1 in metrics and metric2 in metrics:
                    i = metrics.index(metric1)
                    j = metrics.index(metric2)
                    matrix[i][j] = corr_value
                    matrix[j][i] = corr_value  # Symmetric
            
            # Create heatmap
            fig = go.Figure(data=go.Heatmap(
                z=matrix,
                x=[m.replace('_', ' ').title() for m in metrics],
                y=[m.replace('_', ' ').title() for m in metrics],
                colorscale='RdBu',
                zmid=0,
                text=np.round(matrix, 2),
                texttemplate="%{text}",
                textfont={"size": 10},
                hoverongaps=False
            ))
            
            fig.update_layout(
                title="Metrics Correlations Heatmap",
                **self._get_base_layout()
            )
            
            return fig.to_image(format="svg", **self.chart_config).decode('utf-8')
            
        except Exception as e:
            logger.error(f"Error creating correlations heatmap: {e}")
            return self._create_no_data_chart("Error generating correlations chart")
    
    def _create_weekly_summary_chart(self, stats: Dict[str, Any]) -> str:
        """Create weekly summary dashboard."""
        try:
            summary = stats.get('summary', {})
            
            # Create gauge charts for key metrics
            fig = make_subplots(
                rows=2, cols=2,
                specs=[[{"type": "indicator"}, {"type": "indicator"}],
                       [{"type": "indicator"}, {"type": "indicator"}]],
                subplot_titles=["Data Quality", "Metrics Tracked", "Habits Tracked", "Overall Trend"]
            )
            
            # Data quality gauge
            quality_score = stats.get('data_quality_score', 0) * 100
            fig.add_trace(go.Indicator(
                mode="gauge+number",
                value=quality_score,
                domain={'x': [0, 1], 'y': [0, 1]},
                gauge={'axis': {'range': [None, 100]},
                       'bar': {'color': self.colors['primary']},
                       'steps': [{'range': [0, 50], 'color': self.colors['warning']},
                                {'range': [50, 80], 'color': self.colors['accent']},
                                {'range': [80, 100], 'color': self.colors['success']}]},
                title={'text': "Quality Score"}
            ), row=1, col=1)
            
            # Metrics count
            metrics_count = summary.get('total_metrics_tracked', 0)
            fig.add_trace(go.Indicator(
                mode="number",
                value=metrics_count,
                title={'text': "Metrics"},
                number={'font': {'size': 40, 'color': self.colors['secondary']}}
            ), row=1, col=2)
            
            # Habits count
            habits_count = summary.get('total_habits_tracked', 0)
            fig.add_trace(go.Indicator(
                mode="number",
                value=habits_count,
                title={'text': "Habits"},
                number={'font': {'size': 40, 'color': self.colors['accent']}}
            ), row=2, col=1)
            
            # Overall trend
            trend = summary.get('overall_trend', 'stable')
            trend_color = {
                'improving': self.colors['success'],
                'stable': self.colors['neutral'],
                'declining': self.colors['warning']
            }.get(trend, self.colors['neutral'])
            
            fig.add_trace(go.Indicator(
                mode="number+delta",
                value=1,
                title={'text': "Trend"},
                number={'font': {'size': 20, 'color': trend_color}},
                delta={'reference': 0, 'valueformat': trend.title()}
            ), row=2, col=2)
            
            fig.update_layout(
                title="Weekly Summary Dashboard",
                **self._get_base_layout()
            )
            
            return fig.to_image(format="svg", **self.chart_config).decode('utf-8')
            
        except Exception as e:
            logger.error(f"Error creating weekly summary chart: {e}")
            return self._create_basic_summary_chart(stats)
    
    def _create_wellness_trends_chart(self, df: pd.DataFrame) -> str:
        """Create wellness trends chart focusing on mood, energy, stress."""
        try:
            wellness_metrics = ['mood_score', 'energy_level', 'stress_level']
            available_metrics = [m for m in wellness_metrics if m in df.columns]
            
            if not available_metrics:
                return self._create_no_data_chart("No wellness data available")
            
            fig = go.Figure()
            
            colors = [self.colors['primary'], self.colors['secondary'], self.colors['warning']]
            
            for i, metric in enumerate(available_metrics):
                series = df[metric].dropna()
                if len(series) > 0:
                    fig.add_trace(go.Scatter(
                        x=series.index,
                        y=series.values,
                        mode='lines+markers',
                        name=metric.replace('_', ' ').title(),
                        line=dict(color=colors[i % len(colors)], width=2),
                        marker=dict(size=6)
                    ))
            
            fig.update_layout(
                title="Wellness Trends",
                xaxis_title="Date",
                yaxis_title="Score (1-10)",
                **self._get_base_layout()
            )
            
            return fig.to_image(format="svg", **self.chart_config).decode('utf-8')
            
        except Exception as e:
            logger.error(f"Error creating wellness trends chart: {e}")
            return self._create_no_data_chart("Error generating wellness chart")
    
    def _extract_habit_timeline(self, habit_name: str, entries: List[Dict[str, Any]]) -> List[Dict]:
        """Extract habit performance timeline from entries."""
        timeline = []
        
        for entry in entries:
            try:
                date = pd.to_datetime(entry.get("date", entry.get("created_at")))
                
                # Check qualitative habits
                qual_habits = entry.get("qualitative_habits", [])
                if isinstance(qual_habits, str):
                    qual_habits = json.loads(qual_habits)
                
                for habit in qual_habits:
                    if isinstance(habit, dict) and habit.get("name") == habit_name:
                        value = habit.get("confidence", 1.0) if habit.get("completed", False) else 0
                        timeline.append({"date": date, "value": value})
                        break
                        
            except Exception as e:
                logger.warning(f"Error extracting habit timeline: {e}")
                continue
        
        return sorted(timeline, key=lambda x: x["date"])
    
    def _get_base_layout(self) -> Dict[str, Any]:
        """Get base layout configuration for all charts."""
        return {
            'font': dict(family=self.chart_config['font_family'], size=self.chart_config['font_size']),
            'plot_bgcolor': 'white',
            'paper_bgcolor': 'white',
            'margin': self.chart_config['margin'],
            'showlegend': True,
            'legend': dict(orientation="h", yanchor="bottom", y=1.02, xanchor="right", x=1)
        }
    
    def _create_basic_summary_chart(self, stats: Dict[str, Any]) -> str:
        """Create basic summary chart when detailed data is unavailable."""
        try:
            period = stats.get('period', {})
            entries_count = period.get('entries_count', 0)
            total_days = period.get('total_days', 0)
            
            fig = go.Figure()
            
            # Simple bar chart of basic stats
            categories = ['Journal Entries', 'Active Days', 'Metrics Tracked', 'Habits Tracked']
            values = [
                entries_count,
                total_days,
                len(stats.get('quantitative_metrics', {})),
                len(stats.get('habit_metrics', {}))
            ]
            
            fig.add_trace(go.Bar(
                x=categories,
                y=values,
                marker=dict(color=[self.colors['primary'], self.colors['secondary'], 
                                 self.colors['accent'], self.colors['info']])
            ))
            
            fig.update_layout(
                title="Weekly Activity Summary",
                yaxis_title="Count",
                **self._get_base_layout()
            )
            
            return fig.to_image(format="svg", **self.chart_config).decode('utf-8')
            
        except Exception as e:
            logger.error(f"Error creating basic summary chart: {e}")
            return self._create_no_data_chart("Summary data unavailable")
    
    def _create_basic_habit_chart(self, habit_name: str, habit_data: Dict[str, Any]) -> str:
        """Create basic habit chart when timeline data is unavailable."""
        try:
            completion_rate = habit_data.get('completion_rate', 0) * 100
            
            fig = go.Figure(go.Indicator(
                mode="gauge+number+delta",
                value=completion_rate,
                domain={'x': [0, 1], 'y': [0, 1]},
                title={'text': f"{habit_name.title()} Completion Rate"},
                delta={'reference': 80},
                gauge={'axis': {'range': [None, 100]},
                       'bar': {'color': self.colors['primary']},
                       'steps': [{'range': [0, 50], 'color': self.colors['warning']},
                                {'range': [50, 80], 'color': self.colors['accent']},
                                {'range': [80, 100], 'color': self.colors['success']}],
                       'threshold': {'line': {'color': "red", 'width': 4},
                                   'thickness': 0.75, 'value': 90}}
            ))
            
            fig.update_layout(**self._get_base_layout())
            
            return fig.to_image(format="svg", **self.chart_config).decode('utf-8')
            
        except Exception as e:
            logger.error(f"Error creating basic habit chart: {e}")
            return self._create_no_data_chart(f"Unable to visualize {habit_name}")
    
    def _create_basic_correlation_chart(self, correlation: Dict[str, Any]) -> str:
        """Create basic correlation chart when data is unavailable."""
        try:
            corr_value = correlation.get('correlation', correlation.get('pearson_correlation', 0))
            
            fig = go.Figure(go.Indicator(
                mode="number+gauge",
                value=corr_value,
                domain={'x': [0, 1], 'y': [0, 1]},
                title={'text': "Correlation Strength"},
                gauge={'axis': {'range': [-1, 1]},
                       'bar': {'color': self.colors['primary']},
                       'steps': [{'range': [-1, -0.3], 'color': self.colors['warning']},
                                {'range': [-0.3, 0.3], 'color': self.colors['neutral']},
                                {'range': [0.3, 1], 'color': self.colors['success']}]}
            ))
            
            fig.update_layout(**self._get_base_layout())
            
            return fig.to_image(format="svg", **self.chart_config).decode('utf-8')
            
        except Exception as e:
            logger.error(f"Error creating basic correlation chart: {e}")
            return self._create_no_data_chart("Correlation visualization unavailable")
    
    def _create_no_data_chart(self, message: str) -> str:
        """Create a placeholder chart when no data is available."""
        fig = go.Figure()
        
        fig.add_annotation(
            text=message,
            xref="paper", yref="paper",
            x=0.5, y=0.5,
            xanchor='center', yanchor='middle',
            font=dict(size=16, color=self.colors['neutral'])
        )
        
        fig.update_layout(
            title="Data Visualization",
            xaxis={'visible': False},
            yaxis={'visible': False},
            **self._get_base_layout()
        )
        
        return fig.to_image(format="svg", **self.chart_config).decode('utf-8')
    
    def _get_fallback_visualizations(self) -> Dict[str, str]:
        """Return fallback visualizations when generation fails."""
        return {
            'weekly_summary': self._create_no_data_chart("Visualization temporarily unavailable"),
            'metrics_trends': self._create_no_data_chart("Metrics data not available"),
            'habits_performance': self._create_no_data_chart("Habits data not available")
        }


# Global service instance
visualization_service = VisualizationService()
