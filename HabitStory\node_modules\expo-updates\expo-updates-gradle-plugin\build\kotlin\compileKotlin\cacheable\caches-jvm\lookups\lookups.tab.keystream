  AndroidComponentsExtension com.android.build.api.variant  Sources com.android.build.api.variant  VariantSelector com.android.build.api.variant  
onVariants 8com.android.build.api.variant.AndroidComponentsExtension  selector 8com.android.build.api.variant.AndroidComponentsExtension  sources 'com.android.build.api.variant.Component  name /com.android.build.api.variant.ComponentIdentity  Layered /com.android.build.api.variant.SourceDirectories  addGeneratedSourceDirectory /com.android.build.api.variant.SourceDirectories  addGeneratedSourceDirectory 7com.android.build.api.variant.SourceDirectories.Layered  assets %com.android.build.api.variant.Sources  all -com.android.build.api.variant.VariantSelector  ReactExtension com.facebook.react  	Companion !com.facebook.react.ReactExtension  debuggableVariants !com.facebook.react.ReactExtension  	entryFile !com.facebook.react.ReactExtension  nodeExecutableAndArgs !com.facebook.react.ReactExtension  root !com.facebook.react.ReactExtension  AndroidComponentsExtension expo.modules.updates  Boolean expo.modules.updates  ByteArrayOutputStream expo.modules.updates  CreateUpdatesResourcesTask expo.modules.updates  DefaultTask expo.modules.updates  DirectoryProperty expo.modules.updates  ExecOperations expo.modules.updates  ExpoUpdatesPlugin expo.modules.updates  File expo.modules.updates  Inject expo.modules.updates  Input expo.modules.updates  ListProperty expo.modules.updates  Locale expo.modules.updates  
LoggerFactory expo.modules.updates  Os expo.modules.updates  OutputDirectory expo.modules.updates  Plugin expo.modules.updates  Project expo.modules.updates  Property expo.modules.updates  ReactExtension expo.modules.updates  String expo.modules.updates  System expo.modules.updates  
TaskAction expo.modules.updates  any expo.modules.updates  apply expo.modules.updates  assetDir expo.modules.updates  debuggableVariant expo.modules.updates  deleteRecursively expo.modules.updates  detectedEntryFile expo.modules.updates  	entryFile expo.modules.updates  equals expo.modules.updates  getValue expo.modules.updates  invoke expo.modules.updates  isLowerCase expo.modules.updates  isNativeDebuggingEnabled expo.modules.updates  java expo.modules.updates  lazy expo.modules.updates  listOf expo.modules.updates  logger expo.modules.updates  
mutableListOf expo.modules.updates  nodeExecutableAndArgs expo.modules.updates  projectRoot expo.modules.updates  provideDelegate expo.modules.updates  replaceFirstChar expo.modules.updates  run expo.modules.updates  	titlecase expo.modules.updates  toTypedArray expo.modules.updates  trim expo.modules.updates  AndroidComponentsExtension &expo.modules.updates.ExpoUpdatesPlugin  Boolean &expo.modules.updates.ExpoUpdatesPlugin  ByteArrayOutputStream &expo.modules.updates.ExpoUpdatesPlugin  	Companion &expo.modules.updates.ExpoUpdatesPlugin  CreateUpdatesResourcesTask &expo.modules.updates.ExpoUpdatesPlugin  DefaultTask &expo.modules.updates.ExpoUpdatesPlugin  DirectoryProperty &expo.modules.updates.ExpoUpdatesPlugin  ExecOperations &expo.modules.updates.ExpoUpdatesPlugin  ExpoUpdatesPlugin &expo.modules.updates.ExpoUpdatesPlugin  Inject &expo.modules.updates.ExpoUpdatesPlugin  Input &expo.modules.updates.ExpoUpdatesPlugin  ListProperty &expo.modules.updates.ExpoUpdatesPlugin  Locale &expo.modules.updates.ExpoUpdatesPlugin  
LoggerFactory &expo.modules.updates.ExpoUpdatesPlugin  Os &expo.modules.updates.ExpoUpdatesPlugin  OutputDirectory &expo.modules.updates.ExpoUpdatesPlugin  Project &expo.modules.updates.ExpoUpdatesPlugin  Property &expo.modules.updates.ExpoUpdatesPlugin  ReactExtension &expo.modules.updates.ExpoUpdatesPlugin  String &expo.modules.updates.ExpoUpdatesPlugin  
TaskAction &expo.modules.updates.ExpoUpdatesPlugin  any &expo.modules.updates.ExpoUpdatesPlugin  apply &expo.modules.updates.ExpoUpdatesPlugin  assetDir &expo.modules.updates.ExpoUpdatesPlugin  debuggableVariant &expo.modules.updates.ExpoUpdatesPlugin  deleteRecursively &expo.modules.updates.ExpoUpdatesPlugin  detectedEntryFile &expo.modules.updates.ExpoUpdatesPlugin  	entryFile &expo.modules.updates.ExpoUpdatesPlugin  equals &expo.modules.updates.ExpoUpdatesPlugin  getValue &expo.modules.updates.ExpoUpdatesPlugin  invoke &expo.modules.updates.ExpoUpdatesPlugin  isLowerCase &expo.modules.updates.ExpoUpdatesPlugin  isNativeDebuggingEnabled &expo.modules.updates.ExpoUpdatesPlugin  java &expo.modules.updates.ExpoUpdatesPlugin  lazy &expo.modules.updates.ExpoUpdatesPlugin  listOf &expo.modules.updates.ExpoUpdatesPlugin  logger &expo.modules.updates.ExpoUpdatesPlugin  
mutableListOf &expo.modules.updates.ExpoUpdatesPlugin  nodeExecutableAndArgs &expo.modules.updates.ExpoUpdatesPlugin  projectRoot &expo.modules.updates.ExpoUpdatesPlugin  provideDelegate &expo.modules.updates.ExpoUpdatesPlugin  replaceFirstChar &expo.modules.updates.ExpoUpdatesPlugin  run &expo.modules.updates.ExpoUpdatesPlugin  	titlecase &expo.modules.updates.ExpoUpdatesPlugin  toTypedArray &expo.modules.updates.ExpoUpdatesPlugin  trim &expo.modules.updates.ExpoUpdatesPlugin  AndroidComponentsExtension 0expo.modules.updates.ExpoUpdatesPlugin.Companion  ByteArrayOutputStream 0expo.modules.updates.ExpoUpdatesPlugin.Companion  CreateUpdatesResourcesTask 0expo.modules.updates.ExpoUpdatesPlugin.Companion  ExpoUpdatesPlugin 0expo.modules.updates.ExpoUpdatesPlugin.Companion  Locale 0expo.modules.updates.ExpoUpdatesPlugin.Companion  
LoggerFactory 0expo.modules.updates.ExpoUpdatesPlugin.Companion  Os 0expo.modules.updates.ExpoUpdatesPlugin.Companion  ReactExtension 0expo.modules.updates.ExpoUpdatesPlugin.Companion  String 0expo.modules.updates.ExpoUpdatesPlugin.Companion  any 0expo.modules.updates.ExpoUpdatesPlugin.Companion  apply 0expo.modules.updates.ExpoUpdatesPlugin.Companion  assetDir 0expo.modules.updates.ExpoUpdatesPlugin.Companion  debuggableVariant 0expo.modules.updates.ExpoUpdatesPlugin.Companion  deleteRecursively 0expo.modules.updates.ExpoUpdatesPlugin.Companion  detectedEntryFile 0expo.modules.updates.ExpoUpdatesPlugin.Companion  	entryFile 0expo.modules.updates.ExpoUpdatesPlugin.Companion  equals 0expo.modules.updates.ExpoUpdatesPlugin.Companion  getValue 0expo.modules.updates.ExpoUpdatesPlugin.Companion  invoke 0expo.modules.updates.ExpoUpdatesPlugin.Companion  isLowerCase 0expo.modules.updates.ExpoUpdatesPlugin.Companion  isNativeDebuggingEnabled 0expo.modules.updates.ExpoUpdatesPlugin.Companion  java 0expo.modules.updates.ExpoUpdatesPlugin.Companion  lazy 0expo.modules.updates.ExpoUpdatesPlugin.Companion  listOf 0expo.modules.updates.ExpoUpdatesPlugin.Companion  logger 0expo.modules.updates.ExpoUpdatesPlugin.Companion  
mutableListOf 0expo.modules.updates.ExpoUpdatesPlugin.Companion  nodeExecutableAndArgs 0expo.modules.updates.ExpoUpdatesPlugin.Companion  projectRoot 0expo.modules.updates.ExpoUpdatesPlugin.Companion  provideDelegate 0expo.modules.updates.ExpoUpdatesPlugin.Companion  replaceFirstChar 0expo.modules.updates.ExpoUpdatesPlugin.Companion  run 0expo.modules.updates.ExpoUpdatesPlugin.Companion  	titlecase 0expo.modules.updates.ExpoUpdatesPlugin.Companion  toTypedArray 0expo.modules.updates.ExpoUpdatesPlugin.Companion  trim 0expo.modules.updates.ExpoUpdatesPlugin.Companion  ByteArrayOutputStream Aexpo.modules.updates.ExpoUpdatesPlugin.CreateUpdatesResourcesTask  Os Aexpo.modules.updates.ExpoUpdatesPlugin.CreateUpdatesResourcesTask  String Aexpo.modules.updates.ExpoUpdatesPlugin.CreateUpdatesResourcesTask  apply Aexpo.modules.updates.ExpoUpdatesPlugin.CreateUpdatesResourcesTask  assetDir Aexpo.modules.updates.ExpoUpdatesPlugin.CreateUpdatesResourcesTask  debuggableVariant Aexpo.modules.updates.ExpoUpdatesPlugin.CreateUpdatesResourcesTask  deleteRecursively Aexpo.modules.updates.ExpoUpdatesPlugin.CreateUpdatesResourcesTask  	entryFile Aexpo.modules.updates.ExpoUpdatesPlugin.CreateUpdatesResourcesTask  execOperations Aexpo.modules.updates.ExpoUpdatesPlugin.CreateUpdatesResourcesTask  getExpoUpdatesPackageDir Aexpo.modules.updates.ExpoUpdatesPlugin.CreateUpdatesResourcesTask  invoke Aexpo.modules.updates.ExpoUpdatesPlugin.CreateUpdatesResourcesTask  listOf Aexpo.modules.updates.ExpoUpdatesPlugin.CreateUpdatesResourcesTask  
mutableListOf Aexpo.modules.updates.ExpoUpdatesPlugin.CreateUpdatesResourcesTask  nodeExecutableAndArgs Aexpo.modules.updates.ExpoUpdatesPlugin.CreateUpdatesResourcesTask  projectRoot Aexpo.modules.updates.ExpoUpdatesPlugin.CreateUpdatesResourcesTask  toTypedArray Aexpo.modules.updates.ExpoUpdatesPlugin.CreateUpdatesResourcesTask  trim Aexpo.modules.updates.ExpoUpdatesPlugin.CreateUpdatesResourcesTask  ByteArrayOutputStream java.io  File java.io  toByteArray java.io.ByteArrayOutputStream  exists java.io.File  mkdirs java.io.File  
parentFile java.io.File  toPath java.io.File  Class 	java.lang  getenv java.lang.System  Locale 	java.util  ROOT java.util.Locale  Inject javax.inject  Array kotlin  CharSequence kotlin  	Function0 kotlin  	Function1 kotlin  Lazy kotlin  Nothing kotlin  String kotlin  apply kotlin  getValue kotlin  lazy kotlin  run kotlin  equals 
kotlin.Any  toString 
kotlin.Any  isLowerCase kotlin.Char  	titlecase kotlin.Char  toString kotlin.Char  getValue kotlin.Lazy  provideDelegate kotlin.Lazy  	Companion 
kotlin.String  equals 
kotlin.String  invoke 
kotlin.String  replaceFirstChar 
kotlin.String  trim 
kotlin.String  invoke kotlin.String.Companion  List kotlin.collections  MutableList kotlin.collections  any kotlin.collections  getValue kotlin.collections  listOf kotlin.collections  
mutableListOf kotlin.collections  toTypedArray kotlin.collections  toTypedArray kotlin.collections.List  add kotlin.collections.MutableList  addAll kotlin.collections.MutableList  apply kotlin.collections.MutableList  assetDir kotlin.collections.MutableList  debuggableVariant kotlin.collections.MutableList  	entryFile kotlin.collections.MutableList  nodeExecutableAndArgs kotlin.collections.MutableList  projectRoot kotlin.collections.MutableList  toTypedArray kotlin.collections.MutableList  deleteRecursively 	kotlin.io  java 
kotlin.jvm  
KProperty1 kotlin.reflect  java kotlin.reflect.KClass  any kotlin.sequences  String kotlin.text  any kotlin.text  equals kotlin.text  isLowerCase kotlin.text  replaceFirstChar kotlin.text   replaceFirstCharWithCharSequence kotlin.text  	titlecase kotlin.text  trim kotlin.text  Os 'org.apache.tools.ant.taskdefs.condition  FAMILY_WINDOWS *org.apache.tools.ant.taskdefs.condition.Os  isFamily *org.apache.tools.ant.taskdefs.condition.Os  Action org.gradle.api  DefaultTask org.gradle.api  Plugin org.gradle.api  Project org.gradle.api  <SAM-CONSTRUCTOR> org.gradle.api.Action  ByteArrayOutputStream org.gradle.api.DefaultTask  Os org.gradle.api.DefaultTask  String org.gradle.api.DefaultTask  apply org.gradle.api.DefaultTask  assetDir org.gradle.api.DefaultTask  debuggableVariant org.gradle.api.DefaultTask  deleteRecursively org.gradle.api.DefaultTask  description org.gradle.api.DefaultTask  	entryFile org.gradle.api.DefaultTask  invoke org.gradle.api.DefaultTask  listOf org.gradle.api.DefaultTask  
mutableListOf org.gradle.api.DefaultTask  nodeExecutableAndArgs org.gradle.api.DefaultTask  projectRoot org.gradle.api.DefaultTask  toTypedArray org.gradle.api.DefaultTask  trim org.gradle.api.DefaultTask  
extensions org.gradle.api.Project  findProperty org.gradle.api.Project  
projectDir org.gradle.api.Project  rootProject org.gradle.api.Project  tasks org.gradle.api.Project  DirectoryProperty org.gradle.api.file  RegularFile org.gradle.api.file  RegularFileProperty org.gradle.api.file  asFile org.gradle.api.file.Directory  get %org.gradle.api.file.DirectoryProperty  asFile org.gradle.api.file.RegularFile  orNull 'org.gradle.api.file.RegularFileProperty  ByteArrayOutputStream $org.gradle.api.internal.AbstractTask  Os $org.gradle.api.internal.AbstractTask  String $org.gradle.api.internal.AbstractTask  apply $org.gradle.api.internal.AbstractTask  assetDir $org.gradle.api.internal.AbstractTask  debuggableVariant $org.gradle.api.internal.AbstractTask  deleteRecursively $org.gradle.api.internal.AbstractTask  description $org.gradle.api.internal.AbstractTask  	entryFile $org.gradle.api.internal.AbstractTask  invoke $org.gradle.api.internal.AbstractTask  listOf $org.gradle.api.internal.AbstractTask  
mutableListOf $org.gradle.api.internal.AbstractTask  nodeExecutableAndArgs $org.gradle.api.internal.AbstractTask  projectRoot $org.gradle.api.internal.AbstractTask  toTypedArray $org.gradle.api.internal.AbstractTask  trim $org.gradle.api.internal.AbstractTask  
findByType )org.gradle.api.plugins.ExtensionContainer  	getByType )org.gradle.api.plugins.ExtensionContainer  ListProperty org.gradle.api.provider  Property org.gradle.api.provider  get $org.gradle.api.provider.ListProperty  set $org.gradle.api.provider.ListProperty  get  org.gradle.api.provider.Property  set  org.gradle.api.provider.Property  orNull  org.gradle.api.provider.Provider  Input org.gradle.api.tasks  OutputDirectory org.gradle.api.tasks  
TaskAction org.gradle.api.tasks  register "org.gradle.api.tasks.TaskContainer  ExecOperations org.gradle.process  standardOutput org.gradle.process.BaseExecSpec  exec !org.gradle.process.ExecOperations  commandLine org.gradle.process.ExecSpec  
workingDir %org.gradle.process.ProcessForkOptions  
LoggerFactory 	org.slf4j  warn org.slf4j.Logger  	getLogger org.slf4j.LoggerFactory                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               