{"version": 3, "file": "NotificationChannelGroupManager.types.js", "sourceRoot": "", "sources": ["../src/NotificationChannelGroupManager.types.ts"], "names": [], "mappings": "", "sourcesContent": ["import { ProxyNativeModule } from 'expo-modules-core';\n\nimport { NotificationChannel } from './NotificationChannelManager.types';\n\n/**\n * An object which represents a notification channel group.\n * @platform android\n */\nexport interface NotificationChannelGroup {\n  id: string;\n  name: string | null;\n  description?: string | null;\n  isBlocked?: boolean;\n  channels: NotificationChannel[];\n}\n\n/**\n * An object which represents a notification channel group to be set.\n * @platform android\n */\nexport interface NotificationChannelGroupInput {\n  name: string | null;\n  description?: string | null;\n}\n\nexport interface NotificationChannelGroupManager extends ProxyNativeModule {\n  getNotificationChannelGroupsAsync?: () => Promise<NotificationChannelGroup[]>;\n  getNotificationChannelGroupAsync?: (groupId: string) => Promise<NotificationChannelGroup | null>;\n  setNotificationChannelGroupAsync?: (\n    groupId: string,\n    group: NotificationChannelGroupInput\n  ) => Promise<NotificationChannelGroup | null>;\n  deleteNotificationChannelGroupAsync?: (groupId: string) => Promise<void>;\n}\n"]}