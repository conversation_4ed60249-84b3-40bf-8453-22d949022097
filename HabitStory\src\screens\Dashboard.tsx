import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  StyleSheet,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';

import { useEntries } from '../hooks/useEntries';
import { useHabitTracking } from '../hooks/useHabitTracking';
import { formatDate } from '../lib/db';

interface DashboardStats {
  totalEntries: number;
  currentStreak: number;
  longestStreak: number;
  thisWeekEntries: number;
  topHabits: { habit_name: string; count: number; category: string }[];
}

const DashboardScreen: React.FC = () => {
  const [stats, setStats] = useState<DashboardStats>({
    totalEntries: 0,
    currentStreak: 0,
    longestStreak: 0,
    thisWeekEntries: 0,
    topHabits: [],
  });
  const [todayEntry, setTodayEntry] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);

  const navigation = useNavigation();
  const { getAllEntries, getEntryByDate } = useEntries();
  const { todayHabits, allHabits } = useHabitTracking();

  const today = formatDate(new Date());

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    setIsLoading(true);
    try {
      const [allEntries, todayEntryData] = await Promise.all([
        getAllEntries(),
        getEntryByDate(today),
      ]);

      // Calculate stats
      const totalEntries = allEntries.length;
      const currentStreak = calculateCurrentStreak(allEntries);
      const longestStreak = calculateLongestStreak(allEntries);
      const thisWeekEntries = calculateThisWeekEntries(allEntries);
      const topHabits = allHabits.slice(0, 5); // Top 5 habits

      setStats({
        totalEntries,
        currentStreak,
        longestStreak,
        thisWeekEntries,
        topHabits,
      });

      setTodayEntry(todayEntryData);
    } catch (error) {
      console.error('Error loading dashboard data:', error);
      Alert.alert('Error', 'Failed to load dashboard data');
    } finally {
      setIsLoading(false);
    }
  };

  const calculateCurrentStreak = (entries: any[]): number => {
    if (entries.length === 0) return 0;

    const sortedEntries = entries.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
    let streak = 0;
    const today = new Date();

    for (let i = 0; i < sortedEntries.length; i++) {
      const entryDate = new Date(sortedEntries[i].date);
      const expectedDate = new Date(today);
      expectedDate.setDate(today.getDate() - i);

      if (entryDate.toDateString() === expectedDate.toDateString()) {
        streak++;
      } else {
        break;
      }
    }

    return streak;
  };

  const calculateLongestStreak = (entries: any[]): number => {
    if (entries.length === 0) return 0;

    const sortedEntries = entries.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
    let longestStreak = 1;
    let currentStreak = 1;

    for (let i = 1; i < sortedEntries.length; i++) {
      const prevDate = new Date(sortedEntries[i - 1].date);
      const currentDate = new Date(sortedEntries[i].date);
      const diffTime = currentDate.getTime() - prevDate.getTime();
      const diffDays = diffTime / (1000 * 60 * 60 * 24);

      if (diffDays === 1) {
        currentStreak++;
        longestStreak = Math.max(longestStreak, currentStreak);
      } else {
        currentStreak = 1;
      }
    }

    return longestStreak;
  };

  const calculateThisWeekEntries = (entries: any[]): number => {
    const today = new Date();
    const startOfWeek = new Date(today);
    startOfWeek.setDate(today.getDate() - today.getDay());
    startOfWeek.setHours(0, 0, 0, 0);

    return entries.filter(entry => {
      const entryDate = new Date(entry.date);
      return entryDate >= startOfWeek;
    }).length;
  };

  const navigateToJournal = () => {
    navigation.navigate('Journal' as never);
  };

  const navigateToHistory = () => {
    navigation.navigate('History' as never);
  };

  const renderQuickStats = () => (
    <View style={styles.statsCard}>
      <Text style={styles.cardTitle}>📊 Quick Stats</Text>
      <View style={styles.statsRow}>
        <View style={styles.statItem}>
          <View style={[styles.statBadge, { backgroundColor: '#3b82f6' }]}>
            <Text style={styles.statNumber}>{stats.totalEntries}</Text>
          </View>
          <Text style={styles.statLabel}>Total Entries</Text>
        </View>
        <View style={styles.statItem}>
          <View style={[styles.statBadge, { backgroundColor: '#10b981' }]}>
            <Text style={styles.statNumber}>{stats.currentStreak}</Text>
          </View>
          <Text style={styles.statLabel}>Current Streak</Text>
        </View>
        <View style={styles.statItem}>
          <View style={[styles.statBadge, { backgroundColor: '#8b5cf6' }]}>
            <Text style={styles.statNumber}>{stats.longestStreak}</Text>
          </View>
          <Text style={styles.statLabel}>Best Streak</Text>
        </View>
        <View style={styles.statItem}>
          <View style={[styles.statBadge, { backgroundColor: '#f59e0b' }]}>
            <Text style={styles.statNumber}>{stats.thisWeekEntries}</Text>
          </View>
          <Text style={styles.statLabel}>This Week</Text>
        </View>
      </View>
    </View>
  );

  const renderTodayStatus = () => (
    <View style={styles.todayCard}>
      <Text style={styles.cardTitle}>📝 Today's Status</Text>
      {todayEntry ? (
        <View>
          <View style={styles.completedStatus}>
            <View style={styles.statusHeader}>
              <View style={styles.checkIcon}>
                <Text style={styles.checkText}>✓</Text>
              </View>
              <Text style={styles.completedText}>Journal entry completed!</Text>
            </View>
            <Text style={styles.entryPreview}>
              {todayEntry.text.length > 120
                ? `${todayEntry.text.substring(0, 120)}...`
                : todayEntry.text}
            </Text>
          </View>
          <TouchableOpacity
            onPress={navigateToJournal}
            style={styles.editButton}
          >
            <Text style={styles.buttonText}>✏️ Edit Today's Entry</Text>
          </TouchableOpacity>
        </View>
      ) : (
        <View>
          <View style={styles.pendingStatus}>
            <View style={styles.statusHeader}>
              <View style={styles.warningIcon}>
                <Text style={styles.warningText}>!</Text>
              </View>
              <Text style={styles.pendingText}>Ready to journal?</Text>
            </View>
            <Text style={styles.pendingDescription}>
              Start your day by writing about your thoughts, experiences, and goals.
            </Text>
          </View>
          <TouchableOpacity
            onPress={navigateToJournal}
            style={styles.writeButton}
          >
            <Text style={styles.buttonText}>🚀 Write Today's Entry</Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );

  const renderTopHabits = () => (
    <View style={styles.habitsCard}>
      <Text style={styles.cardTitle}>🎯 Top Habits</Text>
      {stats.topHabits.length > 0 ? (
        <View>
          {stats.topHabits.slice(0, 3).map((habit, index) => (
            <View key={habit.habit_name} style={styles.habitItem}>
              <View style={styles.habitContent}>
                <View style={styles.habitLeft}>
                  <View style={[
                    styles.medalIcon,
                    { backgroundColor: index === 0 ? '#eab308' : index === 1 ? '#9ca3af' : '#f59e0b' }
                  ]}>
                    <Text style={styles.medalText}>
                      {index === 0 ? '🥇' : index === 1 ? '🥈' : '🥉'}
                    </Text>
                  </View>
                  <View style={styles.habitInfo}>
                    <Text style={styles.habitName}>
                      {habit.habit_name}
                    </Text>
                    <Text style={styles.habitCategory}>
                      {habit.category}
                    </Text>
                  </View>
                </View>
                <View style={styles.habitCount}>
                  <Text style={styles.habitCountText}>{habit.count}</Text>
                </View>
              </View>
            </View>
          ))}
        </View>
      ) : (
        <View style={styles.emptyHabits}>
          <Text style={styles.emptyHabitsText}>
            Start journaling to track your habits! 📈
          </Text>
        </View>
      )}
    </View>
  );

  const renderQuickActions = () => (
    <View style={styles.actionsCard}>
      <Text style={styles.cardTitle}>⚡ Quick Actions</Text>
      <View>
        <TouchableOpacity
          onPress={navigateToHistory}
          style={styles.historyAction}
        >
          <View style={styles.actionContent}>
            <Text style={styles.actionIcon}>📚</Text>
            <View style={styles.actionText}>
              <Text style={styles.actionTitle}>View History</Text>
              <Text style={styles.actionSubtitle}>Browse your past entries</Text>
            </View>
          </View>
        </TouchableOpacity>

        <TouchableOpacity
          onPress={() => navigation.navigate('Reports' as never)}
          style={styles.reportsAction}
        >
          <View style={styles.actionContent}>
            <Text style={styles.actionIcon}>📊</Text>
            <View style={styles.actionText}>
              <Text style={styles.actionTitle}>Weekly Reports</Text>
              <Text style={styles.actionSubtitle}>Generate insights & summaries</Text>
            </View>
          </View>
        </TouchableOpacity>
      </View>
    </View>
  );

  if (isLoading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <View style={styles.loadingCard}>
            <ActivityIndicator size="large" color="#6366F1" />
            <Text style={styles.loadingTitle}>Loading dashboard...</Text>
            <Text style={styles.loadingSubtitle}>
              Preparing your personalized insights ✨
            </Text>
          </View>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.greeting}>
            Good {new Date().getHours() < 12 ? 'Morning' : new Date().getHours() < 18 ? 'Afternoon' : 'Evening'}! 👋
          </Text>
          <Text style={styles.date}>
            {new Date().toLocaleDateString('en-US', {
              weekday: 'long',
              year: 'numeric',
              month: 'long',
              day: 'numeric'
            })}
          </Text>
        </View>

        {/* Dashboard Blocks */}
        {renderQuickStats()}
        {renderTodayStatus()}
        {renderTopHabits()}
        {renderQuickActions()}

        {/* Bottom spacing */}
        <View style={styles.bottomSpacing} />
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 24,
  },
  loadingCard: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 32,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
    alignItems: 'center',
  },
  loadingTitle: {
    color: '#374151',
    marginTop: 16,
    fontSize: 18,
    fontWeight: '500',
  },
  loadingSubtitle: {
    color: '#6b7280',
    marginTop: 8,
    textAlign: 'center',
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  header: {
    marginBottom: 24,
    marginTop: 8,
  },
  greeting: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#374151',
    marginBottom: 8,
  },
  date: {
    color: '#6b7280',
    fontSize: 18,
  },
  statsCard: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 24,
    marginBottom: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
    borderWidth: 1,
    borderColor: '#e0f2fe',
  },
  cardTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#374151',
    marginBottom: 16,
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  statItem: {
    alignItems: 'center',
    flex: 1,
  },
  statBadge: {
    borderRadius: 16,
    padding: 12,
    marginBottom: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  statNumber: {
    fontSize: 20,
    fontWeight: 'bold',
    color: 'white',
  },
  statLabel: {
    fontSize: 12,
    fontWeight: '500',
    color: '#374151',
    textAlign: 'center',
  },
  todayCard: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 24,
    marginBottom: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
    borderWidth: 1,
    borderColor: '#e0e7ff',
  },
  completedStatus: {
    backgroundColor: '#dcfce7',
    borderRadius: 16,
    padding: 16,
    marginBottom: 16,
  },
  pendingStatus: {
    backgroundColor: '#fed7aa',
    borderRadius: 16,
    padding: 16,
    marginBottom: 16,
  },
  statusHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  checkIcon: {
    backgroundColor: '#10b981',
    borderRadius: 16,
    padding: 8,
    marginRight: 12,
  },
  warningIcon: {
    backgroundColor: '#f59e0b',
    borderRadius: 16,
    padding: 8,
    marginRight: 12,
  },
  checkText: {
    color: 'white',
    fontSize: 18,
  },
  warningText: {
    color: 'white',
    fontSize: 18,
  },
  completedText: {
    color: '#166534',
    fontWeight: 'bold',
    fontSize: 18,
  },
  pendingText: {
    color: '#92400e',
    fontWeight: 'bold',
    fontSize: 18,
  },
  entryPreview: {
    color: '#166534',
    fontSize: 14,
    lineHeight: 20,
  },
  pendingDescription: {
    color: '#92400e',
    fontSize: 14,
    lineHeight: 20,
  },
  editButton: {
    backgroundColor: '#3b82f6',
    borderRadius: 16,
    paddingVertical: 16,
    paddingHorizontal: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  writeButton: {
    backgroundColor: '#10b981',
    borderRadius: 16,
    paddingVertical: 16,
    paddingHorizontal: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  buttonText: {
    color: 'white',
    fontWeight: 'bold',
    textAlign: 'center',
    fontSize: 16,
  },
  bottomSpacing: {
    height: 16,
  },
  habitsCard: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 24,
    marginBottom: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
    borderWidth: 1,
    borderColor: '#f3e8ff',
  },
  habitItem: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
    borderWidth: 1,
    borderColor: '#f3f4f6',
  },
  habitContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  habitLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  medalIcon: {
    borderRadius: 16,
    padding: 8,
    marginRight: 12,
  },
  medalText: {
    color: 'white',
    fontWeight: 'bold',
  },
  habitInfo: {
    flex: 1,
  },
  habitName: {
    color: '#374151',
    fontWeight: '600',
    textTransform: 'capitalize',
  },
  habitCategory: {
    color: '#6b7280',
    fontSize: 12,
    textTransform: 'capitalize',
  },
  habitCount: {
    backgroundColor: '#6366f1',
    borderRadius: 16,
    paddingHorizontal: 12,
    paddingVertical: 4,
  },
  habitCountText: {
    color: 'white',
    fontWeight: 'bold',
  },
  emptyHabits: {
    backgroundColor: '#f3f4f6',
    borderRadius: 16,
    padding: 16,
  },
  emptyHabitsText: {
    color: '#6b7280',
    textAlign: 'center',
  },
  actionsCard: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 24,
    marginBottom: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
    borderWidth: 1,
    borderColor: '#fce7f3',
  },
  historyAction: {
    backgroundColor: '#8b5cf6',
    borderRadius: 16,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  reportsAction: {
    backgroundColor: '#3b82f6',
    borderRadius: 16,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  actionContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  actionIcon: {
    color: 'white',
    fontSize: 24,
    marginRight: 12,
  },
  actionText: {
    flex: 1,
  },
  actionTitle: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 18,
  },
  actionSubtitle: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 14,
  },
});

export default DashboardScreen;
