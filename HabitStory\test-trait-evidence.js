// Test script to verify trait evidence functionality
const { parseEntryText } = require('./src/lib/gemini');

async function testTraitEvidence() {
  console.log('🧪 Testing Trait Evidence Functionality...\n');

  const testEntry = `
Today was amazing! I woke up at 6:30 AM sharp and immediately did my 45-minute morning workout. 
I drank exactly 2.5 liters of water throughout the day and tracked every meal carefully. 
My productivity was through the roof - I completed 8 tasks on my to-do list and even had time 
to help my colleague with their project. I'm feeling incredibly optimistic about tomorrow's 
presentation. The detailed preparation I did today will definitely pay off!
  `.trim();

  try {
    console.log('📝 Test Entry:');
    console.log(testEntry);
    console.log('\n🤖 Analyzing with Gemini...\n');

    const analysis = await parseEntryText(testEntry);
    
    console.log('✅ Analysis Results:');
    console.log('📊 Habits:', analysis.habits);
    console.log('📈 Qualitative Habits:', analysis.qualitative_habits);
    console.log('📋 Metrics:', analysis.metrics);
    console.log('💭 Reflection:', analysis.reflection);
    
    console.log('\n🎯 User Traits Analysis:');
    console.log('🗣️  Tone:', analysis.user_traits.tone);
    console.log('✍️  Style:', analysis.user_traits.style);
    console.log('🧠 Traits:', analysis.user_traits.traits);
    
    console.log('\n🔍 NEW: Trait Evidence:');
    if (analysis.user_traits.trait_evidence) {
      Object.entries(analysis.user_traits.trait_evidence).forEach(([trait, evidence]) => {
        console.log(`   ${trait}:`);
        evidence.forEach(item => console.log(`     - ${item}`));
      });
    } else {
      console.log('   No trait evidence provided');
    }

    console.log('\n🎉 Test completed successfully!');
    console.log('✅ Trait evidence functionality is working correctly.');

  } catch (error) {
    console.error('❌ Test failed:', error);
    console.error('Error details:', error.message);
  }
}

// Run the test
testTraitEvidence();
