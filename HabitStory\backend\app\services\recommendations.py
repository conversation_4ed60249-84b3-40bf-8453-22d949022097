"""
Module 5: Recommendations Service
Generates personalized recommendations using Gemini based on user data.
"""

import json
import logging
from typing import Dict, List, Any, Optional

from .ai_client import unified_ai_service

logger = logging.getLogger(__name__)


class RecommendationsService:
    """Service for generating personalized recommendations and tips."""
    
    def __init__(self):
        self.ai_service = unified_ai_service
    
    async def generate_weekly_recommendations(
        self,
        user_id: str,
        entries: List[Dict[str, Any]],
        stats: Dict[str, Any],
        correlations: Dict[str, Any],
        user_traits: Dict[str, Any],
        previous_feedback: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        Generate personalized weekly recommendations.
        
        Args:
            user_id: User identifier for token tracking
            entries: List of journal entries
            stats: Weekly statistics from metrics service
            correlations: Correlation analysis results
            user_traits: User personality traits and communication style
            previous_feedback: Previous feedback to incorporate
            
        Returns:
            List of recommendation dictionaries
        """
        try:
            # Define function schema for structured recommendations
            functions = [{
                "name": "generate_recommendations",
                "description": "Generate personalized recommendations based on user data",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "recommendations": {
                            "type": "array",
                            "items": {
                                "type": "object",
                                "properties": {
                                    "category": {
                                        "type": "string",
                                        "enum": ["habits", "wellness", "productivity", "mindfulness", "social", "goals"]
                                    },
                                    "title": {"type": "string"},
                                    "description": {"type": "string"},
                                    "priority": {
                                        "type": "string",
                                        "enum": ["high", "medium", "low"]
                                    },
                                    "actionable_steps": {
                                        "type": "array",
                                        "items": {"type": "string"}
                                    },
                                    "reasoning": {"type": "string"},
                                    "expected_impact": {"type": "string"},
                                    "difficulty": {
                                        "type": "string",
                                        "enum": ["easy", "moderate", "challenging"]
                                    },
                                    "timeframe": {"type": "string"}
                                },
                                "required": ["category", "title", "description", "priority", "actionable_steps", "reasoning"]
                            },
                            "minItems": 3,
                            "maxItems": 8
                        }
                    },
                    "required": ["recommendations"]
                }
            }]
            
            # Create system prompt
            system_prompt = self._create_recommendations_system_prompt(user_traits, previous_feedback)
            
            # Prepare user data
            user_prompt = self._create_recommendations_user_prompt(entries, stats, correlations)
            
            # Generate recommendations
            response = await self.ai_service.generate_response(
                system_prompt=system_prompt,
                user_prompt=user_prompt,
                functions=functions,
                temperature=0.7,
                max_tokens=2000,
                user_id=user_id
            )
            
            # Parse response - try JSON first, then fallback to text parsing
            try:
                # Try to parse as JSON (structured response)
                parsed_data = json.loads(response.content)
                if isinstance(parsed_data, dict) and "recommendations" in parsed_data:
                    recommendations = parsed_data.get("recommendations", [])
                    return self._validate_recommendations(recommendations)
            except (json.JSONDecodeError, KeyError):
                pass

            # Fallback to text parsing
            return await self._parse_recommendations_from_text(response.content)
            
        except Exception as e:
            logger.error(f"Error generating recommendations: {e}")
            return self._get_fallback_recommendations(user_traits)
    
    async def generate_habit_recommendations(
        self,
        user_id: str,
        habit_name: str,
        habit_data: Dict[str, Any],
        user_traits: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """
        Generate recommendations for improving a specific habit.
        
        Args:
            user_id: User identifier
            habit_name: Name of the habit
            habit_data: Statistics and data about the habit
            user_traits: User personality traits
            
        Returns:
            List of habit-specific recommendations
        """
        try:
            system_prompt = f"""You are an expert habit coach who provides personalized, actionable advice.

USER PROFILE:
- Communication tone: {user_traits.get('tone', 'informal')}
- Style: {user_traits.get('style', 'conversational')}
- Personality: {json.dumps(user_traits.get('traits', {}), indent=2)}

Generate 2-4 specific, actionable recommendations to help improve the habit '{habit_name}'.
Focus on practical steps that match their personality and communication style.

Return recommendations as a JSON array with this structure:
[
  {{
    "category": "habits",
    "title": "Recommendation title",
    "description": "Detailed description",
    "priority": "high/medium/low",
    "actionable_steps": ["step 1", "step 2"],
    "reasoning": "Why this recommendation",
    "difficulty": "easy/moderate/challenging",
    "timeframe": "when to implement"
  }}
]"""
            
            user_prompt = f"""Habit: {habit_name}
Current Performance: {json.dumps(habit_data, indent=2)}

Generate personalized recommendations to improve this habit."""
            
            response = await self.client.call_gemini(
                system=system_prompt,
                user=user_prompt,
                temperature=0.7,
                max_tokens=1200,
                user_id=user_id
            )
            
            return await self._parse_recommendations_from_text(response["response"])
            
        except Exception as e:
            logger.error(f"Error generating habit recommendations: {e}")
            return [{
                "category": "habits",
                "title": f"Improve {habit_name}",
                "description": f"Focus on consistency and small improvements with {habit_name}.",
                "priority": "medium",
                "actionable_steps": ["Set a specific time", "Start small", "Track progress"],
                "reasoning": "Consistency is key to habit formation",
                "difficulty": "moderate",
                "timeframe": "next week"
            }]
    
    async def generate_correlation_recommendations(
        self,
        user_id: str,
        correlation: Dict[str, Any],
        user_traits: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Generate a recommendation based on a specific correlation.
        
        Args:
            user_id: User identifier
            correlation: Specific correlation data
            user_traits: User personality traits
            
        Returns:
            Single recommendation based on the correlation
        """
        try:
            system_prompt = f"""You are a data-driven wellness coach who helps people leverage insights from their personal data.

USER PROFILE:
- Tone: {user_traits.get('tone', 'informal')}
- Style: {user_traits.get('style', 'conversational')}
- Personality: {json.dumps(user_traits.get('traits', {}), indent=2)}

Based on the correlation data provided, create ONE specific, actionable recommendation that helps the user leverage this insight.
Match their communication style and personality."""
            
            user_prompt = f"""Correlation Insight: {json.dumps(correlation, indent=2)}

Create a specific recommendation that helps the user take advantage of this correlation."""
            
            response = await self.client.call_gemini(
                system=system_prompt,
                user=user_prompt,
                temperature=0.6,
                max_tokens=600,
                user_id=user_id
            )
            
            # Create structured recommendation from response
            return {
                "category": "insights",
                "title": "Leverage Your Personal Pattern",
                "description": response["response"],
                "priority": "medium",
                "actionable_steps": ["Monitor the connection", "Experiment with timing", "Track results"],
                "reasoning": f"Based on correlation: {correlation.get('insight', 'Personal data pattern')}",
                "difficulty": "moderate",
                "timeframe": "next 1-2 weeks"
            }
            
        except Exception as e:
            logger.error(f"Error generating correlation recommendation: {e}")
            return {
                "category": "insights",
                "title": "Explore Your Patterns",
                "description": "Pay attention to how different aspects of your life connect and influence each other.",
                "priority": "low",
                "actionable_steps": ["Observe patterns", "Note connections", "Experiment mindfully"],
                "reasoning": "Understanding personal patterns leads to better decisions",
                "difficulty": "easy",
                "timeframe": "ongoing"
            }
    
    def _create_recommendations_system_prompt(
        self, 
        user_traits: Dict[str, Any], 
        previous_feedback: Optional[str] = None
    ) -> str:
        """Create the system prompt for recommendations generation."""
        tone = user_traits.get('tone', 'informal')
        style = user_traits.get('style', 'conversational')
        traits = user_traits.get('traits', {})
        
        feedback_context = ""
        if previous_feedback:
            feedback_context = f"\nPREVIOUS FEEDBACK TO INCORPORATE:\n{previous_feedback}\n"
        
        return f"""You are an expert life coach and wellness advisor who creates highly personalized, actionable recommendations based on personal data analysis.

USER COMMUNICATION PROFILE:
- Tone: {tone}
- Style: {style}
- Personality Traits: {json.dumps(traits, indent=2)}

{feedback_context}

RECOMMENDATION GUIDELINES:

1. **Personalization**:
   - Match their {tone} communication tone exactly
   - Use their {style} style throughout
   - Consider their personality traits when suggesting approaches
   - Make recommendations feel personally relevant

2. **Actionability**:
   - Provide specific, concrete steps they can take
   - Include realistic timeframes
   - Consider their current patterns and constraints
   - Suggest gradual, sustainable changes

3. **Data-Driven**:
   - Base recommendations on their actual data patterns
   - Reference specific insights from their statistics
   - Leverage correlations they've discovered
   - Address areas showing concerning trends

4. **Categories to Consider**:
   - **Habits**: Building, improving, or maintaining habits
   - **Wellness**: Physical and mental health improvements
   - **Productivity**: Time management and efficiency
   - **Mindfulness**: Self-awareness and reflection practices
   - **Social**: Relationship and social connection improvements
   - **Goals**: Achievement and progress strategies

5. **Priority Levels**:
   - **High**: Urgent issues or high-impact opportunities
   - **Medium**: Important improvements with good ROI
   - **Low**: Nice-to-have enhancements

6. **Difficulty Assessment**:
   - **Easy**: Can start immediately with minimal effort
   - **Moderate**: Requires some planning and commitment
   - **Challenging**: Significant lifestyle changes needed

Use the generate_recommendations function to return 3-8 personalized recommendations that will genuinely help improve their life based on their data."""
    
    def _create_recommendations_user_prompt(
        self,
        entries: List[Dict[str, Any]],
        stats: Dict[str, Any],
        correlations: Dict[str, Any]
    ) -> str:
        """Create the user data prompt for recommendations."""
        # Summarize key patterns and issues
        summary_parts = []
        
        # Stats summary
        quant_metrics = stats.get('quantitative_metrics', {})
        declining_metrics = []
        improving_metrics = []
        
        for metric, data in quant_metrics.items():
            if isinstance(data, dict):
                trend = data.get('trend', 'stable')
                if trend == 'declining':
                    declining_metrics.append(metric.replace('_', ' '))
                elif trend == 'improving':
                    improving_metrics.append(metric.replace('_', ' '))
        
        if declining_metrics:
            summary_parts.append(f"Declining areas: {', '.join(declining_metrics)}")
        if improving_metrics:
            summary_parts.append(f"Improving areas: {', '.join(improving_metrics)}")
        
        # Habit performance
        habit_metrics = stats.get('habit_metrics', {})
        low_performing_habits = []
        high_performing_habits = []
        
        for habit, data in habit_metrics.items():
            if isinstance(data, dict):
                completion_rate = data.get('completion_rate', 0)
                if completion_rate < 0.5:
                    low_performing_habits.append(habit)
                elif completion_rate > 0.8:
                    high_performing_habits.append(habit)
        
        if low_performing_habits:
            summary_parts.append(f"Struggling habits: {', '.join(low_performing_habits)}")
        if high_performing_habits:
            summary_parts.append(f"Strong habits: {', '.join(high_performing_habits)}")
        
        # Key insights
        insights = correlations.get('insights', [])
        if insights:
            key_insights = [insight.get('insight', '') for insight in insights[:3]]
            summary_parts.append(f"Key insights: {'; '.join(key_insights)}")
        
        # Recent entries themes
        recent_reflections = []
        for entry in entries[-3:]:  # Last 3 entries
            reflection = entry.get('reflection', '')
            if reflection:
                recent_reflections.append(reflection)
        
        if recent_reflections:
            summary_parts.append(f"Recent reflections: {'; '.join(recent_reflections)}")
        
        data_summary = "\n".join(summary_parts) if summary_parts else "Limited data available"
        
        return f"""USER DATA ANALYSIS:

{data_summary}

FULL STATISTICS:
{json.dumps(stats, indent=2)}

CORRELATIONS AND INSIGHTS:
{json.dumps(correlations, indent=2)}

Based on this comprehensive data analysis, generate personalized recommendations that will help the user improve their wellbeing, habits, and overall life satisfaction. Focus on actionable steps they can take based on their specific patterns and needs."""
    
    def _validate_recommendations(self, recommendations: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Validate and clean recommendations."""
        validated = []
        
        for rec in recommendations:
            if isinstance(rec, dict) and all(key in rec for key in ['title', 'description', 'category']):
                # Ensure required fields have defaults
                validated_rec = {
                    "category": rec.get("category", "general"),
                    "title": rec.get("title", "Improvement Suggestion"),
                    "description": rec.get("description", ""),
                    "priority": rec.get("priority", "medium"),
                    "actionable_steps": rec.get("actionable_steps", []),
                    "reasoning": rec.get("reasoning", "Based on your data patterns"),
                    "expected_impact": rec.get("expected_impact", "Positive improvement expected"),
                    "difficulty": rec.get("difficulty", "moderate"),
                    "timeframe": rec.get("timeframe", "next week")
                }
                validated.append(validated_rec)
        
        return validated[:8]  # Limit to 8 recommendations
    
    async def _parse_recommendations_from_text(self, text_response: str) -> List[Dict[str, Any]]:
        """Parse recommendations from text response."""
        try:
            # Try to find JSON in the response
            import re
            json_match = re.search(r'\[.*\]', text_response, re.DOTALL)
            if json_match:
                json_text = json_match.group()
                recommendations = json.loads(json_text)
                return self._validate_recommendations(recommendations)
        except:
            pass
        
        # Fallback: create basic recommendations from text
        return [{
            "category": "general",
            "title": "Personal Growth Opportunity",
            "description": text_response[:300] + "..." if len(text_response) > 300 else text_response,
            "priority": "medium",
            "actionable_steps": ["Review your patterns", "Make small changes", "Track progress"],
            "reasoning": "Based on your personal data analysis",
            "difficulty": "moderate",
            "timeframe": "next week"
        }]
    
    def _get_fallback_recommendations(self, user_traits: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate fallback recommendations when AI generation fails."""
        tone = user_traits.get('tone', 'informal')
        
        if tone == 'formal':
            return [
                {
                    "category": "mindfulness",
                    "title": "Enhance Self-Reflection Practice",
                    "description": "Continue your excellent journaling practice with more structured reflection techniques.",
                    "priority": "medium",
                    "actionable_steps": ["Set aside 10 minutes daily", "Use guided prompts", "Review weekly patterns"],
                    "reasoning": "Consistent reflection leads to better self-awareness",
                    "difficulty": "easy",
                    "timeframe": "this week"
                },
                {
                    "category": "habits",
                    "title": "Optimize Daily Routines",
                    "description": "Analyze your current routines and identify opportunities for improvement.",
                    "priority": "medium",
                    "actionable_steps": ["Document current routines", "Identify inefficiencies", "Test small changes"],
                    "reasoning": "Optimized routines improve overall life satisfaction",
                    "difficulty": "moderate",
                    "timeframe": "next two weeks"
                }
            ]
        else:
            return [
                {
                    "category": "wellness",
                    "title": "Keep Up the Great Work!",
                    "description": "You're doing amazing with your journaling - let's build on that momentum!",
                    "priority": "high",
                    "actionable_steps": ["Celebrate your consistency", "Try one new healthy habit", "Share your progress with someone"],
                    "reasoning": "You're already showing great self-awareness",
                    "difficulty": "easy",
                    "timeframe": "this week"
                },
                {
                    "category": "goals",
                    "title": "Set Your Next Challenge",
                    "description": "Ready to level up? Pick one area where you want to see improvement.",
                    "priority": "medium",
                    "actionable_steps": ["Choose one focus area", "Set a small, specific goal", "Track it for a week"],
                    "reasoning": "You're ready for the next step in your growth journey",
                    "difficulty": "moderate",
                    "timeframe": "next week"
                }
            ]


# Global service instance
recommendations_service = RecommendationsService()
